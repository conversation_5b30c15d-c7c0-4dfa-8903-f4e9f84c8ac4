
package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.NotePriorityConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiTalentNoteDetailExcelENVO implements Serializable {

    @ExcelProperty(value = "Candidate Name", index = 0)
    private String fullName;

    @ExcelProperty(value = "Contact Information", index = 1)
    private String additionalInfo;

    @ExcelProperty(value = "Contact Information", index = 1)
    private String noteType;

    @ExcelProperty(value = "Subject", index = 2)
    private String title;

    @ExcelProperty(value = "Priority", index = 3)
    @Convert(converter = NotePriorityConverter.class)
    private NotePriority priority;

    @ExcelProperty(value = "Note", index = 4)
    private String note;

    @ExcelProperty(value = "Job Search Status", index = 5)
    private String jobSearchStatusDisplay;

    @ExcelProperty(value = "Attendees", index = 6)
    private String attendees;

    @ExcelProperty(value = "Created By", index = 7)
    private String createdBy;

    @ExcelProperty(value = "Last Updated By", index = 8)
    private String lastModifiedBy;

    @ExcelProperty(value = "Created Time", index = 9)
    private String createdDateFormat;

    @ExcelProperty(value = "Last Updated At", index = 10)
    private String lastModifiedDateFormat;

    public String getNote() {
        if (StrUtil.isNotBlank(note)) {
            return HtmlUtil.cleanHtmlTag(note);
        }
        return note;
    }
}
