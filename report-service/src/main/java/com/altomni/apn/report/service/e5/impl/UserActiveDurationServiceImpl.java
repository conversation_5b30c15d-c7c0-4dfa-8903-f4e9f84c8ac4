package com.altomni.apn.report.service.e5.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.PlatformTypeEnum;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationNoteDetailExcelENVO;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationNoteDetailVO;
import com.altomni.apn.report.domain.vo.e5.UserActiveDurationDetailReportExcelVO;
import com.altomni.apn.report.domain.vo.e5.UserActiveDurationDetailReportVO;
import com.altomni.apn.report.dto.e5.UserAdoptionDetailReportSearchDTO;
import com.altomni.apn.report.service.e5.UserActiveDurationService;
import com.altomni.apn.report.util.ExcelUtil;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Accumulators.push;
import static com.mongodb.client.model.Aggregates.*;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Projections.*;

@Slf4j
@Service
public class UserActiveDurationServiceImpl implements UserActiveDurationService {

    private final MongoTemplate mongoTemplate;

    private static final long MILLISECONDS_TO_SECONDS = 1000L;

    @Autowired
    public UserActiveDurationServiceImpl(@Qualifier("statsMongoTemplate") MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public Map<Long, Long> getUsersByOnlineDuration(Long tenantId, String startDate, String endDate, Collection<Long> userIds, Integer durationThresholdInMinute) {
//        MongoDatabase database = mongoTemplate.getDb();
//
//        MongoCollection<Document> collection = database.getCollection("apn_online_user");
//
//        // 创建基本的 match 过滤条件（tenantId & date 必须匹配）
//        List<Bson> baseMatchFilters = new ArrayList<>(Arrays.asList(
//                eq("tenant_id", tenantId),
//                gte("date", startDate),
//                lte("date", endDate)
//        ));
//
//        // 如果 userIds 不为空，则添加 userId 过滤条件
//        if (userIds != null && !userIds.isEmpty()) {
//            baseMatchFilters.add(in("user_id", userIds));
//        }
//
//        List<Bson> pipeline = Arrays.asList(
//                //1. 过滤 `apn_online_user`
//                match(and(baseMatchFilters)),
//
//                project(fields(include("user_id", "online_detail"))),
//
//                //2. 关联 `crm_online_user`，按 `user_id` 关联
//                lookup("crm_online_user", "user_id", "user_id", "crm_data"),
//
//                //3. 确保 `apn_online_user` 没有匹配的 `crm_online_user` 时，仍然保留数据
//                unwind("$crm_data", new UnwindOptions().preserveNullAndEmptyArrays(true)),
//
//                //4. 过滤 `crm_online_user`，并考虑 `userIds` 是否存在
//                match(or(
//                        and(
//                                eq("tenant_id", tenantId),
//                                gte("date", startDate),
//                                lte("date", endDate),
//                                userIds == null || userIds.isEmpty() ? new Document() : in("user_id", userIds)
//                        ),
//                        and(
//                                eq("tenant_id", tenantId),
//                                gte("crm_data.date", startDate),
//                                lte("crm_data.date", endDate),
//                                userIds == null || userIds.isEmpty() ? new Document() : in("crm_data.user_id", userIds)
//                        )
//                )),
//
//                //5. 合并 `online_detail` 数据
//                project(fields(
//                        include("user_id"),
//                        computed("online_detail", new Document("$concatArrays", Arrays.asList(
//                                "$online_detail",
//                                new Document("$ifNull", Arrays.asList("$crm_data.online_detail", Collections.emptyList()))
//                        )))
//                )),
//
//                //6. 展开 `online_detail`
//                unwind("$online_detail"),
//
//                //7. 统一时间格式，去掉毫秒
//                addFields(new Field<>("online_detail.startTime",
//                        new Document("$dateFromParts", new Document()
//                                .append("year", new Document("$year", "$online_detail.startTime"))
//                                .append("month", new Document("$month", "$online_detail.startTime"))
//                                .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
//                                .append("hour", new Document("$hour", "$online_detail.startTime"))
//                                .append("minute", new Document("$minute", "$online_detail.startTime"))
//                                .append("second", new Document("$second", "$online_detail.startTime"))
//                        )
//                )),
//                addFields(new Field<>("online_detail.endTime",
//                        new Document("$dateFromParts", new Document()
//                                .append("year", new Document("$year", "$online_detail.endTime"))
//                                .append("month", new Document("$month", "$online_detail.endTime"))
//                                .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
//                                .append("hour", new Document("$hour", "$online_detail.endTime"))
//                                .append("minute", new Document("$minute", "$online_detail.endTime"))
//                                .append("second", new Document("$second", "$online_detail.endTime"))
//                        )
//                )),
//
//                //8. 重新按 `user_id` 进行聚合，并按 `startTime` 排序
//                group("$user_id", push("intervals", "$online_detail")),
//                addFields(new Field<>("intervals",
//                        new Document("$sortArray", new Document()
//                                .append("input", "$intervals")
//                                .append("sortBy", new Document("startTime", 1))
//                        )
//                )),
//
//                //9. 合并连续的时间段（如果 `endTime` 和下一个 `startTime` 仅差 1 秒，则合并）
//                addFields(new Field<>("merged_intervals",
//                        new Document("$reduce", new Document()
//                                .append("input", "$intervals")
//                                .append("initialValue", new ArrayList<>())
//                                .append("in", new Document("$concatArrays", Arrays.asList(
//                                        new Document("$cond", new Document()
//                                                .append("if", new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)))
//                                                .append("then", new Document("$let", new Document()
//                                                        .append("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
//                                                        .append("in", new Document("$cond", new Document()
//                                                                .append("if", new Document("$lte", Arrays.asList(
//                                                                        new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")),
//                                                                        1000 // 允许最多 1 秒的间隔合并
//                                                                )))
//                                                                .append("then", new Document("$concatArrays", Arrays.asList(
//                                                                        new Document("$slice", Arrays.asList("$$value", new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1)))),
//                                                                        Arrays.asList(new Document("startTime", "$$last.startTime")
//                                                                                .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime"))))
//                                                                )))
//                                                                .append("else", new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this"))))
//                                                        ))
//                                                )))
//                                                .append("else", Arrays.asList("$$this"))
//                                )
//                                ))
//                        )
//                )),
//
//                //10. 计算合并后所有时间段的 `total_duration`
//                addFields(new Field<>("total_duration",
//                        new Document("$sum", new Document("$map", new Document()
//                                .append("input", "$merged_intervals")
//                                .append("as", "interval")
//                                .append("in", new Document("$subtract", Arrays.asList("$$interval.endTime", "$$interval.startTime")))
//                        ))
//                )),
//
//                //11. 过滤掉 total_duration 小于 threshold 的数据
//                match(gte("total_duration", durationThresholdInMinute)),
//
//                //12. 只返回 `user_id` 和 `total_duration`
//                project(fields(
//                        excludeId(),
//                        Projections.computed("user_id", "$_id"),
//                        Projections.include("total_duration")
//                ))
//        );
//
//        // 执行 Aggregation
//        AggregateIterable<Document> result = collection.aggregate(pipeline);
//
//        // 转换结果为 Map<Long, Long>
//        return result.into(new ArrayList<>()).stream()
//                .collect(Collectors.toMap(
//                        doc -> doc.getLong("user_id"),
//                        doc -> doc.getLong("total_duration") / MILLISECONDS_TO_SECONDS
//                ));

        /**
        List<Bson> pipeline = new ArrayList<>();

        //1. 过滤 `apn_online_user` 数据
        pipeline.add(Aggregates.match(
                Filters.and(
                        Filters.gte("date", startDate),
                        Filters.lte("date", endDate),
                        Filters.eq("tenant_id", tenantId),
                        (CollectionUtils.isEmpty(userIds)) ? new Document() : Filters.in("user_id", userIds)
                )
        ));

        //2. 关联 `crm_online_user`
        pipeline.add(Aggregates.lookup("crm_online_user", "user_id", "user_id", "crm_data"));

        //3. 保留 `apn_online_user` 数据，即使 `crm_online_user` 没有匹配
        pipeline.add(Aggregates.unwind("$crm_data", new UnwindOptions().preserveNullAndEmptyArrays(true)));

        //4. 过滤 `crm_online_user` 数据
        pipeline.add(Aggregates.match(
                Filters.or(
                        // 过滤 `apn_online_user`
                        Filters.and(
                                Filters.eq("tenant_id", tenantId),
                                Filters.gte("date", startDate),
                                Filters.lte("date", endDate),
                                (CollectionUtils.isEmpty(userIds)) ? new Document() : Filters.in("user_id", userIds)
                        ),
                        // 过滤 `crm_online_user`
                        Filters.and(
                                Filters.eq("crm_data.tenant_id", tenantId),
                                Filters.gte("crm_data.date", startDate),
                                Filters.lte("crm_data.date", endDate),
                                (CollectionUtils.isEmpty(userIds)) ? new Document() : Filters.in("crm_data.user_id", userIds)
                        )
                )
        ));

        //5. 合并 `online_detail`
        pipeline.add(Aggregates.project(
                Projections.fields(
                        Projections.include("user_id"),
                        Projections.computed("online_detail",
                                new Document("$concatArrays", Arrays.asList("$online_detail",
                                        new Document("$ifNull", Arrays.asList("$crm_data.online_detail", new ArrayList<>()))))
                        )
                )
        ));

        //6. 展开 `online_detail`
        pipeline.add(Aggregates.unwind("$online_detail"));

        //7. 统一时间格式，忽略毫秒
        pipeline.add(Aggregates.addFields(new Field<>("online_detail.startTime",
                new Document("$dateFromParts", new Document("year", new Document("$year", "$online_detail.startTime"))
                        .append("month", new Document("$month", "$online_detail.startTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
                        .append("hour", new Document("$hour", "$online_detail.startTime"))
                        .append("minute", new Document("$minute", "$online_detail.startTime"))
                        .append("second", new Document("$second", "$online_detail.startTime"))))));

        pipeline.add(Aggregates.addFields(new Field<>("online_detail.endTime",
                new Document("$dateFromParts", new Document("year", new Document("$year", "$online_detail.endTime"))
                        .append("month", new Document("$month", "$online_detail.endTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
                        .append("hour", new Document("$hour", "$online_detail.endTime"))
                        .append("minute", new Document("$minute", "$online_detail.endTime"))
                        .append("second", new Document("$second", "$online_detail.endTime"))))));

        //8. 按 `user_id` 重新聚合并排序
        pipeline.add(Aggregates.group("$user_id", Accumulators.push("intervals", "$online_detail")));

        //9. 处理 `merged_intervals`（合并连续区间）
        pipeline.add(Aggregates.addFields(new Field<>("merged_intervals", new Document("$reduce", new Document("input", "$intervals")
                .append("initialValue", new ArrayList<>())
                .append("in", new Document("$concatArrays", Arrays.asList(
                        new Document("$cond", Arrays.asList(
                                new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)),
                                new Document("$let", new Document("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
                                        .append("in", new Document("$cond", Arrays.asList(
                                                new Document("$lte", Arrays.asList(new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")), 1000)),
                                                new Document("$concatArrays", Arrays.asList(
                                                        new Document("$slice", Arrays.asList("$$value", new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1)))),
                                                        Arrays.asList(new Document("startTime", "$$last.startTime")
                                                                .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime"))))
                                                )),
                                                new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this")))
                                        )))),
                                Arrays.asList("$$this")
                        ))
                )))))));

        //10. 计算 `total_duration`
        pipeline.add(Aggregates.addFields(new Field<>("total_duration", new Document("$sum",
                new Document("$map", new Document("input", "$merged_intervals")
                        .append("as", "interval")
                        .append("in", new Document("$subtract", Arrays.asList("$$interval.endTime", "$$interval.startTime"))))))));

        //11. 只在 `durationThresholdInMinute` 存在时进行过滤
        if (durationThresholdInMinute != null) {
            long durationThresholdMs = durationThresholdInMinute * 60 * 1000; // 转换为毫秒
            pipeline.add(Aggregates.match(Filters.lt("total_duration", durationThresholdMs)));
        }

        //12. 只返回 `user_id` 和 `total_duration`
        pipeline.add(Aggregates.project(Projections.fields(
                Projections.excludeId(),
//                Projections.include("user_id", "total_duration")
                Projections.computed("user_id", "$_id"),
                Projections.include("total_duration")
        )));



        // 运行聚合查询
        List<Document> results = mongoTemplate.getCollection("apn_online_user")
                .aggregate(pipeline)
                .into(new ArrayList<>());

        // 转换为 `Map<Long, Integer>`
        Map<Long, Long> durationMap = results.stream()
                .collect(Collectors.toMap(
                        doc -> doc.getLong("user_id"),
                        doc -> doc.getLong("total_duration") / MILLISECONDS_TO_SECONDS
                ));


        return durationMap;
         */

        MongoDatabase database = mongoTemplate.getDb();
        // 使用预先创建的 user_collection 作为 dummy 集合
        MongoCollection<Document> collection = database.getCollection("user_collection");

        List<Bson> pipeline = new ArrayList<>();

        // 1. 从 user_collection 中开始，如果传入了 userIds，则只匹配这些用户，否则返回所有
        if (!CollectionUtils.isEmpty(userIds)) {
            pipeline.add(match(in("user_id", userIds)));
        } else {
            pipeline.add(match(new Document()));
        }

        // 2. unionWith apn_online_user
        pipeline.add(unionWith("apn_online_user", Arrays.asList(
                match(and(
                        eq("tenant_id", tenantId),
                        gte("date", startDate),
                        lte("date", endDate),
                        CollectionUtils.isEmpty(userIds) ? new Document() : in("user_id", userIds)
                )),
                project(fields(include("user_id", "online_detail")))
        )));

        // 3. unionWith crm_online_user
        pipeline.add(unionWith("crm_online_user", Arrays.asList(
                match(and(
                        eq("tenant_id", tenantId),
                        gte("date", startDate),
                        lte("date", endDate),
                        CollectionUtils.isEmpty(userIds) ? new Document() : in("user_id", userIds)
                )),
                project(fields(include("user_id", "online_detail")))
        )));

        // 4. 展开 online_detail 数组
        pipeline.add(unwind("$online_detail", new UnwindOptions().preserveNullAndEmptyArrays(true)));

        // 4.1. 将 online_detail 为 null 时转换为空对象
        pipeline.add(project(Projections.fields(
                include("user_id"),
                computed("online_detail", new Document("$ifNull", Arrays.asList("$online_detail", new Document())))
        )));

        // 5. 统一 online_detail 时间格式，去掉毫秒（只保留到秒）
        pipeline.add(addFields(new Field<>("online_detail.startTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.startTime"))
                        .append("month", new Document("$month", "$online_detail.startTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
                        .append("hour", new Document("$hour", "$online_detail.startTime"))
                        .append("minute", new Document("$minute", "$online_detail.startTime"))
                        .append("second", new Document("$second", "$online_detail.startTime"))
                )
        )));
        pipeline.add(addFields(new Field<>("online_detail.endTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.endTime"))
                        .append("month", new Document("$month", "$online_detail.endTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
                        .append("hour", new Document("$hour", "$online_detail.endTime"))
                        .append("minute", new Document("$minute", "$online_detail.endTime"))
                        .append("second", new Document("$second", "$online_detail.endTime"))
                )
        )));

        // 6. 在聚合之前先排序，确保每个 user_id 的文档按 online_detail.startTime 升序排列
        pipeline.add(sort(new Document("user_id", 1).append("online_detail.startTime", 1)));

        // 7. 按 user_id 聚合，将所有 online_detail 收集到一个数组中（intervals）
        pipeline.add(Aggregates.group("$user_id", Accumulators.push("intervals", "$online_detail")));

        // 7.1. 过滤掉 intervals 中 startTime 为 null 的记录
        pipeline.add(addFields(new Field<>("intervals",
                new Document("$filter", new Document()
                        .append("input", "$intervals")
                        .append("as", "it")
                        .append("cond", new Document("$ne", Arrays.asList("$$it.startTime", null)))
                )
        )));

        // 8. 合并连续时间区间（允许相邻时间差 ≤ 1000 毫秒合并）
        pipeline.add(addFields(new Field<>("merged_intervals",
                new Document("$reduce", new Document()
                        .append("input", "$intervals")
                        .append("initialValue", new ArrayList<>())
                        .append("in", new Document("$concatArrays", Arrays.asList(
                                new Document("$cond", Arrays.asList(
                                        new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)),
                                        new Document("$let", new Document("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
                                                .append("in", new Document("$cond", Arrays.asList(
                                                        new Document("$lte", Arrays.asList(
                                                                new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")),
                                                                1000
                                                        )),
                                                        new Document("$concatArrays", Arrays.asList(
                                                                new Document("$slice", Arrays.asList("$$value", new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1)))),
                                                                Arrays.asList(new Document("startTime", "$$last.startTime")
                                                                        .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime")))
                                                                )
                                                        )),
                                                        new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this")))
                                                )))),
                                        Arrays.asList("$$this")
                                ))
                        )))
                ))));

        // 9. 计算 total_duration（毫秒单位）
//        pipeline.add(addFields(new Field<>("total_duration",
//                new Document("$sum", new Document("$map", new Document()
//                        .append("input", "$merged_intervals")
//                        .append("as", "interval")
//                        .append("in", new Document("$subtract", Arrays.asList("$$interval.endTime", "$$interval.startTime")))
//                ))
//        )));
        pipeline.add(addFields(new Field<>("total_duration",
                new Document("$sum", new Document("$map", new Document()
                        .append("input", "$merged_intervals")
                        .append("as", "interval")
                        .append("in", new Document("$add", Arrays.asList(
                                new Document("$subtract", Arrays.asList("$$interval.endTime", "$$interval.startTime")),
                                new Document("$cond", Arrays.asList(
                                        new Document("$eq", Arrays.asList(new Document("$second", "$$interval.endTime"), 59)),
                                        1000,
                                        0
                                ))
                        )))
                ))
        )));

        // 10. 如果 durationThresholdInMinute 不为 null，则过滤 total_duration 小于该阈值
        if (durationThresholdInMinute != null) {
            long durationThresholdMs = durationThresholdInMinute * 60 * 1000L;
            pipeline.add(match(Filters.lte("total_duration", durationThresholdMs)));
        }

        // 11. 只返回 user_id 和 total_duration
        pipeline.add(project(Projections.fields(
                Projections.computed("user_id", "$_id"),
                Projections.include("total_duration")
        )));

        // 执行 Aggregation
        List<Document> results = collection.aggregate(pipeline).allowDiskUse(true).into(new ArrayList<>());

        // 转换结果为 Map<Long, Long>
        Map<Long, Long> durationMap = results.stream()
                .collect(Collectors.toMap(
                        doc -> doc.get("user_id", Number.class).longValue(),
                        doc -> doc.get("total_duration", Number.class).longValue() / MILLISECONDS_TO_SECONDS
                ));

//        for (Bson stage : pipeline) {
//            String json = stage.toBsonDocument(Document.class, mongoTemplate.getDb().getCodecRegistry()).toJson();
//            System.out.println(json);
//        }

        return durationMap;
    }


    @Override
    public Map<Long, Long> getTopUsersByOnlineDuration(Long tenantId, String startDate, String endDate) {

        MongoDatabase database = mongoTemplate.getDb();
        MongoCollection<Document> apnCollection = database.getCollection("apn_online_user");

        List<Bson> pipeline = Arrays.asList(
                //1. 过滤 tenant_id & 日期
                match(and(
                        eq("tenant_id", tenantId),
                        gte("date", startDate),
                        lte("date", endDate)
                )),
                project(fields(include("user_id", "online_detail"))),

                //2. 使用 `$unionWith` 追加 `crm_online_user`
                unionWith("crm_online_user", Arrays.asList(
                        match(and(
                                eq("tenant_id", tenantId),
                                gte("date", startDate),
                                lte("date", endDate)
                        )),
                        project(fields(include("user_id", "online_detail")))
                )),

                //3. 展开 `online_detail`
                unwind("$online_detail"),

                //4. 规范化时间（去掉毫秒）
                addFields(new Field<>("online_detail.startTime",
                        new Document("$dateFromParts", new Document()
                                .append("year", new Document("$year", "$online_detail.startTime"))
                                .append("month", new Document("$month", "$online_detail.startTime"))
                                .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
                                .append("hour", new Document("$hour", "$online_detail.startTime"))
                                .append("minute", new Document("$minute", "$online_detail.startTime"))
                                .append("second", new Document("$second", "$online_detail.startTime"))
                        )
                )),
                addFields(new Field<>("online_detail.endTime",
                        new Document("$dateFromParts", new Document()
                                .append("year", new Document("$year", "$online_detail.endTime"))
                                .append("month", new Document("$month", "$online_detail.endTime"))
                                .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
                                .append("hour", new Document("$hour", "$online_detail.endTime"))
                                .append("minute", new Document("$minute", "$online_detail.endTime"))
                                .append("second", new Document("$second", "$online_detail.endTime"))
                        )
                )),

                //5. 按 `user_id` 进行聚合，合并时间区间
                group("$user_id", push("intervals", "$online_detail")),
                addFields(new Field<>("intervals",
                        new Document("$sortArray", new Document()
                                .append("input", "$intervals")
                                .append("sortBy", new Document("startTime", 1))
                        )
                )),

                //6. 合并连续时间区间（允许 1 秒以内的间隔）
                addFields(new Field<>("merged_intervals",
                        new Document("$reduce", new Document()
                                .append("input", "$intervals")
                                .append("initialValue", new ArrayList<>())
                                .append("in", new Document("$concatArrays", Arrays.asList(
                                        new Document("$cond", new Document()
                                                .append("if", new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)))
                                                .append("then", new Document("$let", new Document()
                                                        .append("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
                                                        .append("in", new Document("$cond", new Document()
                                                                // 允许最多 1 秒的间隔合并
                                                                .append("if", new Document("$lte", Arrays.asList(
                                                                        new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")),
                                                                        1000
                                                                )))
                                                                .append("then", new Document("$concatArrays", Arrays.asList(
                                                                        new Document("$slice", Arrays.asList("$$value", new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1)))),
                                                                        Arrays.asList(new Document("startTime", "$$last.startTime")
                                                                                .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime"))))
                                                                )))
                                                                .append("else", new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this"))))
                                                        ))
                                                )))
                                                .append("else", Arrays.asList("$$this"))
                                )
                                ))
                        )
                )),

                //7. 计算 `total_duration`
                addFields(new Field<>("total_duration",
                        new Document("$sum", new Document("$map", new Document()
                                .append("input", "$merged_intervals")
                                .append("as", "interval")
                                .append("in", new Document("$subtract", Arrays.asList("$$interval.endTime", "$$interval.startTime")))
                        ))
                )),

                //8. 按 `total_duration` 排序并取前 10 名
                sort(Sorts.descending("total_duration")),
                limit(10),

                //9. 只返回 user_id 和 total_duration
                project(fields(
                        excludeId(),
                        Projections.computed("user_id", "$_id"),
                        Projections.include("total_duration")
                ))
        );

        // 执行 Aggregation
        AggregateIterable<Document> result = apnCollection.aggregate(pipeline).allowDiskUse(true);

        // 转换结果为 Map<Long, Integer>
        return result.into(new ArrayList<>()).stream()
                .collect(Collectors.toMap(
                        doc -> doc.getLong("user_id"),
                        doc -> doc.getLong("total_duration")
                ));
    }

    @Override
    public Page<UserActiveDurationDetailReportVO> getUserOnlineIntervalsPaginated(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable) {
        Long tenantId = SecurityUtils.getTenantId();
        String startDate = userAdoptionReportDTO.getStartTime().toString();
        String endDate = userAdoptionReportDTO.getEndTime().toString();
        Long userId = userAdoptionReportDTO.getUserId();
        PlatformTypeEnum searchPlatform = null;
        if (Objects.nonNull(userAdoptionReportDTO.getSearch())) {
            searchPlatform = userAdoptionReportDTO.getSearch().getPlatform();
        }

        MongoDatabase database = mongoTemplate.getDb();
        // APN集合和CRM集合
        MongoCollection<Document> apnCollection = database.getCollection("apn_online_user");
        MongoCollection<Document> crmCollection = database.getCollection("crm_online_user");

        // ---------------------------
        // 构造APN分支 pipeline
        // ---------------------------
        List<Bson> apnPipeline = new ArrayList<>();
        apnPipeline.add(match(and(
                eq("tenant_id", tenantId),
                eq("user_id", userId),
                gte("date", startDate),
                lte("date", endDate)
        )));
        apnPipeline.add(project(fields(include("user_id", "online_detail"))));
        apnPipeline.add(unwind("$online_detail"));
        // 规范化 online_detail.startTime（去掉毫秒）
        apnPipeline.add(addFields(new Field<>("online_detail.startTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.startTime"))
                        .append("month", new Document("$month", "$online_detail.startTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
                        .append("hour", new Document("$hour", "$online_detail.startTime"))
                        .append("minute", new Document("$minute", "$online_detail.startTime"))
                        .append("second", new Document("$second", "$online_detail.startTime"))
                )
        )));
        // 规范化 online_detail.endTime（去掉毫秒）
        apnPipeline.add(addFields(new Field<>("online_detail.endTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.endTime"))
                        .append("month", new Document("$month", "$online_detail.endTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
                        .append("hour", new Document("$hour", "$online_detail.endTime"))
                        .append("minute", new Document("$minute", "$online_detail.endTime"))
                        .append("second", new Document("$second", "$online_detail.endTime"))
                )
        )));
        // 预排序：按照 online_detail.startTime 升序排序，使得后续group时push进数组的顺序正确
        apnPipeline.add(sort(Sorts.ascending("online_detail.startTime")));
        // 按 user_id 分组，将所有 online_detail 收集到数组中
        apnPipeline.add(group("$user_id", Accumulators.push("intervals", "$online_detail")));
        // 合并连续时间区间（允许最多1秒的间隔合并）
        apnPipeline.add(addFields(new Field<>("merged_intervals",
                new Document("$reduce", new Document()
                        .append("input", "$intervals")
                        .append("initialValue", new ArrayList<>())
                        .append("in", new Document("$cond", new Document()
                                .append("if", new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)))
                                .append("then", new Document("$let", new Document()
                                        .append("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
                                        .append("in", new Document("$cond", new Document()
                                                .append("if", new Document("$lte", Arrays.asList(
                                                        new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")),
                                                        1000
                                                )))
                                                .append("then", new Document("$concatArrays", Arrays.asList(
                                                        new Document("$slice", Arrays.asList("$$value",
                                                                new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1))
                                                        )),
                                                        Arrays.asList(new Document("startTime", "$$last.startTime")
                                                                .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime")))
                                                        )
                                                )))
                                                .append("else", new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this"))))
                                        ))
                                ))
                                .append("else", Arrays.asList("$$this"))
                        ))
                )
        )));
        // 拆分合并后的区间，每个区间变成单独文档
        apnPipeline.add(unwind("$merged_intervals"));
        // 计算每个区间的持续时长（毫秒差值），这里加上一个条件（如果秒数为59则补1000毫秒），确保边界合并逻辑正确
        apnPipeline.add(addFields(new Field<>("duration",
                new Document("$add", Arrays.asList(
                        new Document("$subtract", Arrays.asList("$merged_intervals.endTime", "$merged_intervals.startTime")),
                        new Document("$cond", Arrays.asList(
                                new Document("$eq", Arrays.asList(new Document("$second", "$merged_intervals.endTime"), 59)),
                                1000,
                                0
                        ))
                ))
        )));
        // 增加平台字段：APN
        apnPipeline.add(addFields(new Field<>("platform", "APN")));
        // 只返回需要的字段
        apnPipeline.add(project(fields(
                computed("platform", "$platform"),
                computed("startTime", "$merged_intervals.startTime"),
                computed("endTime", "$merged_intervals.endTime"),
                include("duration")
        )));

        // ---------------------------
        // 构造CRM分支 pipeline（与APN类似，只是platform固定为 "CRM"）
        // ---------------------------
        List<Bson> crmPipeline = new ArrayList<>();
        crmPipeline.add(match(and(
                eq("tenant_id", tenantId),
                eq("user_id", userId),
                gte("date", startDate),
                lte("date", endDate)
        )));
        crmPipeline.add(project(fields(include("user_id", "online_detail"))));
        crmPipeline.add(unwind("$online_detail"));
        crmPipeline.add(addFields(new Field<>("online_detail.startTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.startTime"))
                        .append("month", new Document("$month", "$online_detail.startTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.startTime"))
                        .append("hour", new Document("$hour", "$online_detail.startTime"))
                        .append("minute", new Document("$minute", "$online_detail.startTime"))
                        .append("second", new Document("$second", "$online_detail.startTime"))
                )
        )));
        crmPipeline.add(addFields(new Field<>("online_detail.endTime",
                new Document("$dateFromParts", new Document()
                        .append("year", new Document("$year", "$online_detail.endTime"))
                        .append("month", new Document("$month", "$online_detail.endTime"))
                        .append("day", new Document("$dayOfMonth", "$online_detail.endTime"))
                        .append("hour", new Document("$hour", "$online_detail.endTime"))
                        .append("minute", new Document("$minute", "$online_detail.endTime"))
                        .append("second", new Document("$second", "$online_detail.endTime"))
                )
        )));
        crmPipeline.add(sort(Sorts.ascending("online_detail.startTime")));
        crmPipeline.add(group("$user_id", Accumulators.push("intervals", "$online_detail")));
        crmPipeline.add(addFields(new Field<>("merged_intervals",
                new Document("$reduce", new Document()
                        .append("input", "$intervals")
                        .append("initialValue", new ArrayList<>())
                        .append("in", new Document("$cond", new Document()
                                .append("if", new Document("$gt", Arrays.asList(new Document("$size", "$$value"), 0)))
                                .append("then", new Document("$let", new Document()
                                        .append("vars", new Document("last", new Document("$arrayElemAt", Arrays.asList("$$value", -1))))
                                        .append("in", new Document("$cond", new Document()
                                                .append("if", new Document("$lte", Arrays.asList(
                                                        new Document("$subtract", Arrays.asList("$$this.startTime", "$$last.endTime")),
                                                        1000
                                                )))
                                                .append("then", new Document("$concatArrays", Arrays.asList(
                                                        new Document("$slice", Arrays.asList("$$value",
                                                                new Document("$subtract", Arrays.asList(new Document("$size", "$$value"), 1))
                                                        )),
                                                        Arrays.asList(new Document("startTime", "$$last.startTime")
                                                                .append("endTime", new Document("$max", Arrays.asList("$$this.endTime", "$$last.endTime")))
                                                        )
                                                )))
                                                .append("else", new Document("$concatArrays", Arrays.asList("$$value", Arrays.asList("$$this"))))
                                        ))
                                ))
                                .append("else", Arrays.asList("$$this"))
                        ))
                )
        )));
        crmPipeline.add(unwind("$merged_intervals"));
        crmPipeline.add(addFields(new Field<>("duration",
                new Document("$add", Arrays.asList(
                        new Document("$subtract", Arrays.asList("$merged_intervals.endTime", "$merged_intervals.startTime")),
                        new Document("$cond", Arrays.asList(
                                new Document("$eq", Arrays.asList(new Document("$second", "$merged_intervals.endTime"), 59)),
                                1000,
                                0
                        ))
                ))
        )));
        crmPipeline.add(addFields(new Field<>("platform", "CRM")));
        crmPipeline.add(project(fields(
                computed("platform", "$platform"),
                computed("startTime", "$merged_intervals.startTime"),
                computed("endTime", "$merged_intervals.endTime"),
                include("duration")
        )));

        // ---------------------------
        // 分页、排序及总数计算：构造 facet 阶段
        // ---------------------------
        // 根据 Pageable 构造排序阶段（只允许 "duration"、"startTime"、"platform"）
        List<Bson> sortList = new ArrayList<>();
        if (Objects.nonNull(pageable)) {
            Sort sort = pageable.getSort();
            sort.forEach(order -> {
                if ("duration".equals(order.getProperty())) {
                    sortList.add(order.isAscending() ? Sorts.ascending("duration") : Sorts.descending("duration"));
                } else if ("startTime".equals(order.getProperty())) {
                    sortList.add(order.isAscending() ? Sorts.ascending("startTime") : Sorts.descending("startTime"));
                } else if ("platform".equals(order.getProperty())) {
                    sortList.add(order.isAscending() ? Sorts.ascending("platform") : Sorts.descending("platform"));
                }
            });
        }

        Bson sortStage;
        if (sortList.isEmpty()) {
            sortStage = sort(Sorts.descending("startTime")); // 默认按照 startTime 倒序
        } else {
            sortStage = sort(Sorts.orderBy(sortList));
        }

//        // 构造 facet 阶段
//        int skip = (int) pageable.getOffset();
//        int limit = pageable.getPageSize();
//        Bson facetStage = facet(
//                new Facet("data", skip(skip), limit(limit)),
//                new Facet("totalCount", count("count"))
//        );
        Bson facetStage;
        if (Objects.nonNull(pageable)) {
            int skip = (int) pageable.getOffset();
            int limit = pageable.getPageSize();
            facetStage = facet(
                    new Facet("data", skip(skip), limit(limit)),
                    new Facet("totalCount", count("count"))
            );
        } else {
            // 若 pageable 为 null，则返回所有数据，不做分页限制
            facetStage = facet(
                    new Facet("data", new ArrayList<>()), // 空的管道步骤，数据不做过滤
                    new Facet("totalCount", count("count"))
            );
        }

        // ---------------------------
        // 根据 searchPlatform 分支选择执行哪一个 pipeline
        // ---------------------------
        AggregateIterable<Document> facetResult;
        if (Objects.nonNull(searchPlatform)) {
            if (PlatformTypeEnum.APN.equals(searchPlatform)) {
                // 只使用 APN pipeline，不添加 unionWith
                // 添加排序和 facet 阶段
                apnPipeline.add(sortStage);
                apnPipeline.add(facetStage);
                facetResult = apnCollection.aggregate(apnPipeline).allowDiskUse(true);
            } else if (PlatformTypeEnum.CRM.equals(searchPlatform)) {
                // 只使用 CRM pipeline，不添加 unionWith；这里直接在CRM集合上执行
                crmPipeline.add(sortStage);
                crmPipeline.add(facetStage);
                facetResult = crmCollection.aggregate(crmPipeline).allowDiskUse(true);
            } else {
                // 若传入的平台值不匹配，则默认合并两边数据
                apnPipeline.add(unionWith("crm_online_user", crmPipeline));
                apnPipeline.add(sortStage);
                apnPipeline.add(facetStage);
                facetResult = apnCollection.aggregate(apnPipeline).allowDiskUse(true);
            }
        } else {
            // 未指定 platform，则按原逻辑合并 APN 与 CRM
            apnPipeline.add(unionWith("crm_online_user", crmPipeline));
            apnPipeline.add(sortStage);
            apnPipeline.add(facetStage);
            facetResult = apnCollection.aggregate(apnPipeline).allowDiskUse(true);
        }

        // ---------------------------
        // 解析 facet 结果：获取分页数据和总记录数
        // ---------------------------
        Document facetDoc = facetResult.first();
        if (facetDoc == null) {
            if (Objects.isNull(pageable)) {
                pageable = PageRequest.of(0, 0);
            }
            return new PageImpl<>(Collections.emptyList(), pageable, 0L);
        }
        List<Document> dataList = (List<Document>) facetDoc.get("data");
        List<Document> totalCountList = (List<Document>) facetDoc.get("totalCount");
        long totalCount = 0;
        if (totalCountList != null && !totalCountList.isEmpty()) {
            // 兼容 Integer 或 Long 类型：将返回值转为 Number 后再转换为 long
            Number countNumber = (Number) totalCountList.get(0).get("count");
            totalCount = countNumber.longValue();
        }

        // 将 dataList 转换为 VO 对象列表
        List<UserActiveDurationDetailReportVO> reportList = new ArrayList<>();
        for (Document doc : dataList) {
            String platformField = doc.getString("platform");
            Date start = doc.getDate("startTime");
            Date end = doc.getDate("endTime");
            Long durationMs = doc.getLong("duration");
            // 将毫秒转换为分钟（向下取整）
            Long durationInMinutes = durationMs / 60000L;

            OffsetDateTime startTime = start.toInstant()
                    .atZone(ZoneId.of("UTC"))
                    .toOffsetDateTime();
            OffsetDateTime endTime = end.toInstant()
                    .atZone(ZoneId.of("UTC"))
                    .toOffsetDateTime();

            reportList.add(new UserActiveDurationDetailReportVO(platformField, startTime, endTime, durationInMinutes));
        }
        if (Objects.isNull(pageable)) {
            pageable = PageRequest.of(0, (int) totalCount);
        }

        return new PageImpl<>(reportList, pageable, totalCount);
    }

    @Override
    public void downloadUserOnlineIntervalsDetails(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("downloadUserOnlineIntervalsDetails");
        stopWatch.start("1. search data");
        Page<UserActiveDurationDetailReportVO> data = this.getUserOnlineIntervalsPaginated(userAdoptionReportDTO, null);
//        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        stopWatch.stop();

        stopWatch.start("2. 实体转换");
        List<UserActiveDurationDetailReportExcelVO> list = data.stream().map(UserActiveDurationDetailReportExcelVO::new).collect(Collectors.toList());
        stopWatch.stop();

        stopWatch.start("3. 输出 excel");
        LocalDate utcDate = LocalDate.now(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = utcDate.format(formatter);
        ExcelUtil.downloadExcel(response, UserActiveDurationDetailReportExcelVO.class, list, "",  "User Active Duration " + formattedDate + ".xlsx", false);
        stopWatch.stop();

        log.info(" downloadUserOnlineIntervalsDetails by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }
}
