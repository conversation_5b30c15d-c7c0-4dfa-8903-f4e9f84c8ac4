package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiInterviewDetailExcelENVO {

    @ExcelIgnore
    private Long nodeId;

    @ExcelProperty(value = "Candidate Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private Instant submitDate;

    @ExcelProperty(value = "Date of Update to Interview", index = 1)
    private String submitDateFormat;

    @ExcelIgnore
    private String jobTitle;

    @ExcelIgnore
    private Long jobId;

    @ExcelProperty(value = "Job Title (ID)", index = 2)
    private String jobTitleAndJobId;

    @ExcelProperty(value = "Job Code", index = 3)
    private String jobCode;

    @ExcelProperty(value = "Company", index = 4)
    private String companyName;

    @ExcelIgnore
    private Long companyId;

    @ExcelIgnore
    private JobType jobType;

    @ExcelProperty(value = "Job Status", index = 5)
    private JobStatus jobStatus;

    //后端去做 淘汰的兼容
    @ExcelProperty(value = "Workflow Status", index = 6)
    private MyCandidateStatusFilter workflowStatus;

    @ExcelProperty(value = "Interview Progress", index = 7)
    private Long interviewProgress;

    @ExcelProperty(value = "Interview Type", index = 8)
    private InterviewType interviewType;

    @ExcelProperty(value = "Time Zone", index = 9)
    private String timeZone;

    @ExcelIgnore
    private Instant fromTime;

    @ExcelIgnore
    private Instant toTime;

    @ExcelProperty(value = "Interview Time", index = 10)
    private String interviewTime;

    @ExcelProperty(value = "Pipeline Notes", index = 11)
    private String pipelineNote;

    @ExcelProperty(value = "Job Location", index = 12)
    private String jobLocation;

    @ExcelProperty(value = "Account Manager", index = 13)
    private String am;

    @ExcelProperty(value = "Co-Account Manager", index = 14)
    private String coAm;

    @ExcelProperty(value = "Recruiter", index = 15)
    private String recruiter;

    @ExcelProperty(value = "Sourcer", index = 16)
    private String sourcer;

    @ExcelProperty(value = "AC", index = 17)
    private String ac;

    @ExcelProperty(value = "DM", index = 18)
    private String dm;

    @ExcelProperty(value = "Owner", index = 19)
    private String owner;

    @ExcelProperty(value = "Sales Lead Owner", index = 20)
    private String salesLeadOwner;

    @ExcelProperty(value = "BD Owner", index = 21)
    private String bdOwner;

    @ExcelProperty(value = "Last Updated By", index = 22)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelProperty(value = "Last Updated At", index = 23)
    private String lastModifiedDateFormat;


    public String getInterviewTime() {
        return getFromTime() + " ~ " + getToTime();
    }

    public String getFromTime() {
        if (ObjectUtil.isEmpty(fromTime)) {
            return "";
        }
        return fromTime.atZone(ZoneId.of("UTC")).toLocalDateTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS));
    }

    public String getToTime() {
        if (ObjectUtil.isEmpty(toTime)) {
            return "";
        }
        return toTime.atZone(ZoneId.of("UTC")).toLocalDateTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS));
    }

    public String getJobTitleAndJobId() {
        return jobTitle + " (" + jobId + ")";
    }

    public JobStatus getJobStatus() {
        if (jobType == JobType.PAY_ROLL) {
            return null;
        }
        return jobStatus;
    }
}
