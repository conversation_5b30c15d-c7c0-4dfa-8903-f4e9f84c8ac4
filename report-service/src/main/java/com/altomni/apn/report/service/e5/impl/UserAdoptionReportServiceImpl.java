package com.altomni.apn.report.service.e5.impl;

import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ConcurrentStopWatch;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportWithThresholdVO;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportApplicationStatsDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportCrmNoteStatsDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportEmailStatsDTO;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.e5.UserActiveDurationService;
import com.altomni.apn.report.service.e5.UserAdoptionReportDetailsService;
import com.altomni.apn.report.service.e5.UserAdoptionReportService;
import com.altomni.apn.report.service.email.EmailService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.Collator;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.ReportConstants.GRAND_TOTAL;

/**
 * Service Implementation for UserAdoptionReportServiceImpl.
 */
@Slf4j
@Service
public class UserAdoptionReportServiceImpl implements UserAdoptionReportService {

    private volatile Integer activeDurationThreshold;

    private static final long SECONDS_TO_MINUTES = 60;

    private static final Integer TEAM_DATA_SCOPE = 2;

    private static final Integer ALL_DATA_SCOPE = 99;

    @Resource
    private UserActiveDurationService userActiveDurationService;

    @Resource
    private UserService userService;

    @Resource
    private UserAdoptionReportDetailsService userAdoptionReportDetailsService;

    @Resource
    private CompanyService companyService;

    @Resource
    private EmailService emailService;

    @PostConstruct
    public void init() {
//        LoginUtil.simulateLoginWithClient();
//        ApnParam apnParam = userService.findByParamKey(
//                Constants.APN_PARAM_REPORT_USER_ACTIVE_DURATION_THRESHOLD,
//                SecurityUtils.getTenantId(),
//                Status.Available
//        ).getBody();

//        if (apnParam != null) {
//            this.activeDurationThreshold = Integer.valueOf(apnParam.getParamValue());
//        } else {
            this.activeDurationThreshold = 582; //default value in minute
//        }
    }

    @Override
    public Integer getActiveDurationThreshold() { return activeDurationThreshold; }


    private static <T extends Comparable<T>> Comparator<T> getNullHandlingComparator(Sort.Order order) {
        return Comparator.nullsLast(Comparator.naturalOrder());
    }

    private static Comparator<UserAdoptionReportVO> getUserAdoptionReportComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(UserAdoptionReportServiceImpl::getUserAdoptionReportComparator)
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    private static Comparator<UserAdoptionReportVO> getUserAdoptionReportComparator(Sort.Order order) {
        Comparator<UserAdoptionReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
//            case "username" -> comparator = Comparator.comparing(UserAdoptionReportVO::getUserName, getNullHandlingComparator(order));
            case "username" -> {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        UserAdoptionReportVO::getUserName,
                        Comparator.nullsLast(collator)
                );
            }
            case "activationduration" -> comparator = Comparator.comparing(UserAdoptionReportVO::getActivationDuration, getNullHandlingComparator(order));
            case "callcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getCallCount, getNullHandlingComparator(order));
            case "notecount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getNoteCount, getNullHandlingComparator(order));
            case "emailcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getEmailCount, getNullHandlingComparator(order));
            case "submittojobcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getSubmitToJobCount, getNullHandlingComparator(order));
            case "interviewcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getInterviewCount, getNullHandlingComparator(order));
            case "onboardtalentcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getOnboardTalentCount, getNullHandlingComparator(order));
        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    @Override
    public Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByTeamId(Long tenantId, List<Long> teamIds) {
        Set<Long> teamIdSet = new HashSet<>(teamIds);
        Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(teamIdSet).getBody();
        LocalDate today = LocalDate.now();
        LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate lastMonday = lastSunday.minusDays(6);
        Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(tenantId, lastMonday.toString(), lastSunday.toString(), teamUserIds, getActiveDurationThreshold());
        return userDurationTimeMap;
    }

    public Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByUserId(Long tenantId, List<Long> userIds) {
        LocalDate today = LocalDate.now();
        LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate lastMonday = lastSunday.minusDays(6);
        Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(tenantId, lastMonday.toString(), lastSunday.toString(), userIds, getActiveDurationThreshold());
        return userDurationTimeMap;
    }

    @Override
    public UserAdoptionReportWithThresholdVO getUserAdoptionReport(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException {
        Sort sort = pageable.getSort();

        if (BooleanUtils.isTrue(userAdoptionReportDTO.getLastWeekActivityBelowAverage())) {
            LocalDate today = LocalDate.now();

            LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
            LocalDate lastMonday = lastSunday.minusDays(6);

            userAdoptionReportDTO.setStartTime(lastMonday);
            userAdoptionReportDTO.setEndTime(lastSunday);

            Set<Long> userIds = new HashSet<>();
            List<UserBriefDTO> users = new ArrayList<>();
            try {
                ResponseEntity<List<UserBriefDTO>> res = userService.getAllBriefUsersByTenantId(SecurityUtils.getTenantId());
                users = res.getBody();
            } catch (Exception e) {
                log.error("voip report service get all brief users error: {}, stacktrace: {}", e.getMessage(), e.getStackTrace());
            }
            Map<Long, UserBriefDTO> userMap = users.stream().filter(UserBriefDTO::isActivated).collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

            if (CollectionUtils.isEmpty(userAdoptionReportDTO.getUserIdList()) && CollectionUtils.isEmpty(userAdoptionReportDTO.getTeamIdList())) {

                Set<Long> allUserIds = userMap.keySet();

                Set<Long> withPermissionUserIds = checkReportDataPermission(SecurityUtils.getUserId(), allUserIds);
                userIds = userIds.isEmpty() ? withPermissionUserIds : SetUtils.intersection(userIds, withPermissionUserIds);
            } else {
                if(userAdoptionReportDTO.getUserIdList() != null) userIds.addAll(userAdoptionReportDTO.getUserIdList());
                if(userAdoptionReportDTO.getTeamIdList() != null) {
                    Set<Long> teamIds = new HashSet<>(userAdoptionReportDTO.getTeamIdList());
                    Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                    if(teamUserIds != null) userIds.addAll(teamUserIds);
                }
            }

//            Set<Long> searchUserIds = new HashSet<>(SetUtils.intersection(userIds, withPermissionUserIds)); //if empty set, search all userIds belong to current tenant, or search userIds within this set
//
//            Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), lastMonday.toString(), lastSunday.toString(), searchUserIds, null);
//
//            Set<Long> aboveAverageIds = new HashSet<>();
//            userDurationTimeMap.entrySet().forEach(entry -> {
//                if (entry.getValue() >= activeDurationThreshold) { //找出高于阈值的userIds
//                    aboveAverageIds.add(entry.getKey());
//                }
//            });
//
//
//            if (userIds.isEmpty()) { //查询所有有权限的userIds
//                if (CollectionUtils.isNotEmpty(aboveAverageIds)) { //仅当满足阈值的userIds set不为空时有意义
//                    searchUserIds = new HashSet<>(withPermissionUserIds);
//                    searchUserIds.removeAll(aboveAverageIds);
//                } //否则searchUserIds本身就是empty,传入empty set进行搜索时即为当前用户本身的数据权限
//            } else //查询指定的users
//                if (Objects.equals(searchUserIds.size(), aboveAverageIds.size())) { //所有查询的userIds (searchUserIds)都满足阈值，不需要进行之后的查询，结束并返回
//                return Collections.emptyList();
//            } else {//查询的user，只有一部分满足阈值：从查询的userId set中(searchUserIds)移除满足阈值的ids (aboveAverageIds)，用剩余的不满足阈值的id继续查询
//                    searchUserIds.removeAll(aboveAverageIds);
//            }

//            Set<Long> belowAverageUserIds = userDurationTimeMap.keySet(); //may not cover all userIds, some users do not have any record in mongo

            Integer threshold = null;
            if (BooleanUtils.isNotTrue(userAdoptionReportDTO.getIgnoreThreshold())) {
                threshold = getActiveDurationThreshold();
            }
            Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), userIds, threshold);
            Set<Long> belowAverageUserIds = userDurationTimeMap.keySet();

            UserAdoptionReportWithThresholdVO r = new UserAdoptionReportWithThresholdVO();
            r.setUserActiveThreshold(Long.valueOf(getActiveDurationThreshold()));

            if (BooleanUtils.isTrue(userAdoptionReportDTO.getIgnoreThreshold())) {
                Long currentUserActiveDuration = userDurationTimeMap.values().stream().findFirst().orElse(0L) / SECONDS_TO_MINUTES;
                r.setPersonalViewCurrentUserActiveDuration(currentUserActiveDuration);
                if (Long.compare(currentUserActiveDuration, r.getUserActiveThreshold()) >= 0) {
                    r.setUserAdoptionReport(Collections.emptyList());
                    return r;
                }
            }

            if (CollectionUtils.isEmpty(belowAverageUserIds)) {
                r.setUserAdoptionReport(Collections.emptyList());
                return r;
            }

            ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("getUserAdoptionReport");
            stopWatch.start("total task");
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture<Map<Long, int[]>> callReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("findOnboardedNotInvoiced");
                Map<Long, int[]> res = userAdoptionReportDetailsService.getCallReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCallReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportApplicationStatsDTO>> applicationStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getApplicationStatsReport");
                Map<Long, UserAdoptionReportApplicationStatsDTO> res = userAdoptionReportDetailsService.getApplicationStatsReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getApplicationStatsReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportEmailStatsDTO>> emailStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("searchSentEmailsByUsers");
                MailSearchDTO mailSearchDTO = new MailSearchDTO();
                mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
                mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
                mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
                mailSearchDTO.setUserIds(new ArrayList<>(belowAverageUserIds));
                mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT"));
                List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
                stopWatch.stop();
                log.info("[getUserAdoptionReport] searchSentEmailsByUsers time = {}ms ", stopWatch.getTotalTimeMillis());

                if (CollectionUtils.isEmpty(res)) {
                    return Collections.emptyMap();
                }

                Set<Long> contactIds = res.parallelStream()
                        .peek(this::extractTalentIdAndContactId)
                        .flatMap(e -> e.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

                Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();

                return res.parallelStream()
                        // 使用 groupingByConcurrent 进行并发分组，且自定义 Collector 归并每个分组的 EmailLogOverviewDTO
                        .collect(Collectors.groupingByConcurrent(
                                EmailLogOverviewDTO::getUserId,
                                Collector.of(
                                        // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                        UserAdoptionReportEmailStatsDTO::new,
                                        // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                        (stats, dto) -> {
                                            stats.setId(dto.getUserId());
                                            int talentSize = CollectionUtils.size(dto.getTalentIds());
                                            int contactSize =  CollectionUtils.size(dto.getContactIds());
                                            long current = stats.getEmailCount();
                                            stats.setEmailCount(current + talentSize + contactSize);
                                            if (dto.getTalentIds() != null) {
                                                stats.getTalentIds().addAll(dto.getTalentIds());
                                            }
                                            if (dto.getContactIds() != null) {
                                                stats.getContactIds().addAll(dto.getContactIds());
                                            }
                                        },
                                        // 合并器：合并两个累加器
                                        (stats1, stats2) -> {
                                            long count1 = stats1.getEmailCount();
                                            long count2 = stats2.getEmailCount();
                                            stats1.setEmailCount(count1 + count2);
                                            stats1.getTalentIds().addAll(stats2.getTalentIds());
                                            stats1.getContactIds().addAll(stats2.getContactIds());
                                            return stats1;
                                        },
                                        // 在 finisher 中一次性并行处理 contactIds
                                        stats -> {
                                            // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                            Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                            Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                            stats.getContactIds().parallelStream().forEach(contactId -> {
                                                Long mappedTalent = contactToTalentMap.get(contactId);
                                                if (mappedTalent != null) {
                                                    concurrentMappedTalents.add(mappedTalent);
                                                } else {
                                                    concurrentUnmappedContacts.add(contactId);
                                                }
                                            });

                                            // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                            Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                            mergedTalentIds.addAll(concurrentMappedTalents);

                                            // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                            stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                            return stats;
                                        }
                                )
                        ));
            });

            CompletableFuture<Map<Long, UserAdoptionReportCrmNoteStatsDTO>> crmNoteStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getCrmNoteReport");
                List<UserAdoptionReportCrmNoteStatsDTO> res = userAdoptionReportDetailsService.getCrmNoteReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCrmNoteReport time = {}ms ", stopWatch.getTotalTimeMillis());

                Set<Long> contactIds = res.parallelStream()
                        .flatMap(a -> a.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
                if (Objects.nonNull(relation)) {
                    Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
                    res.parallelStream().forEach(item -> {
                        Set<Long> talentIds = item.getContactIds().stream()
                                .map(contactToTalentMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());

                        item.setTalentIds(talentIds);
                        item.setUniqueContactIdCount(item.getContactIds().size() - talentIds.size());
                    });
                }

                return res.stream().collect(Collectors.toMap(UserAdoptionReportCrmNoteStatsDTO::getId, Function.identity()));
            });

            CompletableFuture.allOf(callReport, applicationStatsReport, emailStatsReport, crmNoteStatsReport)
                    .exceptionally(FutureExceptionUtil::handleFutureException);

            Map<Long, int[]> callReportMap = callReport.join();
            Map<Long, UserAdoptionReportApplicationStatsDTO> applicationStatsReportMap = applicationStatsReport.join();
            Map<Long, UserAdoptionReportEmailStatsDTO> emailStatsReportMap = emailStatsReport.join();
            Map<Long, UserAdoptionReportCrmNoteStatsDTO> crmNoteReportMap = crmNoteStatsReport.join();

            List<UserAdoptionReportVO> res = new ArrayList<>();
            for (Long userId : callReportMap.keySet()) {
                UserAdoptionReportVO userAdoptionReportVO = new UserAdoptionReportVO();
                userAdoptionReportVO.setUserId(userId);
                UserBriefDTO u = userMap.get(userId);
                if (Objects.isNull(u)) {
                    continue;
                }
                userAdoptionReportVO.setUserName(CommonUtils.formatFullName(u.getFirstName(), u.getLastName()));
                userAdoptionReportVO.setActivationDuration(userDurationTimeMap.getOrDefault(userId, 0L) / SECONDS_TO_MINUTES);

                int[] callReportDetails = callReportMap.getOrDefault(userId, new int[]{0, 0});
                userAdoptionReportVO.setCallCount(callReportDetails[0]);
                userAdoptionReportVO.setUniqueCalledTalentCount(callReportDetails[1]);

                //email
                UserAdoptionReportEmailStatsDTO emailStatsDTO = emailStatsReportMap.getOrDefault(userId, new UserAdoptionReportEmailStatsDTO());
                userAdoptionReportVO.setEmailCount(emailStatsDTO.getEmailCount());
                userAdoptionReportVO.setUniqueEmailedTalentCount(emailStatsDTO.getUniqueEmailCount());

                //application
                UserAdoptionReportApplicationStatsDTO applicationStatsDetails = applicationStatsReportMap.getOrDefault(userId, new UserAdoptionReportApplicationStatsDTO());
                userAdoptionReportVO.setSubmitToJobCount(ObjectUtils.firstNonNull(applicationStatsDetails.getSubmitToJobCount(), 0L));
                userAdoptionReportVO.setInterviewCount(ObjectUtils.firstNonNull(applicationStatsDetails.getInterviewCount(), 0L));
                userAdoptionReportVO.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getUniqueInterviewedTalentCount(), 0L));
                userAdoptionReportVO.setOnboardTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getOnboardTalentCount(), 0L));

                UserAdoptionReportCrmNoteStatsDTO crmNoteStatsDTO = crmNoteReportMap.getOrDefault(userId, new UserAdoptionReportCrmNoteStatsDTO());
                userAdoptionReportVO.setNoteCount(ObjectUtils.firstNonNull(crmNoteStatsDTO.getNoteCount(), 0L) + ObjectUtils.firstNonNull(applicationStatsDetails.getApnTalentNoteCount(), 0L));
                Set<Long> apnTalentIds = Optional.ofNullable(applicationStatsDetails.getUniqueTalentIds())
                        .filter(StringUtils::isNotBlank)
                        .map(s -> Arrays.stream(s.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .map(Long::valueOf)
                                .collect(Collectors.toSet()))
                        .orElse(Collections.emptySet());
                Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmNoteStatsDTO.getTalentIds());

                userAdoptionReportVO.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmNoteStatsDTO.getUniqueContactIdCount());

                res.add(userAdoptionReportVO);
            }

            if(sort != null) res.sort(getUserAdoptionReportComparatorFromSort(sort));
            r.setUserAdoptionReport(res);
            return r;

        } else {

            Set<Long> userIds = new HashSet<>();
            List<UserBriefDTO> users = new ArrayList<>();
            try {
                ResponseEntity<List<UserBriefDTO>> res = userService.getAllBriefUsersByTenantId(SecurityUtils.getTenantId());
                users = res.getBody();
            } catch (Exception e) {
                log.error("voip report service get all brief users error: {}, stacktrace: {}", e.getMessage(), e.getStackTrace());
            }
            Map<Long, UserBriefDTO> userMap = users.stream().filter(UserBriefDTO::isActivated).collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

            if (CollectionUtils.isEmpty(userAdoptionReportDTO.getUserIdList()) && CollectionUtils.isEmpty(userAdoptionReportDTO.getTeamIdList())) {

                Set<Long> allUserIds = userMap.keySet();

                Set<Long> withPermissionUserIds = checkReportDataPermission(SecurityUtils.getUserId(), allUserIds);
                userIds = userIds.isEmpty() ? withPermissionUserIds : SetUtils.intersection(userIds, withPermissionUserIds);
            } else {
                if(userAdoptionReportDTO.getUserIdList() != null) userIds.addAll(userAdoptionReportDTO.getUserIdList());
                if(userAdoptionReportDTO.getTeamIdList() != null) {
                    Set<Long> teamIds = new HashSet<>(userAdoptionReportDTO.getTeamIdList());
                    Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                    if(teamUserIds != null) userIds.addAll(teamUserIds);
                }
            }

            if(userAdoptionReportDTO.getUserIdList() != null) userIds.addAll(userAdoptionReportDTO.getUserIdList());
            if(userAdoptionReportDTO.getTeamIdList() != null) {
                Set<Long> teamIds = new HashSet<>(userAdoptionReportDTO.getTeamIdList());
                Set<Long> teamUserIds = userService.getAllTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                if(teamUserIds != null) userIds.addAll(teamUserIds);
            }

            Set<Long> ids = userIds;


            ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("getUserAdoptionReport");
            stopWatch.start("total task");
            SecurityContext context = SecurityContextHolder.getContext();


            CompletableFuture<Map<Long, Long>> userActiveDurationReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("userActiveDurationReport");
                Map<Long, Long> res = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), ids, null);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getUsersByOnlineDurationReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, int[]>> callReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("findOnboardedNotInvoiced");
                Map<Long, int[]> res = userAdoptionReportDetailsService.getCallReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCallReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportApplicationStatsDTO>> applicationStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getApplicationStatsReport");
                Map<Long, UserAdoptionReportApplicationStatsDTO> res = userAdoptionReportDetailsService.getApplicationStatsReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getApplicationStatsReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportEmailStatsDTO>> emailStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("searchSentEmailsByUsers");
                MailSearchDTO mailSearchDTO = new MailSearchDTO();
                mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
                mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
                mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
                mailSearchDTO.setUserIds(new ArrayList<>(ids));
                mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT"));
                List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
                stopWatch.stop();
                log.info("[getUserAdoptionReport] searchSentEmailsByUsers time = {}ms ", stopWatch.getTotalTimeMillis());

                if (CollectionUtils.isEmpty(res)) {
                    return Collections.emptyMap();
                }

                Set<Long> contactIds = res.parallelStream()
                        .peek(this::extractTalentIdAndContactId)
                        .flatMap(e -> e.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

                Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();

                return res.parallelStream()
                        // 使用 groupingByConcurrent 进行并发分组，且自定义 Collector 归并每个分组的 EmailLogOverviewDTO
                        .collect(Collectors.groupingByConcurrent(
                                EmailLogOverviewDTO::getUserId,
                                Collector.of(
                                        // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                        UserAdoptionReportEmailStatsDTO::new,
                                        // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                        (stats, dto) -> {
                                            stats.setId(dto.getUserId());
                                            int talentSize = CollectionUtils.size(dto.getTalentIds());
                                            int contactSize =  CollectionUtils.size(dto.getContactIds());
                                            long current = stats.getEmailCount();
                                            stats.setEmailCount(current + talentSize + contactSize);
                                            if (dto.getTalentIds() != null) {
                                                stats.getTalentIds().addAll(dto.getTalentIds());
                                            }
                                            if (dto.getContactIds() != null) {
                                                stats.getContactIds().addAll(dto.getContactIds());
                                            }
                                        },
                                        // 合并器：合并两个累加器
                                        (stats1, stats2) -> {
                                            long count1 = stats1.getEmailCount();
                                            long count2 = stats2.getEmailCount();
                                            stats1.setEmailCount(count1 + count2);
                                            stats1.getTalentIds().addAll(stats2.getTalentIds());
                                            stats1.getContactIds().addAll(stats2.getContactIds());
                                            return stats1;
                                        },
                                        // 在 finisher 中一次性并行处理 contactIds
                                        stats -> {
                                            // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                            Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                            Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                            stats.getContactIds().parallelStream().forEach(contactId -> {
                                                Long mappedTalent = contactToTalentMap.get(contactId);
                                                if (mappedTalent != null) {
                                                    concurrentMappedTalents.add(mappedTalent);
                                                } else {
                                                    concurrentUnmappedContacts.add(contactId);
                                                }
                                            });

                                            // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                            Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                            mergedTalentIds.addAll(concurrentMappedTalents);

                                            // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                            stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                            return stats;
                                        }
                                )
                        ));
            });

            CompletableFuture<Map<Long, UserAdoptionReportCrmNoteStatsDTO>> crmNoteStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getCrmNoteReport");
                List<UserAdoptionReportCrmNoteStatsDTO> res = userAdoptionReportDetailsService.getCrmNoteReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCrmNoteReport time = {}ms ", stopWatch.getTotalTimeMillis());

                Set<Long> contactIds = res.parallelStream()
                        .flatMap(a -> a.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
                if (Objects.nonNull(relation)) {
                    Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
                    res.parallelStream().forEach(item -> {
                        Set<Long> talentIds = item.getContactIds().stream()
                                .map(contactToTalentMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());

                        item.setTalentIds(talentIds);
                        item.setUniqueContactIdCount(item.getContactIds().size() - talentIds.size());
                    });
                }

                return res.stream().collect(Collectors.toMap(UserAdoptionReportCrmNoteStatsDTO::getId, Function.identity()));
            });

            CompletableFuture.allOf(userActiveDurationReport, callReport, applicationStatsReport, emailStatsReport, crmNoteStatsReport)
                    .exceptionally(FutureExceptionUtil::handleFutureException);

            Map<Long, Long> userActiveDuractionReportMap = userActiveDurationReport.join();
            Map<Long, int[]> callReportMap = callReport.join();
            Map<Long, UserAdoptionReportApplicationStatsDTO> applicationStatsReportMap = applicationStatsReport.join();
            Map<Long, UserAdoptionReportEmailStatsDTO> emailStatsReportMap = emailStatsReport.join();
            Map<Long, UserAdoptionReportCrmNoteStatsDTO> crmNoteReportMap = crmNoteStatsReport.join();


            List<UserAdoptionReportVO> res = new ArrayList<>();
            for (Long userId : callReportMap.keySet()) {
                UserAdoptionReportVO userAdoptionReportVO = new UserAdoptionReportVO();
                userAdoptionReportVO.setUserId(userId);
                UserBriefDTO u = userMap.get(userId);
                if (Objects.isNull(u)) {
                    continue;
                }
                userAdoptionReportVO.setUserName(CommonUtils.formatFullName(u.getFirstName(), u.getLastName()));
                userAdoptionReportVO.setActivationDuration(userActiveDuractionReportMap.getOrDefault(userId, 0L) / SECONDS_TO_MINUTES);

                int[] callReportDetails = callReportMap.getOrDefault(userId, new int[]{0, 0});
                userAdoptionReportVO.setCallCount(callReportDetails[0]);
                userAdoptionReportVO.setUniqueCalledTalentCount(callReportDetails[1]);

                //email
                UserAdoptionReportEmailStatsDTO emailStatsDTO = emailStatsReportMap.getOrDefault(userId, new UserAdoptionReportEmailStatsDTO());
                userAdoptionReportVO.setEmailCount(emailStatsDTO.getEmailCount());
                userAdoptionReportVO.setUniqueEmailedTalentCount(emailStatsDTO.getUniqueEmailCount());

                //application
                UserAdoptionReportApplicationStatsDTO applicationStatsDetails = applicationStatsReportMap.getOrDefault(userId, new UserAdoptionReportApplicationStatsDTO());
                userAdoptionReportVO.setSubmitToJobCount(ObjectUtils.firstNonNull(applicationStatsDetails.getSubmitToJobCount(), 0L));
                userAdoptionReportVO.setInterviewCount(ObjectUtils.firstNonNull(applicationStatsDetails.getInterviewCount(), 0L));
                userAdoptionReportVO.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getUniqueInterviewedTalentCount(), 0L));
                userAdoptionReportVO.setOnboardTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getOnboardTalentCount(), 0L));

                //note
                UserAdoptionReportCrmNoteStatsDTO crmNoteStatsDTO = crmNoteReportMap.getOrDefault(userId, new UserAdoptionReportCrmNoteStatsDTO());
                userAdoptionReportVO.setNoteCount(ObjectUtils.firstNonNull(crmNoteStatsDTO.getNoteCount(), 0L) + ObjectUtils.firstNonNull(applicationStatsDetails.getApnTalentNoteCount(), 0L));
                Set<Long> apnTalentIds = Optional.ofNullable(applicationStatsDetails.getUniqueTalentIds())
                        .filter(StringUtils::isNotBlank)
                        .map(s -> Arrays.stream(s.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .map(Long::valueOf)
                                .collect(Collectors.toSet()))
                        .orElse(Collections.emptySet());
                Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmNoteStatsDTO.getTalentIds());
                userAdoptionReportVO.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmNoteStatsDTO.getUniqueContactIdCount());

                res.add(userAdoptionReportVO);
            }

            if(sort != null) res.sort(getUserAdoptionReportComparatorFromSort(sort));

            UserAdoptionReportWithThresholdVO r = new UserAdoptionReportWithThresholdVO();
            r.setUserActiveThreshold(Long.valueOf(getActiveDurationThreshold()));
            r.setUserAdoptionReport(res);
            return r;
        }
    }

    private void extractTalentIdAndContactId(EmailLogOverviewDTO emailLog) {
        String[] parts = StringUtils.split(emailLog.getCustomerId2(), ",");
        List<Long> talentIds = new ArrayList<>(parts.length);
        List<Long> contactIds = new ArrayList<>(parts.length);
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("APN:TALENT")) {
                talentIds.add(Long.parseLong(part.substring("APN:TALENT".length())));
            } else if (part.startsWith("AC")) {
                contactIds.add(Long.parseLong(part.substring("AC".length())));
            }
        }
        emailLog.setTalentIds(talentIds);
        emailLog.setContactIds(contactIds);
    }


    private void collectUserIds(PermissionTeamTreeDTO teamTree, Set<Long> userIds) {
        if (teamTree == null) {
            return;
        }
        if (teamTree.getData() != null) {
            for (PermissionTeamUserDTO user : teamTree.getData()) {
                if (user != null && user.getId() != null) {
                    userIds.add(user.getId());
                }
            }
        }

        // 递归遍历子节点
        if (teamTree.getChildren() != null) {
            for (PermissionTeamTreeDTO child : teamTree.getChildren()) {
                collectUserIds(child, userIds);
            }
        }
    }

    private Set<Long> checkReportDataPermission(Long userId, Set<Long> allUserIds) throws ExecutionException, InterruptedException {
        if (SecurityUtils.isAdmin()) return allUserIds;
        PermissionUserTeamPermissionVM permissions = userService.getDataPermissionsByUserId(userId).getBody();
        Set<Long> availableUserIds = new HashSet<>();
        availableUserIds.add(userId);
        if(permissions != null) {
            PermissionUserTeamPermissionVM.PermissionDetail permissionDetail = permissions.getReportPermission();
            if(permissionDetail != null) {
                if(permissionDetail.getDataScope().equals(TEAM_DATA_SCOPE)) {
                    List<PermissionTeamTreeDTO> permissionTeamTrees = userService.getTeamsWithPermissionAndUserByType(Module.REPORT, true).getBody();
                    if(permissionTeamTrees != null) {
                        for (PermissionTeamTreeDTO teamTree : permissionTeamTrees) {
                            collectUserIds(teamTree, availableUserIds);
                        }
                    }
                }
                if(permissionDetail.getDataScope().equals(ALL_DATA_SCOPE)) {
                    availableUserIds.addAll(allUserIds);
                }
            }
        }
        return availableUserIds;
    }

    @Override
    public UserAdoptionReportWithThresholdVO getUserAdoptionReportForDashboard(String view, UserAdoptionReportDTO search) throws ExecutionException, InterruptedException {
        UserAdoptionReportDTO userAdoptionReportDTO = new UserAdoptionReportDTO();
        userAdoptionReportDTO.setLastWeekActivityBelowAverage(true);
        Pageable pageable = PageRequest.of(0, 10, Sort.by("activationDuration").ascending());

        if ("PERSONAL".equalsIgnoreCase(view)) {
            userAdoptionReportDTO.setUserIdList(Collections.singletonList(SecurityUtils.getUserId()));
            userAdoptionReportDTO.setIgnoreThreshold(Boolean.TRUE);
            return this.getUserAdoptionReport(userAdoptionReportDTO, pageable);
        } else if ("MANAGEMENT".equalsIgnoreCase(view)) {
//            Set<Long> withPermissionUserIds = checkReportDataPermission(SecurityUtils.getUserId(), new HashSet<>()); //if withPermissionUserIds is null, it means current user have permission to view all users
//            userAdoptionReportDTO.setUserIdList(new ArrayList<>(withPermissionUserIds));
            userAdoptionReportDTO.setUserIdList(search.getUserIdList());
            userAdoptionReportDTO.setTeamIdList(search.getTeamIdList());
            return this.getUserAdoptionReport(userAdoptionReportDTO, pageable);
        }
        throw new CustomParameterizedException("Invalid view param!");
    }

    @Override
    public void exportE5UserAdoptionReportToExcel(UserAdoptionReportDTO userAdoptionReportDTO, HttpServletResponse response) throws ExecutionException, InterruptedException {
        UserAdoptionReportWithThresholdVO vo = this.getUserAdoptionReport(userAdoptionReportDTO, null);
        List<UserAdoptionReportVO> userAdoptionReport = vo.getUserAdoptionReport();
//        setP1ExcelGrandTotal(userAdoptionReport);
        ExcelUtil.downloadExcelWithGramdTotal(response, UserAdoptionReportVO.class, userAdoptionReport, "", "ActivitiesReportByUser.xlsx", true);
    }

    @Override
    public UserActiveDurationStatistic getLastWeekActiveDurationUserInfo(GetLastWeekActiveDurationUserInfoDTO dto) {
        UserActiveDurationStatistic ret = new UserActiveDurationStatistic();
        ret.setAverage(getActiveDurationThreshold());
        Map<Long, Long> lastWeekBelow = new HashMap<>();
        if(dto.getUserIds() == null) {
            lastWeekBelow = SecurityUtils.getTeamId() == null ? new HashMap<>() : getLastWeekBelowAverageActiveDurationUserByTeamId(SecurityUtils.getTenantId(), List.of(SecurityUtils.getTeamId()));
        } else {
            lastWeekBelow = getLastWeekBelowAverageActiveDurationUserByUserId(SecurityUtils.getTenantId(), dto.getUserIds());
        }
        ret.setLastWeekBelowAverageCount(lastWeekBelow.keySet().size());
        ret.setLastWeekBelowAverage(lastWeekBelow);
        return ret;
    }


    private void setP1ExcelGrandTotal(List<PipelineAnalyticsVO> pipelineAnalyticsVOList) {
        Set<Long> appliedProcessIdSet = new HashSet<>();
        Set<Long> submittedProcessIdSet = new HashSet<>();
        Set<Long> updateSubmittedProcessIdSet = new HashSet<>();
        Set<Long> interviewProcessIdSet = new HashSet<>();
        Set<Long> offeredProcessIdSet = new HashSet<>();
        Set<Long> offerAcceptedProcessIdSet = new HashSet<>();
        Set<Long> startedProcessIdSet = new HashSet<>();
        pipelineAnalyticsVOList.forEach(o -> {
//            if (ObjectUtil.isNotEmpty(o.getAppliedActivityId())) {
//                Set<Long> itemAppliedProcessIdSet = Arrays.stream(o.getAppliedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                appliedProcessIdSet.addAll(itemAppliedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getSubmittedActivityId())) {
//                Set<Long> itemSubmittedProcessIdSet = Arrays.stream(o.getSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                submittedProcessIdSet.addAll(itemSubmittedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getPipelineUpdateSubmittedActivityId())) {
//                Set<Long> itemUpdateSubmittedProcessIdSet = Arrays.stream(o.getPipelineUpdateSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                updateSubmittedProcessIdSet.addAll(itemUpdateSubmittedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getInterviewActivityId())) {
//                Set<Long> itemInterviewProcessIdSet = Arrays.stream(o.getInterviewActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                interviewProcessIdSet.addAll(itemInterviewProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getOfferedActivityId())) {
//                Set<Long> itemOfferedProcessIdSet = Arrays.stream(o.getOfferedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                offeredProcessIdSet.addAll(itemOfferedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getOfferAcceptedActivityId())) {
//                Set<Long> itemOfferAcceptedProcessIdSet = Arrays.stream(o.getOfferAcceptedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                offerAcceptedProcessIdSet.addAll(itemOfferAcceptedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getStartedActivityId())) {
//                Set<Long> itemStartedProcessIdSet = Arrays.stream(o.getStartedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                startedProcessIdSet.addAll(itemStartedProcessIdSet);
//            }
        });

        PipelineAnalyticsVO grandTotal = new PipelineAnalyticsVO();
        grandTotal.setUserName(GRAND_TOTAL);
        grandTotal.setAppliedCount(appliedProcessIdSet.size());
        grandTotal.setSubmittedCount(submittedProcessIdSet.size());
        grandTotal.setPipelineUpdateSubmittedCount(updateSubmittedProcessIdSet.size());
        grandTotal.setInterviewCount(interviewProcessIdSet.size());
        grandTotal.setOfferedCount(offeredProcessIdSet.size());
        grandTotal.setOfferAcceptedCount(offerAcceptedProcessIdSet.size());
        grandTotal.setStartedCount(startedProcessIdSet.size());
        pipelineAnalyticsVOList.add(grandTotal);
    }

}
