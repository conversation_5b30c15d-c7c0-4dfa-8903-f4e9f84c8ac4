package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchUserDto;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.KpiReportByUserStayedOver;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.service.timezone.EnumTimezoneService;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.dto.RecruitingKpiApplicationDetailSearchDto;
import com.altomni.apn.report.util.MapToEntityUtil;
import liquibase.pro.packaged.S;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Repository
public class RecruitingKpiBaseRepository extends BaseCustomRepository {

    @Resource
    protected EnumTimezoneService enumTimezoneService;

    protected void setListWithPage(Pageable pageable, Map<String, Object> whereParamMap, Query query) {
        Optional.of(whereParamMap).ifPresent(m -> m.forEach(query::setParameter));
        if (pageable == null) {
            return;
        }
        query.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
        query.setMaxResults(pageable.getPageSize());
    }

    protected void appendApplicationDetailStayedOver(RecruitingKpiApplicationDetailSearchDto searchDto, StringBuilder sb, Integer hour) {
        if (null != searchDto.getDetail() && StringUtils.isNotBlank(searchDto.getDetail().getStayedOver())) {
            if (searchDto.getDetail().getStayedOver().equals("1")) {
                if(searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_JOB)){
                    if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                ") > ").append(hour);
                    }else{
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=10," +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                "," +
                                "(select case when pn.node_type=20 then sc.created_date\n" +
                                "when pn.node_type=30 then vi.created_date \n" +
                                "when pn.node_type=40 then er.created_date\n" +
                                "when pn.node_type=41 then ofa.created_date \n" +
                                "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                                " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                                "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 10  order by node_type asc limit 1))) > ").append(hour);
                    }
                } else  if(searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_CLIENT)){
                    if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                ") > ").append(hour);
                    }else{
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=20," +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                "," +
                                "(select case when pn.node_type=20 then sc.created_date\n" +
                                "when pn.node_type=30 then vi.created_date \n" +
                                "when pn.node_type=40 then er.created_date\n" +
                                "when pn.node_type=41 then ofa.created_date \n" +
                                "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                                " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                                "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 20  order by node_type asc limit 1))) > ").append(hour);
                    }
                }
            } else {
                if(searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_JOB)){
                    if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                ") < ").append(hour);
                    }else{
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=10," +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                "," +
                                "(select case when pn.node_type=20 then sc.created_date\n" +
                                "when pn.node_type=30 then vi.created_date \n" +
                                "when pn.node_type=40 then er.created_date\n" +
                                "when pn.node_type=41 then ofa.created_date \n" +
                                "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                                " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                                "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 10  order by node_type asc limit 1))) < ").append(hour);
                    }
                } else  if(searchDto.getReportApplicationStatus().equals(ReportApplicationStatus.SUBMIT_TO_CLIENT)){
                    if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                ") < ").append(hour);
                    }else{
                        sb.append(" and TIMESTAMPDIFF(HOUR, node.created_date, " +
                                " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=20," +
                                "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                                "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                                "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                                "," +
                                "(select case when pn.node_type=20 then sc.created_date\n" +
                                "when pn.node_type=30 then vi.created_date \n" +
                                "when pn.node_type=40 then er.created_date\n" +
                                "when pn.node_type=41 then ofa.created_date \n" +
                                "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                                " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                                "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 20  order by node_type asc limit 1))) < ").append(hour);
                    }
                }
            }
        }
    }

    protected void appendDetailCommonCondition(Long companyId, Long jobId, Long userId, Long teamId, StringBuilder sb, Map<String, Object> whereParamMap) {
        if (ObjectUtil.isNotEmpty(companyId)) {
            sb.append(" and c.id = :companyIdOnly ");
            whereParamMap.put("companyIdOnly", companyId);
        }
        if (ObjectUtil.isNotEmpty(jobId)) {
            sb.append(" and j.id = :jobIdOnly ");
            whereParamMap.put("jobIdOnly", jobId);
        }
        if (ObjectUtil.isNotEmpty(userId)) {
            sb.append(" and put.user_id = :userId ");
            whereParamMap.put("userId", userId);
        }
        if (ObjectUtil.isNotEmpty(teamId)) {
            sb.append(" and put.team_id = :teamId ");
            whereParamMap.put("teamId", teamId);
        }
    }

    public String getColumnSuffix(String timezone) {
        Map<String, String> map = enumTimezoneService.getColumnSuffix();
        return map.getOrDefault(timezone, null);
    }


    /**
     * 根据入参来获取 join tables
     * @param searchDto 分组字段
     * @param searchTableAlias 查询表别名
     * @return
     */
    protected String getJoinTablesByUser(RecruitingKpiReportSearchDto searchDto, String searchTableAlias, ReportTableType reportTableType) {
        List<RecruitingKpiGroupByFieldType> groupByFieldList = searchDto.getGroupByFieldList();
        String timezone = searchDto.getTimezone();
        RecruitingKpiDateType dateType = searchDto.getDateType();
        return getCurrentNodeType(reportTableType, dateType) + groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case DAY, WEEK, MONTH, QUARTER, YEAR -> {
                    String dateField = "";
                    switch (dateType) {
                        case ADD -> dateField = getTimezoneField(searchTableAlias, timezone, "created_date");
                        case EVENT -> {
                            if (reportTableType == ReportTableType.SUBMIT_TO_CLIENT) {
                                dateField = searchTableAlias + ".submit_time_format";
                            } else if (reportTableType == ReportTableType.INTERVIEW) {
                                dateField = getTimezoneField(searchTableAlias, timezone, "from_time");
                            } else if (reportTableType == ReportTableType.ON_BOARD) {
                                dateField = "trpod.onboard_date";
                            } else {
                                dateField = getTimezoneField(searchTableAlias, timezone, "created_date");
                            }
                        }
                    }
                    return  " inner join date_dimension d on d.date = " + dateField + " ";
                }
                default -> { return "";}
            }
        }).collect(Collectors.joining(" ")) + getJoinTableByUser(searchDto, searchTableAlias);
    }

    /**
     * 根据入参来获取 kpi user join
     * @param reportTableType
     * @return
     */
    protected String getKpiUserJoinTablesByUser(ReportTableType reportTableType) {
        if (reportTableType == null) {
            return " ";
        }
        return switch (reportTableType) {
            case SUBMIT_TO_JOB -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (10,-2) ";
            case SUBMIT_TO_CLIENT -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (20,-2) ";
            case OFFER -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (40,-2) ";
            case OFFER_ACCEPT -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (41,-2)";
            case ON_BOARD,RESIGNED->  " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (60,-2)";
            case ELIMINATED -> " inner join talent_recruitment_process_user_relation ul on ul.talent_recruitment_process_id = trp.id and ul.node_type in (-1,-2)";
            case INTERVIEW -> " inner join talent_recruitment_process_user_relation ul on (\n" +
                    "(ul.talent_recruitment_process_id = trp.id and ul.node_type in (30) and node.id = ul.node_id) or (ul.talent_recruitment_process_id = trp.id and ul.node_type in (-2)))";
        };
    }

    protected String getJoinTableByUser(RecruitingKpiReportSearchDto searchDto, String searchTableAlias) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(searchDto.getJob())
                || ObjectUtil.isNotEmpty(searchDto.getCompany())
                || ObjectUtil.isNotEmpty(searchDto.getUser())) {
            if (Objects.equals(searchTableAlias, "t") || Objects.equals(searchTableAlias, "tn")) {
                // talent 和 talent_note 做主表示,需要多关联一张 流程表
                sb.append("""
                left join talent_recruitment_process trp on trp.talent_id = t.id
                left join job j on j.id = trp.job_id
                left join company c on c.id = j.company_id
                """);
            }
            if (Objects.equals(searchTableAlias, "tn")) {
                sb.append("""
                left join talent_recruitment_process_kpi_user ul on ul.talent_recruitment_process_id = trp.id
                """);
            }
            getJoinTablesByJobOrCompany(searchDto, sb);
        }
        return sb.toString();
    }

    protected String getCurrentNodeType(ReportTableType reportTableType, RecruitingKpiDateType dateType) {
        if (reportTableType == null) {
            return " ";
        }
        return switch (reportTableType) {
            case SUBMIT_TO_JOB -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 10 and trpn.node_status =1 ";
            case SUBMIT_TO_CLIENT -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 20 and trpn.node_status =1 ";
            case OFFER -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 40 and trpn.node_status = 1 ";
            case OFFER_ACCEPT -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 41 and trpn.node_status = 1 ";
            case ON_BOARD, RESIGNED -> (dateType == RecruitingKpiDateType.EVENT? " inner join talent_recruitment_process_onboard_date trpod on trp.id = trpod.talent_recruitment_process_id ": "") +
                    " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 60 and trpn.node_status = 1 ";
            case ELIMINATED -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_status = 4 ";
            case INTERVIEW -> " left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 30 and trpn.node_status = 1 ";
        };
    }

    protected String getCurrentNodeTypeForCurrentByApplicationStatus(ReportApplicationStatus applicationStatus) {
        if (applicationStatus == null) {
            return " ";
        }
        return switch (applicationStatus) {
            case SUBMIT_TO_JOB -> " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 10 and trpn.node_status = 1 ";
            case SUBMIT_TO_CLIENT -> " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 20 and trpn.node_status = 1 ";
            case OFFER -> " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 40 and trpn.node_status = 1 ";
            case OFFER_ACCEPT -> " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 41 and trpn.node_status = 1 ";
            case ON_BOARD -> " inner join talent_recruitment_process_onboard_date trpod on trp.id = trpod.talent_recruitment_process_id " +
                    " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_type = 60 and trpn.node_status = 1 ";
            case INTERVIEW, INTERVIEW_FIRST, INTERVIEW_SECOND ,INTERVIEW_APPOINTMENTS,TWO_OR_MORE_INTERVIEW, INTERVIEW_FINAL ->
                    "  INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id and trpn.node_type = 30 AND trpn.node_status = 1 ";
            case ELIMINATED -> " inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_status = 4 ";
        };
    }

    protected List<? extends ReportField> getSelectFieldsForTalentOrApplicationOlap(RecruitingKpiReportSearchDto searchDto, ReportTableType reportTableType) {
        List<? extends ReportField> baseFields = searchDto.getGroupByFieldList().stream()
            .flatMap(groupByFieldType -> switch (groupByFieldType) {
                case USER -> Stream.of(ColumnField.ofAutoAlias("ul.user_id"), FunctionField.of("CONCAT(u.first_name, ' ', u.last_name)", "user_name"));
                case TEAM -> Stream.of(ColumnField.ofAutoAlias("put.team_id"), ColumnField.of("pt.name", "team_name"));
                case DAY -> Stream.of(ColumnField.groupByDate("d.date"));
                case WEEK -> Stream.of(ColumnField.groupByDate("d.start_of_week"));
                case MONTH -> Stream.of(FunctionField.of("DATE_FORMAT(d.start_of_month, '%Y-%m' )", "group_by_date"));
                case QUARTER -> Stream.of(ColumnField.groupByDate("d.quarter_of_year"));
                case YEAR -> Stream.of(ColumnField.groupByDate("d.year"));
                default -> Stream.empty();
            })
            .toList();
        
        // Add currentCountNum field if needed
        if (reportTableType != null && !List.of(ReportTableType.INTERVIEW).contains(reportTableType)) {
            List<? extends ReportField> appendBaseFields= Stream.concat(
                baseFields.stream(),
                Stream.of(FunctionField.of("count(DISTINCT CASE WHEN trpn.id IS NOT NULL THEN node.id END)", "currentCountNum", true),
                        FunctionField.of("count(distinct case when trp.ai_score is not null and trpn.id IS NOT NULL then trp.id else null end )","currentAiRecommendNum",true),
                        FunctionField.of("count(distinct case when rf.id is not null and trpn.id IS NOT NULL then trp.id else null end )","currentPrecisionAiRecommendNum",true))
            ).toList();

            if (reportTableType != null && List.of(ReportTableType.SUBMIT_TO_JOB).contains(reportTableType)) {
                return Stream.concat(
                        appendBaseFields.stream(),
                        Stream.of(FunctionField.of(" GROUP_CONCAT(DISTINCT case when TIMESTAMPDIFF(HOUR, node.created_date, if(trpn.id IS NOT NULL," +
                                        "now()" +
                                        ",(select case when x.createTime is null then pe.created_date else x.createTime end from (\n" +
                                        "select  case when pn.node_type=20 then sc.created_date\n" +
                                        "when pn.node_type=30 then vi.created_date \n" +
                                        "when pn.node_type=40 then er.created_date\n" +
                                        "when pn.node_type=41 then ofa.created_date \n" +
                                        "when pn.node_type=60 then bo.created_date else null end \n" +
                                        "as createTime,pn.talent_recruitment_process_id from talent_recruitment_process_node pn \n" +
                                        " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                        " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t\n" +
                                        "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type >10  order by node_type asc limit 1\n" +
                                        ") x \n" +
                                        "left join talent_recruitment_process_eliminate pe on x.talent_recruitment_process_id = pe.talent_recruitment_process_id)" +
                                        ")) > 24 then 1 else null end ) ", "stayedOver", true),
                                FunctionField.of(" GROUP_CONCAT(DISTINCT if(trpn.id IS NOT NULL,case when TIMESTAMPDIFF(HOUR, node.created_date,NOW()) > 24 then 1 else null end,null)) ", "currentStayedOver", true))
                ).toList();
            } else if (reportTableType != null && List.of(ReportTableType.SUBMIT_TO_CLIENT).contains(reportTableType)) {
                return Stream.concat(
                        appendBaseFields.stream(),
                        Stream.of(FunctionField.of(" GROUP_CONCAT(DISTINCT case when TIMESTAMPDIFF(HOUR, node.created_date, if(trpn.id IS NOT NULL," +
                                        "now()" +
                                        ",(select case when x.createTime is null then pe.created_date else x.createTime end from (\n" +
                                        "select  case when pn.node_type=20 then sc.created_date\n" +
                                        "when pn.node_type=30 then vi.created_date \n" +
                                        "when pn.node_type=40 then er.created_date\n" +
                                        "when pn.node_type=41 then ofa.created_date \n" +
                                        "when pn.node_type=60 then bo.created_date else null end \n" +
                                        "as createTime,pn.talent_recruitment_process_id from talent_recruitment_process_node pn \n" +
                                        " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                                        " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                                        " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t\n" +
                                        "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type >20  order by node_type asc limit 1\n" +
                                        ") x \n" +
                                        "left join talent_recruitment_process_eliminate pe on x.talent_recruitment_process_id = pe.talent_recruitment_process_id)" +
                                        ")) > 72 then 1 else null end ) ", "stayedOver", true),
                                FunctionField.of(" GROUP_CONCAT(DISTINCT if(trpn.id IS NOT NULL,case when TIMESTAMPDIFF(HOUR, node.created_date, NOW()) > 72 then 1 else null end,null)) ", "currentStayedOver", true))
                ).toList();
            }
            return appendBaseFields;
        }
        
        return baseFields;
    }

    protected String getTempGroupByFieldForJob(List<? extends ReportField> selectTempFields) {
        return appendGroupByFields("", selectTempFields);
    }

    protected String getSelectFieldsForJob(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case DAY -> { return " d.date group_by_date, ";}
                case WEEK -> { return " d.start_of_week group_by_date, ";}
                case MONTH -> { return "  DATE_FORMAT(d.start_of_month, '%Y-%m' ) group_by_date, ";}
                case QUARTER -> { return " d.quarter_of_year group_by_date, ";}
                case YEAR -> { return " d.year group_by_date, ";}
                default -> { return "";}
            }
        }).collect(Collectors.joining(" "));
    }

    /**
     * 获取时间字段
     * @param searchTableAlias 别名，talent t, talent_note tn, 流程 node, job j
     * @param timezone 用户时区
     * @param column   使用的字段
     * @return
     */
    protected String getTimezoneField(String searchTableAlias, String timezone, String column) {
        String dateField = searchTableAlias + "." + column;
        String suffix = getColumnSuffix(timezone);
        if (StrUtil.isBlank(suffix)) {
            //走实时计算
            if (Objects.equals(column, "from_time")) {
                return " DATE_FORMAT(CONVERT_TZ(from_time_utc, 'UTC', '" + timezone + "'), '%Y-%m-%d') ";
            } else {
                return " DATE_FORMAT(CONVERT_TZ(" + dateField + ", 'UTC', '" + timezone + "'), '%Y-%m-%d') ";
            }
        } else {
            return dateField + "_" + suffix;
        }
    }

    protected void getJoinTablesForJobByJobOrCompanyOrTalent(RecruitingKpiReportSearchDto searchDto, StringBuilder sb) {
        if (searchDto.searchUserNotEmpty()) {
            sb.append(" left join talent t on t.id = trp.talent_id ");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            sb.append(" inner join company c on c.id = j.company_id ");
        }
        getJoinTablesByJobOrCompany(searchDto, sb);
    }


    protected void getJoinTablesForJobColumn(RecruitingKpiReportSearchDto searchDto, StringBuilder sb) {
        RecruitingKpiDateType dateType = searchDto.getDateType();
        String timezone = searchDto.getTimezone();
        String dateField;
        if (RecruitingKpiDateType.ADD.equals(dateType)) {
            dateField = getTimezoneField("j", timezone, "created_date");
        } else {
            dateField = "j.start_date_format";
        }
        sb.append(" inner join date_dimension d on d.date = ").append(dateField).append(" ");
    }


    protected void getJoinTablesByJobOrCompany(RecruitingKpiReportSearchDto searchDto, StringBuilder sb) {
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && StrUtil.isNotBlank(searchDto.getJob().getCountry())) {
            sb.append(" left JOIN job_location jl ON jl.job_id = j.id ");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) &&  CollUtil.isNotEmpty(searchDto.getJob().getJobFunctions())) {
            sb.append(" left join job_job_function_relation jjfr on jjfr.job_id = j.id ");
        }
        if ((ObjectUtil.isNotEmpty(searchDto.getCompany()) && CollUtil.isNotEmpty(searchDto.getCompany().getIndustries()))) {
            sb.append(" left join company_industry_relation cir on cir.company_id = c.id ");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ");
        }
    }

    protected String getWhereClauseForJob(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        //通用搜索
        appendDateSearchForJob(sb, searchDto, param);
        //search job
        appendSearchJobForJobOrDetail(sb, searchDto, param);
        //search company
        appendCompanySearch(sb, searchDto, param);
        //search user
        appendUserActiveStatus(sb, searchDto.getUser(), param);
        //add job permission
        appendJobDataPermissionForJobOrDetail(sb, searchDto, param, teamDTO);
        return sb.toString();
    }

    protected void appendUserActiveStatus(StringBuilder sb, SearchUserDto searchUserDto, Map<String, Object> param){
        this.appendUserActiveStatus("u", sb, searchUserDto, param);
    }

    protected void appendUserActiveStatus(String userAlias, StringBuilder sb, SearchUserDto searchUserDto, Map<String, Object> param){
        if (ObjectUtil.isNotEmpty(searchUserDto) && ObjectUtil.isNotEmpty(searchUserDto.getUserActiveStatus())){
            sb.append(" and ").append(userAlias).append(".activated =:activated ");
            param.put("activated", searchUserDto.getUserActiveStatus());
        }
    }

    protected void appendJobDataPermissionForJobOrDetail(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, TeamDataPermissionRespDTO teamDTO) {
        //权限
        param.put("privateJobIds", searchDto.getPrivateJobIds());
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            param.put("puserId", searchDto.getSearchUserId());
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                sb.append(" and ((ul.user_id = :puserId and j.pteam_id != :teamIdForPrivateJob )or j.id in :privateJobIds ) ");
                param.put("teamIdForPrivateJob", teamDTO.getTeamIdForPrivateJob());
            } else {
                sb.append(" and (ul.user_id = :puserId or j.id in :privateJobIds ) ");
            }
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            sb.append(" and (put.team_id in :teamIds or j.id in :privateJobIds ) ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        } else {
            //全部
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                sb.append(" AND (j.pteam_id != :teamIdForPrivateJob or j.id in :privateJobIds ) ");
                param.put("teamIdForPrivateJob", teamDTO.getTeamIdForPrivateJob());
            } else {
                sb.append(" AND (j.pteam_id is not null or j.id in :privateJobIds) ");
            }
        }
    }

    protected void appendSearchJobForJobOrDetail(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        //job search
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && StrUtil.isNotBlank(searchDto.getJob().getTitle())) {
            sb.append(" and j.title like :jobTitle ");
            param.put("jobTitle", "%" + searchDto.getJob().getTitle() + "%");
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getStatusList())) {
            sb.append(" and j.status in :jobStatusList ");
            param.put("jobStatusList", searchDto.getJob().getStatusList().stream().map(JobStatus::toDbValue).toList());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && StrUtil.isNotBlank(searchDto.getJob().getCountry())) {
            sb.append(" and jl.official_country = :jobCountry  ");
            param.put("jobCountry", searchDto.getJob().getCountry());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getJobFunctions())) {
            sb.append(" and jjfr.job_function_id in :jobFunctionIds ");
            param.put("jobFunctionIds", searchDto.getJob().getJobFunctions());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getIndustries())) {
            sb.append(" and cir.industry_id in :industryIds ");
            param.put("industryIds", searchDto.getJob().getIndustries());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
            sb.append(" and rp.job_type in :jobType ");
            param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && CollUtil.isNotEmpty(searchDto.getJob().getUserIdList())) {
            sb.append(" and j.puser_id in :jobUserIdList ");
            param.put("jobUserIdList", searchDto.getJob().getUserIdList());
        }
        if (ObjectUtil.isNotEmpty(searchDto.getJob()) && ObjectUtil.isNotEmpty(searchDto.getJob().getTeamIdList())) {
            sb.append(" and j.pteam_id in :jobTeamIds ");
            param.put("jobTeamIds", searchDto.getJob().getTeamIdList());
        }
    }

    protected void appendDateSearchForJob(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (searchDto.getStartDate() == null && searchDto.getEndDate() == null) {
            return;
        }
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        boolean groupDateFlag = shouldJoinDateDimension(searchDto);
        if (searchDto.getDateType() == RecruitingKpiDateType.ADD) {
            //add 时间
            if (StrUtil.isNotBlank(timezoneColumn)) {
                //已经固化的时区,直接 d.date filter
                if (BooleanUtil.isFalse(groupDateFlag)) {
                    param.put("jobStartDate", searchDto.getStartDateUtc());
                    param.put("jobEndDate", searchDto.getEndDateUtc());
                    sb.append(" and j.created_date BETWEEN :jobStartDate AND :jobEndDate ");
                } else {
                    param.put("jobStartDate", searchDto.getStartDate());
                    param.put("jobEndDate", searchDto.getEndDate());
                    sb.append(" and d.date BETWEEN :jobStartDate AND :jobEndDate ");
                }
            } else {
                param.put("jobStartDate", searchDto.getStartDateUtc());
                param.put("jobEndDate", searchDto.getEndDateUtc());
                sb.append(" and j.created_date BETWEEN :jobStartDate AND :jobEndDate ");
            }
        } else {
            //event
            param.put("jobStartDate", searchDto.getStartDate());
            param.put("jobEndDate", searchDto.getEndDate());
            sb.append(" and j.start_date_format BETWEEN :jobStartDate AND :jobEndDate ");
        }
        appendCommonUserIdAndTeamId(searchDto, sb, param);
    }

    protected void appendJobIdForJob(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (searchDto.getJob() != null) {
            if (CollUtil.isNotEmpty(searchDto.getJob().getJobIdList())) {
                param.put("jobId", searchDto.getJob().getJobIdList());
                sb.append(" and j.id in :jobId ");
            }

            if (CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
                sb.append(" and rp.job_type in :jobType ");
                param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
            }
        }
    }

    protected void appendDateSearchForJobDetail(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        sb.append(" and j.tenant_id = :tenantId ");
        param.put("tenantId", searchDto.getSearchTenantId());
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        if (searchDto.getDateType() == RecruitingKpiDateType.ADD) {
            //add 时间
            if (StrUtil.isNotBlank(timezoneColumn)) {
                //已经固化的时区,直接 d.date filter
                param.put("jobStartDate", searchDto.getStartDateUtc());
                param.put("jobEndDate", searchDto.getEndDateUtc());
                sb.append(" and j.created_date_").append(timezoneColumn).append(" BETWEEN :jobStartDate AND :jobEndDate ");
            } else {
                param.put("jobStartDate", searchDto.getStartDateUtc());
                param.put("jobEndDate", searchDto.getEndDateUtc());
                sb.append(" and j.created_date BETWEEN :jobStartDate AND :jobEndDate ");
            }
        } else {
            //event
            param.put("jobStartDate", searchDto.getStartDate());
            param.put("jobEndDate", searchDto.getEndDate());
            sb.append(" and j.start_date_format BETWEEN :jobStartDate AND :jobEndDate ");
        }
        appendCommonUserIdAndTeamId(searchDto, sb, param);
    }

    protected void appendCommonUserIdAndTeamId(RecruitingKpiReportSearchDto searchDto, StringBuilder sb, Map<String, Object> param) {
        if (CollUtil.isNotEmpty(searchDto.getUserIdList()) && CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
            sb.append(" and ( put.user_id in :userIdList or put.team_id in :teamIdList ) ");
            param.put("userIdList", searchDto.getUserIdList());
            param.put("teamIdList", searchDto.getTeamIdList());
        } else {
            if (CollUtil.isNotEmpty(searchDto.getUserIdList())) {
                sb.append(" and put.user_id in :userIdList ");
                param.put("userIdList", searchDto.getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getTeamIdList())) {
                sb.append(" and put.team_id in :teamIdList ");
                param.put("teamIdList", searchDto.getTeamIdList());
            }
        }
    }

    protected String getWhereClauseForTalent(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param) {
        StringBuilder sb = new StringBuilder();
        //时间查询
        appendDateSearchForTalent(sb, searchDto, param);
        //条件查询和权限控制
        appendSearchConditionAndPermissionForTalent(sb, searchDto, param, teamDTO);
        appendUserActiveStatus(sb, searchDto.getUser(), param);
        return sb.toString();
    }

    protected String getWhereClauseForApplication(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO, Map<String, Object> param, ReportTableType reportTableType) {
        StringBuilder sb = new StringBuilder();
        appendDateSearchForApplication(sb, searchDto, param, reportTableType);
        appendSearchConditionAndPermission(sb, searchDto, param, teamDTO);
        appendJobDataPermissionForJobOrDetail(sb, searchDto, param, teamDTO);
        appendUserActiveStatus(sb, searchDto.getUser(), param);
        return sb.toString();
    }

    protected void appendSearchConditionAndPermission(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, TeamDataPermissionRespDTO teamDTO) {
        //talent search
        appendTalentSearch(sb, searchDto, param);
        //job search
        appendJobSearch(sb, searchDto, param);
        //company search
        appendCompanySearch(sb, searchDto, param);
        //data permission
//        appendDatePermission(sb, teamDTO, searchDto, param);
    }

    protected void appendSearchConditionAndPermissionForTalent(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, TeamDataPermissionRespDTO teamDTO) {
        //job search
        appendJobSearch(sb, searchDto, param);
        //company search
        appendCompanySearch(sb, searchDto, param);
        //data permission
        appendDatePermission(sb, teamDTO, searchDto, param);
    }

    protected void appendDateSearchForApplication(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param, ReportTableType reportTableType) {
        appendApplicationDateSearchForApplication(searchDto, sb, param, reportTableType);
        appendCommonUserIdAndTeamId(searchDto, sb, param);
    }

    protected void appendApplicationDateSearchForApplication(RecruitingKpiReportSearchDto searchDto, StringBuilder sb, Map<String, Object> param, ReportTableType reportTableType) {
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        boolean groupDateFlag = shouldJoinDateDimension(searchDto);
        if (searchDto.getDateType() == RecruitingKpiDateType.ADD) {
            if (StrUtil.isNotBlank(timezoneColumn)) {
                //已经固化的时区,直接 d.date filter
                if (BooleanUtil.isFalse(groupDateFlag)) {
                    param.put("startDate", searchDto.getStartDateUtc());
                    param.put("endDate", searchDto.getEndDateUtc());
                    sb.append(" and node.created_date BETWEEN :startDate AND :endDate ");
                } else {
                    param.put("startDate", searchDto.getStartDate());
                    param.put("endDate", searchDto.getEndDate());
                    sb.append(" and d.date BETWEEN :startDate AND :endDate ");
                }
            } else {
                param.put("startDate", searchDto.getStartDateUtc());
                param.put("endDate", searchDto.getEndDateUtc());
                sb.append(" and node.created_date BETWEEN :startDate AND :endDate ");
            }
        } else {
            String fieldColunm = "";
            if (reportTableType == ReportTableType.SUBMIT_TO_CLIENT) {
                param.put("startDate", searchDto.getStartDate());
                param.put("endDate", searchDto.getEndDate());
                fieldColunm = " DATE_FORMAT(CONVERT_TZ(node.submit_time, 'UTC', '" + searchDto.getTimezone() + "'), '%Y-%m-%d') ";
            } else if (reportTableType == ReportTableType.INTERVIEW) {
                param.put("startDate", searchDto.getStartDate());
                param.put("endDate", searchDto.getEndDate());
                fieldColunm = getTimezoneField("node", searchDto.getTimezone(), "from_time");
            } else if (reportTableType == ReportTableType.ON_BOARD) {
                param.put("startDate", searchDto.getStartDate());
                param.put("endDate", searchDto.getEndDate());
                fieldColunm = "trpod.onboard_date";
            } else {
                if (BooleanUtil.isTrue(groupDateFlag)) {
                    param.put("startDate", searchDto.getStartDate());
                    param.put("endDate", searchDto.getEndDate());
                    fieldColunm = "d.date";
                } else {
                    param.put("startDate", searchDto.getStartDateUtc());
                    param.put("endDate", searchDto.getEndDateUtc());
                    fieldColunm = "node.created_date";
                }
            }
            sb.append(" and ").append(fieldColunm).append(" BETWEEN :startDate AND :endDate ");
        }
    }

    protected void appendDatePermission(StringBuilder sb, TeamDataPermissionRespDTO teamDTO, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and ul.user_id = :userId ");
            param.put("userId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append(" and put.team_id in :teamIds ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        }
    }

    protected void appendDateSearchForTalent(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        appendTalentDateSearchForTalent(searchDto, sb, param);
        appendCommonUserIdAndTeamId(searchDto, sb, param);
    }

    protected void appendTalentDateSearchForTalent(RecruitingKpiReportSearchDto searchDto, StringBuilder sb, Map<String, Object> param) {
        String timezoneColumn = getColumnSuffix(searchDto.getTimezone());
        boolean groupDateFlag = shouldJoinDateDimension(searchDto);
        if (StrUtil.isNotBlank(timezoneColumn)) {
            //已经固化的时区,直接 d.date filter
            if (BooleanUtil.isFalse(groupDateFlag)) {
                param.put("startDate", searchDto.getStartDateUtc());
                param.put("endDate", searchDto.getEndDateUtc());
                sb.append(" and t.created_date BETWEEN :startDate AND :endDate ");
            } else {
                param.put("startDate", searchDto.getStartDate());
                param.put("endDate", searchDto.getEndDate());
                sb.append(" and d.date BETWEEN :startDate AND :endDate ");
            }
        } else {
            param.put("startDate", searchDto.getStartDateUtc());
            param.put("endDate", searchDto.getEndDateUtc());
            sb.append(" and t.created_date BETWEEN :startDate AND :endDate ");
        }
    }

    protected void appendTalentSearch(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (ObjectUtil.isNotEmpty(searchDto.getUser())) {
            if (CollUtil.isNotEmpty(searchDto.getUser().getUserIdList())) {
                sb.append(" and t.puser_id in :talentUserIdList ");
                param.put("talentUserIdList",searchDto.getUser().getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getUser().getTeamIdList())) {
                sb.append(" and t.pteam_id in :talentTeamIdList ");
                param.put("talentTeamIdList",searchDto.getUser().getTeamIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getUser().getUserRoleList())) {
                sb.append(" and ul.user_role in :talentUserRoleList ");
                if(null !=searchDto.getUser().getProcessOperator() && searchDto.getUser().getProcessOperator()){
                    List<Integer> userRoleList = getTalentUserRoleList(searchDto.getUser().getUserRoleList());
                    List<Integer> ab = new ArrayList<>();
                    if (null != userRoleList) {
                        ab.addAll(userRoleList);
                    }
                    ab.add(10);
                    param.put("talentUserRoleList", ab);
                }else {
                    param.put("talentUserRoleList", getTalentUserRoleList(searchDto.getUser().getUserRoleList()));
                }
            } else {
                if (null != searchDto.getUser().getProcessOperator() && searchDto.getUser().getProcessOperator()) {
                    sb.append(" and ul.user_role in :talentUserRoleList ");
                    List<Integer> userRoleList = new ArrayList<>();
                    userRoleList.add(10);
                    param.put("talentUserRoleList", userRoleList);
                } else {
                    //因为操作角色增加了流程操作者，只有选择角色才会查询，所有前端不传递值时默认查询kpi user的角色数据
                    sb.append(" and ul.user_role in :talentUserRoleList ");
                    param.put("talentUserRoleList", defaultUserRole());
                }
            }
        } else {
            //因为操作角色增加了流程操作者，只有选择角色才会查询，所有前端不传递值时默认查询kpi user的角色数据
            sb.append(" and ul.user_role in :talentUserRoleList ");
            param.put("talentUserRoleList", defaultUserRole());
        }
    }

    private List<Integer> defaultUserRole() {
        List<Integer> ALL_USER_ROLES = new ArrayList<>();
        ALL_USER_ROLES.add(-1);
        List<UserRole> excludeUserRole = Arrays.asList(UserRole.PR,UserRole.NODE_CREATED_BY);
        for (UserRole userRole : UserRole.values()) {
            if (!excludeUserRole.contains(userRole)) {
                ALL_USER_ROLES.add(userRole.toDbValue());
            }
        }
        return ALL_USER_ROLES;
    }

    protected void appendTalentSearchForApplication(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (ObjectUtil.isNotEmpty(searchDto.getUser())) {
            if (CollUtil.isNotEmpty(searchDto.getUser().getUserIdList())) {
                sb.append(" and t.puser_id in :talentUserIdList ");
                param.put("talentUserIdList",searchDto.getUser().getUserIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getUser().getTeamIdList())) {
                sb.append(" and t.pteam_id in :talentTeamIdList ");
                param.put("talentTeamIdList",searchDto.getUser().getTeamIdList());
            }
        }
    }

    protected void appendJobSearch(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (ObjectUtil.isNotEmpty(searchDto.getJob())) {
            if (StrUtil.isNotBlank(searchDto.getJob().getTitle())) {
                sb.append(" and j.title like :jobTitle ");
                param.put("jobTitle", "%" + searchDto.getJob().getTitle() + "%");
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getStatusList())) {
                sb.append(" and j.status in :jobStatusList ");
                param.put("jobStatusList", searchDto.getJob().getStatusList().stream().map(JobStatus::toDbValue).toList());
            }
            if (StrUtil.isNotBlank(searchDto.getJob().getCountry())) {
                sb.append("""
                         and jl.official_country = :jobCountry
                        """);
                param.put("jobCountry", searchDto.getJob().getCountry());
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getIndustries())) {
                sb.append("""
                         and cir.industry_id in :jobIndustryList
                        """);
                param.put("jobIndustryList", searchDto.getJob().getIndustries());
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getJobFunctions())) {
                sb.append("""
                         and jjfr.job_function_id in :jobFunctionList
                        """);
                param.put("jobFunctionList", searchDto.getJob().getJobFunctions());
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getTypeList())) {
                sb.append("""
                         and rp.job_type in :jobType
                        """);
                param.put("jobType", searchDto.getJob().getTypeList().stream().map(JobType::toDbValue).toList());
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getUserIdList())) {
                sb.append(" and (j.puser_id in :jobUserIdList or j.id in :privateJobIds ) ");
                param.put("jobUserIdList", searchDto.getJob().getUserIdList());
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            }
            if (CollUtil.isNotEmpty(searchDto.getJob().getTeamIdList())) {
                sb.append(" and (j.pteam_id in :jobTeamIdList or j.id in :privateJobIds ) ");
                param.put("jobTeamIdList", searchDto.getJob().getTeamIdList());
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            }
        }
    }

    protected void appendCompanySearch(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (Objects.isNull(searchDto.getCompany()) && (Objects.isNull(searchDto.getUser()) || CollectionUtils.isEmpty(searchDto.getUser().getSalesLeadRoleList()))){
            return;
        }
        sb.append(" and exists ( select 1 from company ca ");
        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            // table
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIndustries())) {
                sb.append(" inner join company_industry_relation cir on cir.company_id = ca.id ");
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany()) && (null !=searchDto.getCompany().getCountry() && !searchDto.getCompany().getCountry().isEmpty())) {
                sb.append(" inner join company_location cl on cl.company_id = ca.id ");
            }
        }

        if (ObjectUtil.isNotEmpty(searchDto.getUser()) && CollUtil.isNotEmpty(searchDto.getUser().getSalesLeadRoleList())) {
            sb.append(" inner join business_flow_administrator bfa on bfa.company_id = ca.id ");
        }

        sb.append( " where ca.id = c.id ");

        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIdList())) {
                sb.append(" and ca.id in :companyIdList ");
                param.put("companyIdList", searchDto.getCompany().getIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIndustries())) {
                sb.append(" and cir.industry_id in :companyIndustries ");
                param.put("companyIndustries", searchDto.getCompany().getIndustries());
            }
            if (CollUtil.isNotEmpty(searchDto.getCompany().getCompanyStatuses())) {
                sb.append(" AND ca.active in :companyStatus  ");
                param.put("companyStatus", searchDto.getCompany().getCompanyStatuses().stream().map(AccountCompanyStatus::toDbValue).toList());
            }
            if (null !=searchDto.getCompany().getCountry() && !searchDto.getCompany().getCountry().isEmpty()) {
                sb.append(" and cl.official_country in :country ");
                param.put("country", searchDto.getCompany().getCountry());
            }

            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateEndDate())) {
                sb.append(" and ca.created_date between :createStartDate and :createEndDate ");
                param.put("createStartDate", getUtcByTimeZone(searchDto.getCompany().getCreateStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("createEndDate", getUtcByTimeZone(searchDto.getCompany().getCreateEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestEndDate())) {
                sb.append(" and ca.request_date between :requestStartDate and :requestEndDate ");
                param.put("requestStartDate", getUtcByTimeZone(searchDto.getCompany().getRequestStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("requestEndDate", getUtcByTimeZone(searchDto.getCompany().getRequestEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
        }
        if (ObjectUtil.isNotEmpty(searchDto.getUser()) && CollUtil.isNotEmpty(searchDto.getUser().getSalesLeadRoleList()) && CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            sb.append(" and bfa.sales_lead_role in :salesLeadRole ");
            param.put("salesLeadRole", searchDto.getUser().getSalesLeadRoleList().stream().map(SalesLeadRoleType::toDbValue).toList());
            sb.append(" and bfa.user_id in  (select ur.id from user ur where ur.id in :roleUserIds and ur.activated in :activated_list) ");
            param.put("roleUserIds", searchDto.getUserIdList());
            param.put("activated_list", (Objects.nonNull(searchDto.getUser()) && Objects.nonNull(searchDto.getUser().getUserActiveStatus()))
                    ? List.of(searchDto.getUser().getUserActiveStatus()) : List.of(true, false));
        }

        sb.append(" ) ");

        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateEndDate())) {
                sb.append(" and c.created_date between :createStartDate and :createEndDate ");
                param.put("createStartDate", getUtcByTimeZone(searchDto.getCompany().getCreateStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("createEndDate", getUtcByTimeZone(searchDto.getCompany().getCreateEndDate() + " 23:59:59", searchDto.getTimezone()));
            }

            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestEndDate())) {
                sb.append(" and c.request_date between :requestStartDate and :requestEndDate ");
                param.put("requestStartDate", getUtcByTimeZone(searchDto.getCompany().getRequestStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("requestEndDate", getUtcByTimeZone(searchDto.getCompany().getRequestEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
        }
    }

    protected void appendCompanySearchByTishWeek(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        if (Objects.isNull(searchDto.getCompany()) && (Objects.isNull(searchDto.getUser()) || CollectionUtils.isEmpty(searchDto.getUser().getSalesLeadRoleList()))){
            return;
        }
        sb.append(" and exists ( select 1 from company ca ");
        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            // table
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIndustries())) {
                sb.append(" inner join company_industry_relation cir on cir.company_id = ca.id ");
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany()) && (null !=searchDto.getCompany().getCountry() && !searchDto.getCompany().getCountry().isEmpty())) {
                sb.append(" inner join company_location cl on cl.company_id = ca.id ");
            }
        }

        if (ObjectUtil.isNotEmpty(searchDto.getUser()) && CollUtil.isNotEmpty(searchDto.getUser().getSalesLeadRoleList())) {
            sb.append(" inner join business_flow_administrator bfa on bfa.company_id = ca.id ");
        }

        sb.append( " where ca.id = c.id ");

        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIdList())) {
                sb.append(" and ca.id in :companyIdList ");
                param.put("companyIdList", searchDto.getCompany().getIdList());
            }
            if (CollUtil.isNotEmpty(searchDto.getCompany().getIndustries())) {
                sb.append(" and cir.industry_id in :companyIndustries ");
                param.put("companyIndustries", searchDto.getCompany().getIndustries());
            }
            if (null !=searchDto.getCompany().getCountry() && !searchDto.getCompany().getCountry().isEmpty()) {
                sb.append(" and cl.official_country in :country ");
                param.put("country", searchDto.getCompany().getCountry());
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateEndDate())) {
                sb.append(" and ca.created_date between :createStartDate and :createEndDate ");
                param.put("createStartDate", getUtcByTimeZone(searchDto.getCompany().getCreateStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("createEndDate", getUtcByTimeZone(searchDto.getCompany().getCreateEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestEndDate())) {
                sb.append(" and ca.request_date between :requestStartDate and :requestEndDate ");
                param.put("requestStartDate", getUtcByTimeZone(searchDto.getCompany().getRequestStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("requestEndDate", getUtcByTimeZone(searchDto.getCompany().getRequestEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
        }
        if (ObjectUtil.isNotEmpty(searchDto.getUser()) && CollUtil.isNotEmpty(searchDto.getUser().getSalesLeadRoleList()) && CollUtil.isNotEmpty(searchDto.getUserIdList())) {
            sb.append(" and bfa.sales_lead_role in :salesLeadRole ");
            param.put("salesLeadRole", searchDto.getUser().getSalesLeadRoleList().stream().map(SalesLeadRoleType::toDbValue).toList());
            sb.append(" and bfa.user_id in  (select ur.id from user ur where ur.id in :roleUserIds and ur.activated in :activated_list) ");
            param.put("roleUserIds", searchDto.getUserIdList());
            param.put("activated_list", (Objects.nonNull(searchDto.getUser()) && Objects.nonNull(searchDto.getUser().getUserActiveStatus()))
                    ? List.of(searchDto.getUser().getUserActiveStatus()) : List.of(true, false));
        }

        sb.append(" ) ");

        if (ObjectUtil.isNotEmpty(searchDto.getCompany())) {
            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getCreateEndDate())) {
                sb.append(" and c.created_date between :createStartDate and :createEndDate ");
                param.put("createStartDate", getUtcByTimeZone(searchDto.getCompany().getCreateStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("createEndDate", getUtcByTimeZone(searchDto.getCompany().getCreateEndDate() + " 23:59:59", searchDto.getTimezone()));
            }

            if (ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestStartDate()) && ObjectUtil.isNotEmpty(searchDto.getCompany().getRequestEndDate())) {
                sb.append(" and c.request_date between :requestStartDate and :requestEndDate ");
                param.put("requestStartDate", getUtcByTimeZone(searchDto.getCompany().getRequestStartDate() + " 00:00:00", searchDto.getTimezone()));
                param.put("requestEndDate", getUtcByTimeZone(searchDto.getCompany().getRequestEndDate() + " 23:59:59", searchDto.getTimezone()));
            }
        }

        if (CollUtil.isNotEmpty(searchDto.getCompany().getCompanyStatuses())) {
            sb.append(" AND c.active in :companyStatus  ");
            param.put("companyStatus", searchDto.getCompany().getCompanyStatuses().stream().map(AccountCompanyStatus::toDbValue).toList());
        }
    }

    public String getUtcByTimeZone(String time, String timezone) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).toString();
    }


    protected List<Integer> getTalentUserRoleList(List<UserRole> userRoleList) {
        return userRoleList.stream().map(UserRole::toDbValue).filter(ObjectUtil::isNotNull).toList();
    }

    /**
     * 获取group by字段
     * @param groupByFieldList
     * @return
     */
    protected String getGroupByFieldThisWeek(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        List<String> result = groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case JOB -> { return " j.id, j.title "; }
                case COMPANY -> { return "c.id , c.full_business_name  "; }
                case USER -> { return " u.id ,user_name ";}
                case TEAM -> { return "  put.team_id , pt.name  ";}
                default -> { return "";}
            }
        }).toList();
        List<String> notNullResult = result.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        String groupByField = CollUtil.isNotEmpty(notNullResult)? String.join(" , ", notNullResult) + "," : " ";
        return "group by " + groupByField;
    }

    /**
     * 获取group by字段
     * @param groupByFieldList
     * @return
     */
    protected String getGroupByField(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return "group by " + groupByFieldList.stream().map(groupByFieldType -> {
            switch (groupByFieldType) {
                case JOB -> { return "j.id"; }
                case COMPANY -> { return "c.id"; }
                case USER -> { return " ul.user_id ";}
                case TEAM -> { return " put.team_id ";}
                case DAY -> { return " d.date ";}
                case WEEK -> { return " d.start_of_week ";}
                case MONTH -> { return " d.start_of_month ";}
                case QUARTER -> { return " d.quarter_of_year ";}
                case YEAR -> { return " d.year ";}
                default -> { return null;}
            }
        }).collect(Collectors.joining(" , "));
    }
    protected List<? extends ReportField> getGroupByFieldOlap(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return groupByFieldList.stream()
            .flatMap(groupByFieldType -> switch (groupByFieldType) {
                case JOB -> Stream.of(ColumnField.ofAutoAlias("j.id"));
                case COMPANY -> Stream.of(ColumnField.ofAutoAlias("c.id"));
                case USER -> Stream.of(ColumnField.ofAutoAlias("ul.user_id"));
                case TEAM -> Stream.of(ColumnField.ofAutoAlias("put.team_id"));
                case DAY -> Stream.of(ColumnField.ofAutoAlias("d.date"));
                case WEEK -> Stream.of(ColumnField.ofAutoAlias("d.start_of_week"));
                case MONTH -> Stream.of(ColumnField.ofAutoAlias("d.start_of_month"));
                case QUARTER -> Stream.of(ColumnField.ofAutoAlias("d.quarter_of_year"));
                case YEAR -> Stream.of(ColumnField.ofAutoAlias("d.year"));
            })
            .toList();
    }

    protected <T> List<T> getClassList(String sql, Map<String, String> map, Map<String, Object> param, Class<T> clazz) {
        String formatSql = StrUtil.format(sql, map);
        System.out.println(formatSql);
        for (String key : map.keySet()) {
            System.out.println(key + " = " + map.get(key));
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        return MapToEntityUtil.convertEntity(mapList, clazz);
    }

    protected <T> List<T> getClassList2(String sql, Map<String, String> map, Map<String, Object> param, Class<T> clazz, String name) {
        String formatSql = StrUtil.format(sql, map);
        System.out.println("--------------------");
        System.out.println(name);
        System.out.println(formatSql);
        System.out.println();
        System.out.println(param.toString());
        System.out.println("--------------------");
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        return MapToEntityUtil.convertEntity(mapList, clazz);
    }

    protected boolean shouldJoinDateDimension(RecruitingKpiReportSearchDto searchDto) {
        return Stream.of(RecruitingKpiGroupByFieldType.DAY, RecruitingKpiGroupByFieldType.WEEK, RecruitingKpiGroupByFieldType.MONTH, RecruitingKpiGroupByFieldType.QUARTER, RecruitingKpiGroupByFieldType.YEAR)
                .anyMatch(searchDto.getGroupByFieldList()::contains);
    }

    /**
     * 不同流程的主表不一致
     * @param submitToJob
     * @return
     */
    protected String getFromTable(ReportTableType submitToJob) {
        return submitToJob.getDbTableName();
    }

    protected void appendTeamLimitByPrivateJob(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        TeamDataPermissionRespDTO teamDTO = searchDto.getPermissionRespDTO();
        if (teamDTO == null) return;

        boolean hasPrivateJobs = CollUtil.isNotEmpty(searchDto.getPrivateJobIds());
        Long teamIdForPrivateJob = teamDTO.getTeamIdForPrivateJob();

        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            // 自己
            if (hasPrivateJobs) {
                sb.append(" AND (ul.user_id = :puserId OR j.id IN :privateJobIds) ");
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            } else {
                sb.append(" AND ul.user_id = :puserId ");
            }
            param.put("puserId", searchDto.getSearchUserId());
            return;
        }

        if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            // 团队权限
            if (hasPrivateJobs) {
                sb.append(" AND (put.team_id IN :teamIds OR j.id IN :privateJobIds) ");
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            } else {
                sb.append(" AND put.team_id IN :teamIds ");
            }
            param.put("teamIds", teamDTO.getNestedTeamIds());
            return;
        }

        // 全部权限
        if (teamIdForPrivateJob != null) {
            if (hasPrivateJobs) {
                sb.append(" AND (NOT EXISTS (SELECT 1 FROM job jo WHERE jo.pteam_id = :teamIdForPrivateJob AND jo.id = j.id) ");
                sb.append(" OR j.id IN :privateJobIds) ");
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            } else {
                sb.append(" AND NOT EXISTS (SELECT 1 FROM job jo WHERE jo.pteam_id = :teamIdForPrivateJob AND jo.id = j.id) ");
            }
            param.put("teamIdForPrivateJob", teamIdForPrivateJob);
        } else {
            if (hasPrivateJobs) {
                sb.append(" AND (j.pteam_id IS NOT NULL OR j.id IN :privateJobIds) ");
                param.put("privateJobIds", searchDto.getPrivateJobIds());
            } else {
                sb.append(" AND j.pteam_id IS NOT NULL ");
            }
        }
    }

    protected void appendTeamLimitByCompany(StringBuilder sb, RecruitingKpiReportSearchDto searchDto, Map<String, Object> param) {
        TeamDataPermissionRespDTO teamDTO = searchDto.getPermissionRespDTO();
        if (teamDTO == null) {
            return;
        }
        param.put("privateJobIds", searchDto.getPrivateJobIds());
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            param.put("puserId", searchDto.getSearchUserId());
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                sb.append(" and ((ul.user_id = :puserId and j.pteam_id != :teamIdForPrivateJob )or j.id in :privateJobIds ) ");
                param.put("teamIdForPrivateJob", teamDTO.getTeamIdForPrivateJob());
            } else {
                sb.append(" and (ul.user_id = :puserId or j.id in :privateJobIds ) ");
            }
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            sb.append(" and (put.team_id in :teamIds or j.id in :privateJobIds ) ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        } else {
            //全部
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                sb.append(" AND (j.pteam_id != :teamIdForPrivateJob or j.id in :privateJobIds) ");
                param.put("teamIdForPrivateJob", teamDTO.getTeamIdForPrivateJob());
            } else {
                sb.append(" AND (j.pteam_id is not null or j.id in :privateJobIds) ");
            }
        }
    }

    protected static String appendGroupByFields(String expression, List<? extends ReportField> reportFields) {
        if (reportFields == null || reportFields.isEmpty()) {
            return expression;
        }

        StringBuilder result = new StringBuilder(expression);
        boolean needsComma = !expression.isEmpty() && !expression.trim().endsWith(",");

        for (ReportField field : reportFields) {
            String groupByExpression = field.toGroupByExpression();
            if (groupByExpression == null || groupByExpression.isBlank()) {
                continue;
            }
            if (needsComma) {
                result.append(", ");
            }
            result.append(groupByExpression);
            needsComma = true;
        }
        return result.toString();
    }

    protected static String reportFieldsToExpression(List<? extends ReportField> reportFields) {
        if (CollUtil.isEmpty(reportFields)) {
            return "";
        }
        return reportFields.stream().map(ReportField::toSelectExpression).collect(Collectors.joining(", "));
    }

    protected interface ReportField {
        String toSelectExpression();
        String toGroupByExpression();
        String alias();
    }

    protected record ColumnField(String table, String column, String alias) implements ReportField {

        static ColumnField groupByDate(String expression) {
                return of(expression, "group_by_date");
        }

        static ColumnField ofAutoAlias(String expression) {
            String[] arr = parseExpression(expression);
            return new ColumnField(arr[0], arr[1], arr[1]);
        }

        static ColumnField of(String expression, String alias) {
            String[] arr = parseExpression(expression);
            return new ColumnField(arr[0], arr[1], alias);
        }

        private static String[] parseExpression(String expression) {
            if (expression == null || expression.isBlank() || !expression.contains(".")) {
                throw new IllegalArgumentException("expression must not be null or blank or invalid");
            }
            String[] arr = expression.split("\\.");
            if (arr.length != 2) {
                throw new IllegalArgumentException("expression must be in format 'table.column'");
            }
            return arr;
        }

        @Override
        public String toSelectExpression() {
            return table + "." + column + " AS " + alias;
        }

        @Override
        public String toGroupByExpression() {
            return table + "." + column;
        }
    }

    protected record FunctionField(String functionExpression, String alias, Boolean aggregate) implements ReportField {

        static FunctionField of(String functionExpression, String alias) {
            return new FunctionField(functionExpression, alias, false);
        }


        static FunctionField of(String functionExpression, String alias, Boolean aggregate) {
            return new FunctionField(functionExpression, alias, aggregate);
        }

        @Override
        public String toSelectExpression() {
            return functionExpression + " AS " + alias;
        }

        @Override
        public String toGroupByExpression() {
            if (Boolean.TRUE.equals(aggregate)) {
                return "";
            }
            return alias;
        }
    }

}
