package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.domain.enumeration.start.StartType;
import com.altomni.apn.report.domain.enumeration.ReportSalesType;
import com.altomni.apn.report.domain.vo.ReportSalesCompanyVo;
import com.altomni.apn.report.domain.vo.ReportSalesDetailVo;
import com.altomni.apn.report.domain.vo.ReportSalesViewStatsVo;
import com.altomni.apn.report.dto.ReportSalesParamDto;
import com.altomni.apn.report.dto.SaleDetailDTO;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportSalesService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.user.domain.user.Team;
import liquibase.util.BooleanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.report.domain.enumeration.StartReportStatus.OFFER_ACCEPTED;

@Slf4j
@Service("reportSalesService")
public class ReportSalesServiceImpl extends ReportBaseServiceImpl implements ReportSalesService {

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    protected InitiationService initiationService;

    @Override
    public List<ReportSalesCompanyVo> salesCompanyFilter(ReportSalesParamDto reportParam) {
        reportParam.validateWeekScope();
        log.info("[apn] salesCompanyFilter start ......");
        StopWatch stopWatch = new StopWatch("salesCompanyFilter");
        stopWatch.start("searchCompanyFilterTask");
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> conditionMap = new HashMap<>(16);
        createCompanyFilterSql(sb, reportParam, conditionMap);
        String sql = sb.toString();
        log.info("[apn] salesCompanyFilter create sql = [{}]", sql);
        List<ReportSalesCompanyVo> companyVoList = searchData(sql, ReportSalesCompanyVo.class, conditionMap);
        stopWatch.stop();
        log.info("[apn] salesCompanyFilter time = [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] salesCompanyFilter end ......");
        return companyVoList;
    }

    @Override
    public List<ReportSalesViewStatsVo> searchSales(ReportSalesParamDto reportParam) {
        log.info("[apn] searchSales start ......");
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        reportParam.setTeamDataPermission(teamDataPermission);
        StopWatch stopWatch = new StopWatch("searchSales");
        stopWatch.start("searchTask");
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> conditionMap = new HashMap<>(16);
        createSearchSalesByTypeSql(sb, reportParam, conditionMap);
        String sql = sb.toString();
        log.info("[apn] searchSales sql = [{}]", sql);
        List<Map<String, Object>> mapList = searchDataWithMap(sql, conditionMap);
        stopWatch.stop();
        stopWatch.start("convertTask");
        List<ReportSalesViewStatsVo> voList = convertEntity(mapList, ReportSalesViewStatsVo.class);
        stopWatch.stop();
        log.info("[apn] searchSales time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] searchSales end ......");
        return voList;
    }

    @Override
    public List<ReportSalesViewStatsVo> salesByWeeklyNewOffer(ReportSalesParamDto reportParam) {
        reportParam.validateWeekScope();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        reportParam.setTeamDataPermission(teamDataPermission);
        log.info("[apn] salesByWeeklyNewOffer start ......");
        StopWatch stopWatch = new StopWatch("salesByWeeklyNewOffer");
        stopWatch.start("searchSalesByWeeklyNewOfferTask");
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> conditionMap = new HashMap<>(16);
        createSearchSalesByWeekSql(sb, reportParam, conditionMap);
        String sql = sb.toString();
        log.info("[apn] salesByWeeklyNewOffer sql = [{}]", sql);
        List<Map<String, Object>> mapList = searchDataWithMap(sql, conditionMap);
        stopWatch.stop();
        stopWatch.start("convertSalesByWeekTask");
        List<ReportSalesViewStatsVo> voList = limit26Vo(convertEntity(mapList, ReportSalesViewStatsVo.class));
        limit26Vo(voList);
        stopWatch.stop();
        log.info("[apn] searchSales time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] searchSales end ......");
        return voList;
    }

    private List<ReportSalesViewStatsVo> limit26Vo(List<ReportSalesViewStatsVo> voList) {
        if (CollUtil.isEmpty(voList)) {
            return voList;
        }
        return CollUtil.reverse(voList).stream().limit(26).collect(Collectors.toList());
    }

    @Override
    public List<ReportSalesDetailVo> getSalesDetails(SaleDetailDTO saleDetailDTO) {
        log.info("[apn] getSalesDetails start ......");
        StopWatch stopWatch = new StopWatch("getSalesDetails");
        stopWatch.start("createSalesDetailsSqlTask");
        checkParam(saleDetailDTO);
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        createSalesDetailsSql(saleDetailDTO, map, sb);
        String sql = sb.toString();
        log.info("[apn] getSalesDetails sql = [{}]", sql);
        stopWatch.stop();
        stopWatch.start("searchGetSalesDetailsTask");
        List<Map<String, Object>> mapList = searchDataWithMap(sql, map);
        List<ReportSalesDetailVo> voList = convertSalesDetailEntity(mapList, saleDetailDTO);
        stopWatch.stop();
        log.info("[apn] getSalesDetails time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] getSalesDetails end ......");
        return voList;
    }

    @Override
    public void exportSalesFteDetailsExcel(HttpServletResponse response, SaleDetailDTO saleDetailDTO) {
        List<ReportSalesDetailVo> voList = getSalesDetails(saleDetailDTO)
                .stream()
                .filter(vo -> !saleDetailDTO.getIsNeedUpdateStatusFlag() || (ObjectUtil.isNotEmpty(vo.getIsNeedUpdateStatusFlag()) && vo.getIsNeedUpdateStatusFlag()))
                .map(d -> d.setStatus(BooleanUtils.isTrue(d.getResigned()) ? NodeType.OFF_BOARDED.name() : d.getStatus())).toList();
        ExcelUtil.downloadExcel(response, ReportSalesDetailVo.class, voList, "", "", true);
    }

    private List<ReportSalesDetailVo> convertSalesDetailEntity(List<Map<String, Object>> mapList, SaleDetailDTO dto) {
        List<ReportSalesDetailVo> detailVoList = JSON.parseArray(JSON.toJSONString(mapList), ReportSalesDetailVo.class);
        Map<Long, List<ReportSalesDetailVo>> longListMap = detailVoList.stream().collect(Collectors.groupingBy(ReportSalesDetailVo::getId));
        List<ReportSalesDetailVo> voList = new ArrayList<>();
        for (Map.Entry<Long, List<ReportSalesDetailVo>> longListEntry : longListMap.entrySet()) {
            for (ReportSalesDetailVo vo : longListEntry.getValue()) {
                setName(vo);
                if (Objects.equals(OFFER_ACCEPTED, vo.getType())) {
                    setReportSalesDetailByOfferAccepted(vo, voList, dto);
                } else {
                    setReportSalesDetailByOnBoarded(vo, List.of(vo), dto, voList);
                }
            }
        }
        if (CollUtil.isNotEmpty(voList)) {
            Set<Long> privateJobTeamIds = reportRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
            List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
            Map<Integer, EnumCurrency> currencyMap = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
            EnumCurrency enumCurrencyUSD = enumCurrencyService.findEnumCurrencyById(CurrencyConstants.USD);
            voList.forEach(reportSalesDetailVo -> {
                EnumCurrency enumCurrency = currencyMap.get(reportSalesDetailVo.getCurrency());
                if (CurrencyConstants.NON_CHINA.equals(dto.getCountry())) {
                    enumCurrency = enumCurrencyUSD;
                }
                reportSalesDetailVo.setTotalBillAmountFormat(enumCurrency.getLabel1() + reportSalesDetailVo.getTotalBillAmount());
                reportSalesDetailVo.setRevenueFormat(enumCurrency.getLabel1() + reportSalesDetailVo.getRevenue());
                reportSalesDetailVo.setPrivateJob(privateJobTeamIds.contains(reportSalesDetailVo.getJobPteamId()));
                if (BooleanUtils.isTrue(reportSalesDetailVo.getConvertedToFte())){
                    reportSalesDetailVo.setResigned(Boolean.FALSE);
                }
                // 标记
                reportSalesDetailVo.setUpdateStatusFlag();
            });
        }
        return voList;
    }

    private void setReportSalesDetailByOnBoarded(ReportSalesDetailVo vo, List<ReportSalesDetailVo> v, SaleDetailDTO dto, List<ReportSalesDetailVo> voList) {
        if (Objects.equals(vo.getStatus(), StartStatus.ACTIVE.toDbValue() + "")
                || Objects.equals(vo.getStatus(), StartStatus.CONTRACT_TERMINATED.toDbValue() + "")
                || Objects.equals(vo.getStatus(), StartStatus.CONTRACT_EXTENDED.toDbValue() + "")) {
            setReportSalesDetailStartStatus(vo);
            if (Objects.equals(vo.getJobType(), JobType.CONTRACT) || JobType.PAY_ROLL.equals(vo.getJobType()) || JobType.MSP.equals(vo.getJobType())) {
                BigDecimal totalRevenue = v.stream().filter(detail -> ObjectUtil.isNotNull(detail.getSStartDate()) && ObjectUtil.isNotNull(detail.getFinalBillRate()) && ObjectUtil.isNotNull(detail.getSEndDate())).map(detail ->
                    CommonUtils.calculateRevenue(detail.getFinalBillRate(), detail.getRateUnitType(), detail.getSStartDate(), detail.getSEndDate(), detail.getEstimatedWorkingHourPerWeek())
                ).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalBillAmount = v.stream().filter(detail -> ObjectUtil.isNotNull(detail.getSStartDate()) && ObjectUtil.isNotEmpty(detail.getTotalBillAmount()) && ObjectUtil.isNotNull(detail.getSEndDate())).map(ReportSalesDetailVo::getTotalBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setRevenue(totalRevenue.setScale(2, RoundingMode.HALF_UP));
                if (new BigDecimal(0).compareTo(totalRevenue) != 0) {
                    vo.setPercentageOfGpRevenue(totalBillAmount.divide(totalRevenue, 4, RoundingMode.HALF_UP));
                }
                vo.setTotalBillAmount(totalBillAmount.setScale(2, RoundingMode.HALF_UP));
            } else if (JobType.FULL_TIME.equals(vo.getJobType())) {
                vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate(): null);
                if (vo.getTotalBillAmount() != null) {
                    vo.setTotalBillAmount(vo.getTotalBillAmount().setScale(2, RoundingMode.HALF_UP));
                    vo.setRevenue(vo.getTotalBillAmount());
                    if (new BigDecimal(0).compareTo(vo.getTotalBillAmount()) != 0) {
                        vo.setPercentageOfGpRevenue(vo.getTotalBillAmount().divide(vo.getTotalBillAmount(), 4, RoundingMode.HALF_UP));
                    }
                }
            }
            if(vo.getStartType() == StartType.CONVERT_TO_FTE) {
                vo.setEndDate(null);
            }
        } else if (Objects.equals(vo.getStatus(), StartStatus.FTE_FAIL_WARRANTY.toDbValue() + "")) {
            // Failed to warranty 的前端要显示 Onbaord (已离职)
            vo.setStatus(ONBOARD);
            vo.setResigned(Boolean.TRUE);
            vo.setStartDate(vo.getStartDate() != null ? vo.getStartDate(): null);
            vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate(): null);
            if (vo.getTotalBillAmount() !=  null) {
                vo.setTotalBillAmount(vo.getTotalBillAmount().setScale(2, RoundingMode.HALF_UP));
                vo.setTotalBillAmount(vo.getTotalBillAmount());
                vo.setRevenue(vo.getTotalBillAmount());
                if (new BigDecimal(0).compareTo(vo.getTotalBillAmount()) != 0) {
                    vo.setPercentageOfGpRevenue(vo.getTotalBillAmount().divide(vo.getTotalBillAmount(), 4, RoundingMode.HALF_UP));
                }
            }
        }
        convertCurrency(vo, dto);
        voList.add(vo);
    }

    private void setReportSalesDetailByOfferAccepted(ReportSalesDetailVo vo, List<ReportSalesDetailVo> voList, SaleDetailDTO dto) {
        vo.setStatus(OFFER_ACCEPTED);
        vo.setTotalBillAmount(vo.getTotalBillAmount().setScale(2, RoundingMode.HALF_UP));
        if (JobType.CONTRACT.equals(vo.getJobType()) || JobType.PAY_ROLL.equals(vo.getJobType()) || JobType.MSP.equals(vo.getJobType())) {
            BigDecimal revenue = BigDecimal.ZERO;
            if (vo.getRateUnitType() != null) {
                revenue = CommonUtils.calculateRevenue(vo.getFinalBillRate(), vo.getRateUnitType(), vo.getSStartDate(), vo.getSEndDate(), vo.getEstimatedWorkingHourPerWeek());
            }
            if (new BigDecimal(0).compareTo(revenue) != 0) {
                vo.setPercentageOfGpRevenue(vo.getTotalBillAmount().divide(revenue, 4, RoundingMode.HALF_UP));
            }
            vo.setRevenue(revenue.setScale(2, RoundingMode.HALF_UP));
        } else if (JobType.FULL_TIME.equals(vo.getJobType())) {
            vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate(): null);
            vo.setRevenue(vo.getTotalBillAmount());
            if (new BigDecimal(0).compareTo(vo.getTotalBillAmount()) != 0) {
                vo.setPercentageOfGpRevenue(vo.getTotalBillAmount().divide(vo.getTotalBillAmount(), 4, RoundingMode.HALF_UP));
            }
        }
        convertCurrency(vo, dto);
        voList.add(vo);
    }

    private void convertCurrency(ReportSalesDetailVo vo, SaleDetailDTO dto) {
        if (CurrencyConstants.NON_CHINA.equals(dto.getCountry())) {
            if (vo.getTotalBillAmount() != null) {
                BigDecimal totalBillAmount = vo.getTotalBillAmount().multiply(vo.getFromUsdRate());
                vo.setTotalBillAmount(totalBillAmount.setScale(2, RoundingMode.HALF_UP));
            }
            if (vo.getRevenue() != null) {
                BigDecimal revenue = vo.getRevenue().multiply(vo.getFromUsdRate());
                vo.setRevenue(revenue.setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    private void setReportSalesDetailStartStatus(ReportSalesDetailVo vo) {
        if(Objects.equals(StartStatus.CONTRACT_TERMINATED.toDbValue() + "", vo.getStatus())) {
            vo.setStatus(TERMINATION);
        } else if (Objects.equals(StartType.CONTRACT_EXTENSION, vo.getStartType())) {
            vo.setStatus(EXTENTION);
        } else {
            vo.setStatus(ONBOARD);
        }
    }

    private void setName(ReportSalesDetailVo detail) {
        if (StrUtil.isNotBlank(detail.getAm())) {
            String amNameStr = detail.getAm();
            detail.setAm(Arrays.stream(amNameStr.split(",")).map(am -> {
                String[] amNames = am.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(amNames[0], amNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getRecruiter())) {
            String recruiterNameStr = detail.getRecruiter();
            detail.setRecruiter(Arrays.stream(recruiterNameStr.split(",")).map(recruiter -> {
                String[] recruiterNames = recruiter.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(recruiterNames[0], recruiterNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getSourcer())) {
            String sourcerNameStr = detail.getSourcer();
            detail.setSourcer(Arrays.stream(sourcerNameStr.split(",")).map(sourcer -> {
                String[] sourcerNames = sourcer.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(sourcerNames[0], sourcerNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getDm())) {
            String dmNameStr = detail.getDm();
            detail.setDm(Arrays.stream(dmNameStr.split(",")).map(dm -> {
                String[] dmNames = dm.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(dmNames[0], dmNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getCoAm())) {
            String coAmNameStr = detail.getCoAm();
            detail.setCoAm(Arrays.stream(coAmNameStr.split(",")).map(coAm -> {
                String[] coAmNames = coAm.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(coAmNames[0], coAmNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getAc())) {
            String acNameStr = detail.getAc();
            detail.setAc(Arrays.stream(acNameStr.split(",")).map(ac -> {
                String[] acNames = ac.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(acNames[0], acNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getSalesLeadOwner())) {
            String salesLeadOwnerNameStr = detail.getSalesLeadOwner();
            detail.setSalesLeadOwner(Arrays.stream(salesLeadOwnerNameStr.split(",")).map(salesLeadOwner -> {
                String[] salesLeadOwnerNames = salesLeadOwner.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(salesLeadOwnerNames[0], salesLeadOwnerNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getBdOwner())) {
            String bdOwnerNameStr = detail.getBdOwner();
            detail.setBdOwner(Arrays.stream(bdOwnerNameStr.split(",")).map(bdOwner -> {
                String[] bdOwnerNames = bdOwner.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(bdOwnerNames[0], bdOwnerNames[1]);
            }).collect(Collectors.joining(",")));
        }
        if (StrUtil.isNotBlank(detail.getOwner())) {
            String ownerNameStr = detail.getOwner();
            detail.setOwner(Arrays.stream(ownerNameStr.split(",")).map(owner -> {
                String[] ownerNames = owner.split("_");
                return CommonUtils.formatFullNameWithBlankCheck(ownerNames[0], ownerNames[1]);
            }).collect(Collectors.joining(",")));
        }
    }

    private void createSalesDetailsSql(SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder sb) {
        StringBuilder amAndRecruiterAndSourcerByOfferAcceptedSql = new StringBuilder();
        amAndRecruiterAndSourcerByOfferAcceptedSql
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 0 ) as am, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 1 ) as recruiter, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 2 ) as sourcer, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 3 ) as dm, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 7 ) as co_am, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 5 ) as ac, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 9 ) as sales_lead_owner, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 8 ) as bd_owner, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = trp.id and tu.user_role = 4 ) as owner, ");
        StringBuilder offerAcceptedContactSql = new StringBuilder();
        createSalesDetailOfferAcceptedContractSql(offerAcceptedContactSql, amAndRecruiterAndSourcerByOfferAcceptedSql, saleDetailDTO, map, sb);
        map = new HashMap<>(32);
        StringBuilder offerAcceptedFteSql = new StringBuilder();
        createSalesDetailOfferAcceptedFteSql(offerAcceptedFteSql, amAndRecruiterAndSourcerByOfferAcceptedSql, saleDetailDTO, map, sb);
        map = new HashMap<>(32);
        StringBuilder amAndRecruiterAndSourcerOnBoardedSql = new StringBuilder();
        amAndRecruiterAndSourcerOnBoardedSql
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 0 ) as am, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 1 ) as recruiter, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 2 ) as sourcer, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 3 ) as dm, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 7 ) as co_am, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 5 ) as ac, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 9 ) as sales_lead_owner, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 8 ) as bd_owner, ")
                .append(" (select GROUP_CONCAT(CONCAT(u.first_name,'_',u.last_name)) from talent_recruitment_process_kpi_user tu inner join user u on u.id = tu.user_id where tu.talent_recruitment_process_id = s.talent_recruitment_process_id and tu.user_role = 4 ) as owner, ");
        StringBuilder onboardedContractSql = new StringBuilder();
        createSalesDetailOnBoardedContractSql(onboardedContractSql, amAndRecruiterAndSourcerOnBoardedSql, saleDetailDTO, map, sb);
        map = new HashMap<>(16);
        StringBuilder onboardedFteSql = new StringBuilder();
        createSalesDetailOnBoardedFteSql(onboardedFteSql, amAndRecruiterAndSourcerOnBoardedSql, saleDetailDTO, map, sb);
    }

    private void createSalesDetailOfferAcceptedFteSql(StringBuilder offerAcceptedSql, StringBuilder amAndRecruiterAndSourcerSql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder sb) {
        offerAcceptedSql.append(" select trp.id as id, trpicfc.total_amount as total_bill_amount, t.id AS talent_id, t.full_name, j.id AS job_id, j.title, '' as work_location, j.pteam_id as job_pteam_id, c.id AS company_id, c.`full_business_name` as company_name, null as start_type, null as `status`, ")
                .append(amAndRecruiterAndSourcerSql)
                .append(" trpod.onboard_date as start_date, trpod.end_date as end_date, 'Offer Accepted' as type, ec.from_usd_rate, ")
                .append(" trpod.onboard_date as s_start_date, trpod.end_date as s_end_date, trpod.warranty_end_date, 0.0 as final_pay_rate, 0.0 as final_bill_rate, 0.0 as estimated_working_hour_per_week, rp.job_type AS job_type, trpod.rate_unit_type, ")
                .append(" trpod.currency currency, ")
                .append(" trpo.created_date onboard_date, (case CONCAT(onu.first_name,onu.last_name) regexp '[一-龥]' when 1 then CONCAT(onu.last_name,onu.first_name) ELSE CONCAT(onu.first_name, \" \",onu.last_name) END) onboard_by, ")
                .append(" abi.assignment_division job_division, ac.zip_code zip_code, u.custom_timezone, if(resign.id is null, false, true) as resigned, if(star.id is null, false, true) as converted_to_fte ")
                .append(" FROM talent_recruitment_process trp ")
                .append(" INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id ")
                .append(" INNER JOIN talent_recruitment_process_offer_fee_charge trpicfc ON trp.id = trpicfc.talent_recruitment_process_id ")
                .append(" INNER JOIN talent_recruitment_process_onboard_date trpod ON trpod.talent_recruitment_process_id = trp.id ")
                .append(" INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id ")
                .append(" left JOIN talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = trp.id ")
                .append(" left JOIN talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = trp.id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join user onu on onu.id = trpo.puser_id ")
                .append(" left join timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id ")
                .append(" left join assignment_bill_info abi on abi.assignment_id = tta.id ")
                .append(" left join assignment_location ac on ac.assignment_id = tta.id ")
                .append(" INNER JOIN talent t ON t.id = trp.talent_id ")
                .append(" INNER JOIN job j ON j.id = trp.job_id ")
                .append(" inner join enum_currency ec on ec.id = trpod.currency ")
                .append(" INNER JOIN company c ON c.id = j.company_id ")
                .append(" inner join user u on u.id = ").append(SecurityUtils.getUserId())
                .append(" WHERE trp.tenant_id = ?1 AND ");
        map.put(1, SecurityUtils.getTenantId());
        appendConditionOfferAcceptedByTypeAndTypeValue(saleDetailDTO, map, offerAcceptedSql);
        conditionOnBoardedOrOfferAcceptedByJobType(offerAcceptedSql, saleDetailDTO, map);
        conditionOfferAcceptedByApplications(offerAcceptedSql, saleDetailDTO, map);
        conditionOfferAcceptedByNotInApplicationId(offerAcceptedSql);
        sb.append(UNION_ALL).append(StrUtil.subBefore(offerAcceptedSql, AND_SYMBOL, true));
    }

    private void createSalesDetailOnBoardedFteSql(StringBuilder onboardedFteSql, StringBuilder amAndRecruiterAndSourcerSql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder sb) {
        onboardedFteSql.append(" select s.talent_recruitment_process_id as id, sr.total_bill_amount as total_bill_amount, t.id AS talent_id, t.full_name, j.id AS job_id, j.title, sa.original_loc ->> '$.originDisplay' as work_location, j.pteam_id as job_pteam_id, c.id AS company_id, c.`full_business_name` as company_name, s.start_type, s.`status`, ")
                .append(amAndRecruiterAndSourcerSql)
                .append(" s.start_date as start_date, s.end_date as end_date, 'Onboard' as type, ec.from_usd_rate, ")
                .append(" null as s_start_date, null as s_end_date, s.warranty_end_date as warranty_end_date, null as final_pay_rate , null as final_bill_rate , null as estimated_working_hour_per_week, s.position_type AS job_type, sr.rate_unit_type, ")
                .append(" sr.currency currency, ")
                .append(" trpo.created_date onboard_date, (case CONCAT(onu.first_name,onu.last_name) regexp '[一-龥]' when 1 then CONCAT(onu.last_name,onu.first_name) ELSE CONCAT(onu.first_name, \" \",onu.last_name) END) onboard_by, ")
                .append(" if(abi.id is null,aabi.assignment_division, abi.assignment_division) job_division, if(ac.id is null, aac.zip_code, ac.zip_code), u.custom_timezone, if(resign.id is null, false, true) as resigned, if(star.id is null, false, true) as converted_to_fte ")
                .append(" FROM start s ")
                .append(" INNER JOIN talent t ON t.id = s.talent_id ")
                .append(" INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left JOIN talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left JOIN talent_recruitment_process_onboard_work_location trpowl ON trpowl.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left JOIN talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join start_address sa on sa.start_id = s.id")
                .append(" left join user onu on onu.id = trpo.puser_id ")
                .append(" left join timesheet_talent_assignment tta on tta.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join assignment_bill_info abi on abi.assignment_id = tta.id ")
                .append(" left join assignment_location ac on ac.assignment_id = tta.id ")
                .append(" LEFT JOIN ( SELECT min(id) id, talent_recruitment_process_id FROM timesheet_talent_assignment GROUP BY talent_recruitment_process_id ) atta ON atta.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join assignment_bill_info aabi on aabi.assignment_id = atta.id ")
                .append(" left join assignment_location aac on aac.assignment_id = atta.id ")
                .append(" INNER JOIN job j ON j.id = s.job_id ")
                .append(" INNER JOIN company c ON c.id = j.company_id ")
                .append(" INNER JOIN start_fte_rate sr ON sr.start_id = s.id ")
                .append(" inner join enum_currency ec on ec.id = sr.currency ")
                .append(" inner join user u on u.id = ").append(SecurityUtils.getUserId())
                .append(" WHERE s.status !=-1 and s.tenant_id = ?");
        map.put(map.size() + 1, SecurityUtils.getTenantId());
        onboardedFteSql.append(map.size()).append(AND_SYMBOL);
        appendConditionOnboardedByTypeAndTypeValue(saleDetailDTO, map, onboardedFteSql);
        conditionOnBoardedByJobType(onboardedFteSql, saleDetailDTO, map);
        conditionOnBoardedByApplications(onboardedFteSql, saleDetailDTO, map);
        sb.append(UNION_ALL).append(StrUtil.subBefore(onboardedFteSql, AND_SYMBOL, true));
    }

    private void createSalesDetailOnBoardedContractSql(StringBuilder onboardedContractSql, StringBuilder amAndRecruiterAndSourcerSql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder sb) {
        onboardedContractSql.append(" select s.talent_recruitment_process_id as id, sr.total_bill_amount as total_bill_amount, t.id AS talent_id, t.full_name, j.id AS job_id, j.title, sa.original_loc ->> '$.originDisplay' as work_location, j.pteam_id as job_pteam_id, c.id AS company_id, c.`full_business_name` as company_name, s.start_type, s.`status`, ")
                .append(amAndRecruiterAndSourcerSql)
                .append(" s.start_date as start_date, s.end_date as end_date, 'Onboard' as type, ec.from_usd_rate,")
                .append(" sr.start_date as s_start_date, sr.end_date as s_end_date, s.warranty_end_date as warranty_end_date, sr.final_pay_rate , sr.final_bill_rate , sr.estimated_working_hour_per_week, s.position_type AS job_type, sr.rate_unit_type, ")
                .append(" sr.currency currency, ")
                .append(" trpo.created_date onboard_date, (case CONCAT(onu.first_name,onu.last_name) regexp '[一-龥]' when 1 then CONCAT(onu.last_name,onu.first_name) ELSE CONCAT(onu.first_name, \" \",onu.last_name) END) onboard_by, ")
                .append(" if(abi.id is null,aabi.assignment_division, abi.assignment_division) job_division, if(ac.id is null, aac.zip_code, ac.zip_code), u.custom_timezone, if(resign.id is null, false, true) as resigned, if(star.id is null, false, true) as converted_to_fte ")
                .append(" FROM start s ")
                .append(" INNER JOIN talent t ON t.id = s.talent_id ")
                .append(" left JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left JOIN talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left JOIN talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join start_address sa on sa.start_id = s.id")
                .append(" left join user onu on onu.id = trpo.puser_id ")
                .append(" left join timesheet_talent_assignment tta on tta.talent_recruitment_process_id = s.talent_recruitment_process_id and s.start_date BETWEEN tta.start_date and tta.end_date ")
                .append(" left join assignment_bill_info abi on abi.assignment_id = tta.id ")
                .append(" left join assignment_location ac on ac.assignment_id = tta.id ")
                .append(" LEFT JOIN ( SELECT min(id) id, talent_recruitment_process_id FROM timesheet_talent_assignment GROUP BY talent_recruitment_process_id ) atta ON atta.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join assignment_bill_info aabi on aabi.assignment_id = atta.id ")
                .append(" left join assignment_location aac on aac.assignment_id = atta.id ")
                .append(" INNER JOIN job j ON j.id = s.job_id ")
                .append(" INNER JOIN company c ON c.id = j.company_id ")
                .append(" INNER JOIN start_contract_rate sr ON sr.start_id = s.id ")
                .append(" inner join enum_currency ec on ec.id = sr.currency ")
                .append(" inner join user u on u.id = ").append(SecurityUtils.getUserId())
                .append(" WHERE s.status !=-1 and s.tenant_id = ?");
        map.put(map.size() + 1, SecurityUtils.getTenantId());
        onboardedContractSql.append(map.size()).append(AND_SYMBOL);
        appendConditionOnboardedByTypeAndTypeValue(saleDetailDTO, map, onboardedContractSql);
        conditionOnBoardedByJobType(onboardedContractSql, saleDetailDTO, map);
        conditionOnBoardedByApplications(onboardedContractSql, saleDetailDTO, map);
        sb.append(UNION_ALL).append(StrUtil.subBefore(onboardedContractSql, AND_SYMBOL, true));
    }

    private void createSalesDetailOfferAcceptedContractSql(StringBuilder offerAcceptedSql, StringBuilder amAndRecruiterAndSourcerSql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder sb) {
        offerAcceptedSql.append(" select trp.id as id, trpicfc.gp as total_bill_amount, t.id AS talent_id, t.full_name, j.id AS job_id, j.title, '' as work_location, j.pteam_id as job_pteam_id, c.id AS company_id, c.`full_business_name` as company_name, null as start_type, null as `status`, ")
                .append(amAndRecruiterAndSourcerSql)
                .append(" trpod.onboard_date as start_date, trpod.end_date as end_date, 'Offer Accepted' as type, ec.from_usd_rate, ")
                .append(" trpod.onboard_date as s_start_date, trpod.end_date as s_end_date, trpod.warranty_end_date, trpicfc.final_pay_rate, trpicfc.final_bill_rate, trpicfc.estimated_working_hour_per_week, rp.job_type AS job_type, trpod.rate_unit_type, ")
                .append(" trpod.currency currency, ")
                .append(" trpo.created_date onboard_date, (case CONCAT(onu.first_name,onu.last_name) regexp '[一-龥]' when 1 then CONCAT(onu.last_name,onu.first_name) ELSE CONCAT(onu.first_name, \" \",onu.last_name) END) onboard_by, ")
                .append(" abi.assignment_division job_division, ac.zip_code zip_code, u.custom_timezone, if(resign.id is null, false, true) as resigned, if(star.id is null, false, true) as converted_to_fte ")
                .append(" FROM talent_recruitment_process trp ")
                .append(" INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id ")
                .append(" INNER JOIN talent_recruitment_process_ipg_contract_fee_charge trpicfc ON trp.id = trpicfc.talent_recruitment_process_id ")
                .append(" INNER JOIN talent_recruitment_process_onboard_date trpod ON trpod.talent_recruitment_process_id = trp.id ")
                .append(" left JOIN talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = trp.id ")
                .append(" left JOIN talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = trp.id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join user onu on onu.id = trpo.puser_id ")
                .append(" left join timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id ")
                .append(" left join assignment_bill_info abi on abi.assignment_id = tta.id ")
                .append(" left join assignment_location ac on ac.assignment_id = tta.id ")
                .append(" INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id ")
                .append(" INNER JOIN talent t ON t.id = trp.talent_id ")
                .append(" INNER JOIN job j ON j.id = trp.job_id ")
                .append(" inner join enum_currency ec on ec.id = trpod.currency ")
                .append(" INNER JOIN company c ON c.id = j.company_id ")
                .append(" inner join user u on u.id = ").append(SecurityUtils.getUserId())
                .append(" WHERE trp.tenant_id = ?1 AND ");
        map.put(1, SecurityUtils.getTenantId());
        appendConditionOfferAcceptedByTypeAndTypeValue(saleDetailDTO, map, offerAcceptedSql);
        conditionOnBoardedOrOfferAcceptedByJobType(offerAcceptedSql, saleDetailDTO, map);
        conditionOfferAcceptedByApplications(offerAcceptedSql, saleDetailDTO, map);
        conditionOfferAcceptedByNotInApplicationId(offerAcceptedSql);
        sb.append(StrUtil.subBefore(offerAcceptedSql, AND_SYMBOL, true));
    }

    private void conditionOfferAcceptedByNotInApplicationId(StringBuilder offerAcceptedSql) {
        offerAcceptedSql.append(" not exists (select 1 from start s where s.talent_recruitment_process_id = trp.id) ").append(AND_SYMBOL);
    }

    private void conditionOnBoardedByApplications(StringBuilder sql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map) {
        if (CollUtil.isNotEmpty(saleDetailDTO.getApplicationIds())) {
            sql.append(" s.talent_recruitment_process_id in ?");
            map.put(map.size() + 1, saleDetailDTO.getApplicationIds());
            sql.append(map.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOfferAcceptedByApplications(StringBuilder sql, SaleDetailDTO saleDetailDTO, Map<Integer, Object> map) {
        if (CollUtil.isNotEmpty(saleDetailDTO.getApplicationIds())) {
            sql.append(" trp.id in ?");
            map.put(map.size() + 1, saleDetailDTO.getApplicationIds());
            sql.append(map.size()).append(AND_SYMBOL);
        }
    }

    private void appendConditionOnboardedByTypeAndTypeValue(SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder onboardedSql) {
        if (saleDetailDTO.getType() == ReportSalesType.WEEK) {
            onboardedSql.append(" DATE_FORMAT(trpioa.created_date,'%u') = ").append("DATE_FORMAT('").append(saleDetailDTO.getTypeValue()).append("','%u')").append(AND_SYMBOL);
        } else if (saleDetailDTO.getType() == ReportSalesType.MONTH || saleDetailDTO.getType() == ReportSalesType.QUARTER) {
            onboardedSql.append(" ").append(saleDetailDTO.getType().name()).append("(s.start_date) = ?");
            map.put(map.size() + 1, Long.valueOf(saleDetailDTO.getTypeValue()));
            onboardedSql.append(map.size()).append(AND_SYMBOL).append(" year(s.start_date) in ?");
            map.put(map.size() + 1, saleDetailDTO.getYears());
            onboardedSql.append(map.size()).append(AND_SYMBOL);
        } else {
            onboardedSql.append(" ").append(saleDetailDTO.getType().name()).append("(s.start_date) = ?");
            map.put(map.size() + 1, Long.valueOf(saleDetailDTO.getTypeValue()));
            onboardedSql.append(map.size()).append(AND_SYMBOL);
        }
    }

    private void appendConditionOfferAcceptedByTypeAndTypeValue(SaleDetailDTO saleDetailDTO, Map<Integer, Object> map, StringBuilder offerAcceptedSql) {
        if (saleDetailDTO.getType() == ReportSalesType.WEEK) {
            offerAcceptedSql.append(" DATE_FORMAT(trpioa.created_date,'%u') = ").append("DATE_FORMAT('").append(saleDetailDTO.getTypeValue()).append("','%u')").append(AND_SYMBOL);
        } else if (saleDetailDTO.getType() == ReportSalesType.MONTH || saleDetailDTO.getType() == ReportSalesType.QUARTER) {
            offerAcceptedSql.append(" ").append(saleDetailDTO.getType().name()).append("(trpod.onboard_date) = ?");
            map.put(map.size() + 1, Long.valueOf(saleDetailDTO.getTypeValue()));
            offerAcceptedSql.append(map.size()).append(AND_SYMBOL).append(" year(trpod.onboard_date) in ?");
            map.put(map.size() + 1, saleDetailDTO.getYears());
            offerAcceptedSql.append(map.size()).append(AND_SYMBOL);
        } else {
            offerAcceptedSql.append(" ").append(saleDetailDTO.getType().name()).append("(trpod.onboard_date) = ?");
            map.put(map.size() + 1, Long.valueOf(saleDetailDTO.getTypeValue()));
            offerAcceptedSql.append(map.size()).append(AND_SYMBOL);
        }
    }

    private void checkParam(SaleDetailDTO saleDetailDTO) {
        if (ObjectUtil.isNull(saleDetailDTO.getJobType())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTSALES_CHECKPARAM_JOBTYPENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        if (CollUtil.isEmpty(saleDetailDTO.getApplicationIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTSALES_CHECKPARAM_APPLICATIONIDSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }


    private void createSearchSalesByTypeSql(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder searchSql = new StringBuilder();
        doCreateSearchSalesByTypeSql(searchSql, reportParam, conditionMap);
        sb.append(" SELECT GROUP_CONCAT(distinct a.application_id) as application_id, sum(a.total_bill_amount) as total_bill_amount, a.bill_type, a.bill_year, a.bill_index ")
                .append(" from ( ").append(searchSql).append(" ) ").append(" a ").append(" group by a.bill_type, a.bill_year, a.bill_index ");
    }

    private void createSearchSalesByWeekSql(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder searchSql = new StringBuilder();
        doCreateSearchSalesByTypeSql(searchSql, reportParam, conditionMap);
        // weekSql
        doCreateWeekBy6Month(searchSql, reportParam, conditionMap);
        sb.append(" SELECT GROUP_CONCAT(distinct a.application_id) as application_id, sum(a.total_bill_amount) as total_bill_amount, a.bill_index as bill_week")
                .append(" from ( ").append(searchSql).append(" ) ").append(" a ").append(" group by a.bill_index ");
    }

    private void doCreateSearchSalesByTypeSql(StringBuilder searchSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        //offer accepted contract sql
        createSalesSqlByOfferAcceptedContract(searchSql, reportParam, conditionMap);
        //offer accepted fte sql
        createSalesSqlByOfferAcceptedFte(searchSql, reportParam, conditionMap);
        // on board => start contract
        createSalesSqlByOnboardContract(searchSql, reportParam, conditionMap);
        // on board => start fte
        createSalesSqlByOnboardFte(searchSql, reportParam, conditionMap);
    }

    private void createSalesSqlByOfferAcceptedFte(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder offerAcceptedSql = new StringBuilder();
        offerAcceptedSql.append(" select trpicfc.talent_recruitment_process_id as application_id, ");
        if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
            offerAcceptedSql.append(" (ec.from_usd_rate * trpicfc.total_amount) as total_bill_amount, ");
        } else {
            offerAcceptedSql.append(" trpicfc.total_amount as total_bill_amount, ");
        }
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            offerAcceptedSql.append(" 'Offer Accepted' as bill_type, year(trpioa.created_date) as bill_year, ");
        } else {
            offerAcceptedSql.append(" 'Offer Accepted' as bill_type, year(trpod.onboard_date) as bill_year, ");
        }
        appendOfferAcceptedSearchType(offerAcceptedSql, reportParam);
        offerAcceptedSql.append(" from talent_recruitment_process_offer_fee_charge trpicfc ")
                .append(" inner join talent_recruitment_process trp on trp.id = trpicfc.talent_recruitment_process_id ")
                .append(" inner join recruitment_process rp on rp.id = trp.recruitment_process_id ")
                .append(" inner join job j on j.id = trp.job_id ")
                .append(" inner join talent_recruitment_process_onboard_date trpod on trpod.talent_recruitment_process_id = trp.id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            offerAcceptedSql.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = trp.id ");
        }
        offerAcceptedSql.append(" Inner join enum_currency ec on ec.id = trpod.currency ")
                .append(" where trp.tenant_id = ?");
        conditionMap.put(conditionMap.size() + 1, SecurityUtils.getTenantId());
        offerAcceptedSql.append(conditionMap.size()).append(AND_SYMBOL);
        sb.append(UNION_ALL);
        appendOfferAcceptedCondition(offerAcceptedSql, reportParam, conditionMap, sb);
    }

    private void appendOfferAcceptedCondition(StringBuilder offerAcceptedSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap, StringBuilder sb) {
        offerAcceptedSql.append(" NOT EXISTS (SELECT 1 FROM start s WHERE s.talent_recruitment_process_id = trpicfc.talent_recruitment_process_id) ").append(AND_SYMBOL);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(offerAcceptedSql, conditionMap);
        offerAcceptedSql.append(" NOT EXISTS (SELECT 1 FROM talent_recruitment_process_node trpn where trpn.talent_recruitment_process_id = trp.id and trpn.node_status = 4) ").append(AND_SYMBOL);
        //condition years
        conditionOfferAcceptedByYears(offerAcceptedSql, reportParam, conditionMap);
        //jobType
        conditionOnBoardedOrOfferAcceptedByJobType(offerAcceptedSql, reportParam, conditionMap);
        //currency
        conditionOfferAcceptedByCurrency(offerAcceptedSql, reportParam, conditionMap);
        //companies
        conditionOnBoardedOrOfferAcceptedByCompanies(offerAcceptedSql, reportParam, conditionMap);
        //permission and userId and teamId
        appendPermissionAndTeamUserRoleCondition(offerAcceptedSql, reportParam, conditionMap, false);
        sb.append(StrUtil.subBefore(offerAcceptedSql, AND_SYMBOL, true));
    }

    private void appendPermissionAndTeamUserRoleCondition(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap, boolean isStartFlag) {
        if (CollUtil.isEmpty(reportParam.getUserIdList()) && CollUtil.isEmpty(reportParam.getTeamIdList()) && (SecurityUtils.isAdmin() || SecurityUtils.isSystemAdmin())) {
            return;
        }
        Set<Long> userIdSet = new HashSet<>(reportParam.getUserIdList());
        Set<Long> teamIdSet = new HashSet<>(reportParam.getTeamIdList());
        Set<Long> intersectionTeamIdSet = new HashSet<>(reportParam.getTeamDataPermission().getNestedTeamIds());
        TeamDataPermissionRespDTO permissionRespDTO = reportParam.getTeamDataPermission();
        if (permissionRespDTO.getSelf()) {
            //个人权限, 只能看自己的信息
            userIdSet.clear();
            userIdSet.add(SecurityUtils.getUserId());
            teamIdSet.clear();
        } else if (CollUtil.isNotEmpty(permissionRespDTO.getNestedTeamIds())) {
            //这里是团队权限
            //这个是获取到所有的有的权限团队id
            if (CollUtil.isNotEmpty(teamIdSet)) {
                //当没有要查询 当有查询信息的是, 需要根据权限过滤一下
                teamIdSet = teamIdSet.stream().filter(intersectionTeamIdSet::contains).collect(Collectors.toSet());
            } else {
                //没有需要搜索的 teamId 权限时,如果是有查询 userId 的权限则 teamId 为空
                if (CollUtil.isNotEmpty(userIdSet)) {
                    teamIdSet.clear();
                } else {
                    teamIdSet = intersectionTeamIdSet;
                }
            }
            if (CollUtil.isNotEmpty(userIdSet)) {
                userIdSet = reportRepository.getUserTeamPair(userIdSet)
                        .stream().filter(p -> intersectionTeamIdSet.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
            }
        } else {
            //all 的情况
            if (CollUtil.isEmpty(userIdSet) && CollUtil.isEmpty(teamIdSet)) {
                //过滤完后,没有可以查询的数据
                return;
            }
            doAppendPermission(sb, reportParam, conditionMap, isStartFlag, teamIdSet, intersectionTeamIdSet, userIdSet);
            return;
        }
        if (CollUtil.isEmpty(userIdSet) && CollUtil.isEmpty(teamIdSet)) {
            //过滤完后,没有可以查询的数据
            sb.append(" 1 = 2 ").append(AND_SYMBOL);
            return;
        }
        doAppendPermission(sb, reportParam, conditionMap, isStartFlag, teamIdSet, intersectionTeamIdSet, userIdSet);
    }

    private void doAppendPermission(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap, boolean isStartFlag, Set<Long> teamIdSet, Set<Long> intersectionTeamIdSet, Set<Long> userIdSet) {
        sb.append(" exists ( ")
                .append("""
                     select 1 from talent_recruitment_process_kpi_user kpi
                     inner join permission_user_team put on put.user_id = kpi.user_id and put.is_primary = 1
                     where kpi.talent_recruitment_process_id =
                    """);
        if (isStartFlag) {
            sb.append(" s.talent_recruitment_process_id ");
        } else {
            sb.append(" trp.id ");
        }
        if (CollUtil.isNotEmpty(teamIdSet) && CollUtil.isNotEmpty(userIdSet)) {
            conditionMap.put(conditionMap.size() + 1, teamIdSet);
            sb.append(AND_SYMBOL).append(" ( ( put.team_id in ?");
            sb.append(conditionMap.size()).append(" ");
            sb.append(" ) or ").append(" ( put.user_id in ?");
            conditionMap.put(conditionMap.size() + 1, userIdSet);
            sb.append(conditionMap.size()).append(" ");
            if (CollUtil.isNotEmpty(intersectionTeamIdSet)) {
                sb.append(AND_SYMBOL).append(" put.team_id in ?");
                conditionMap.put(conditionMap.size() + 1, intersectionTeamIdSet);
                sb.append(conditionMap.size()).append(" ");
            }
            sb.append(" ) ") .append(" ) ");
        } else if (CollUtil.isNotEmpty(userIdSet)) {
            conditionMap.put(conditionMap.size() + 1, userIdSet);
            sb.append(AND_SYMBOL).append(" put.user_id in ?");
            sb.append(conditionMap.size()).append(" ");
            if (CollUtil.isNotEmpty(intersectionTeamIdSet)) {
                sb.append(AND_SYMBOL).append(" put.team_id in ?");
                conditionMap.put(conditionMap.size() + 1, intersectionTeamIdSet);
                sb.append(conditionMap.size()).append(" ");
            }
        } else {
            conditionMap.put(conditionMap.size() + 1, teamIdSet);
            sb.append(AND_SYMBOL).append(" put.team_id in ?");
            sb.append(conditionMap.size()).append(" ");
        }
        if (CollUtil.isNotEmpty(reportParam.getUserRoleList())) {
            conditionMap.put(conditionMap.size() + 1, reportParam.getUserRoleList().stream().map(UserRole::toDbValue).toList());
            sb.append(AND_SYMBOL).append(" kpi.user_role in ?");
            sb.append(conditionMap.size()).append(" ");
        }
        sb.append(" ) ").append(AND_SYMBOL);
    }

    private void doCreateWeekBy6Month(StringBuilder searchSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        String weekBy6MonthSql = """
                SELECT NULL                                                                                  AS application_id,
                       0                                                                                     AS total_bill_amount,
                       NULL                                                                                  AS bill_type,
                       NULL                                                                                  AS bill_year,
                       STR_TO_DATE(DATE_ADD(a.bill_week, INTERVAL (- WEEKDAY(a.bill_week)) DAY), '%Y-%m-%d') AS bill_index
                from (SELECT STR_TO_DATE(@cdate \\:= date_add(@cdate, INTERVAL - 1 DAY), '%Y-%m-%d') as bill_week
                      FROM (SELECT @cdate  \\:= date_add({endDate}, INTERVAL 1 DAY)
                            FROM talent_recruitment_process_node  limit 200 ) a) a
                {where}
                """;
        searchSql.append(UNION_ALL);
        Instant mondayFrom = reportParam.mondayFrom();
        Instant sundayTo = reportParam.sundayTo();
        if (ObjectUtil.isNotEmpty(mondayFrom) && ObjectUtil.isNotEmpty(sundayTo)) {
            StringBuilder where = new StringBuilder(" WHERE a.bill_week BETWEEN ");
            conditionMap.put(conditionMap.size() + 1, mondayFrom);
            where.append("?").append(conditionMap.size()).append(AND_SYMBOL);
            conditionMap.put(conditionMap.size() + 1, sundayTo);
            where.append("?").append(conditionMap.size());
            Map<String, String> formatArgs = Map.of("endDate", "'" + sundayTo + "'", "where", where.toString());
            searchSql.append(StrUtil.format(weekBy6MonthSql, formatArgs));
        } else {
            Map<String, String> formatArgs = Map.of("endDate", "CURDATE()", "where", " WHERE a.bill_week >= DATE_ADD( NOW(), INTERVAL - 6 MONTH ) ");
            searchSql.append(StrUtil.format(weekBy6MonthSql, formatArgs));
        }
    }

    private void createSalesSqlByOnboardFte(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder onboardFteSql = new StringBuilder();
        onboardFteSql.append(" select s.talent_recruitment_process_id as application_id, ");
        if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
            onboardFteSql.append(" (ec.from_usd_rate * sfr.total_bill_amount) as total_bill_amount, ");
        } else {
            onboardFteSql.append(" sfr.total_bill_amount as total_bill_amount, ");
        }
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            onboardFteSql.append(" 'Onboard' as bill_type, year(trpioa.created_date) as bill_year, ");
        } else {
            onboardFteSql.append(" 'Onboard' as bill_type, year(s.start_date) as bill_year, ");
        }
        appendSearchType(onboardFteSql, reportParam);
        onboardFteSql.append(" from start_fte_rate sfr ")
                .append(" inner join vChain s on s.id = sfr.start_id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            onboardFteSql.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ");
        }
        if(CollUtil.isNotEmpty(reportParam.getJobType()) && reportParam.getJobType().contains(JobType.FULL_TIME)){
            onboardFteSql.append("""
                    INNER JOIN vLatestChain lc ON s.chain_root = lc.chain_root AND s.job_id = lc.job_id AND s.start_date = lc.max_start_date
                    """);
        }
        onboardFteSql.append(" inner join job j on j.id = s.job_id ")
                .append(" Inner join enum_currency ec on ec.id = sfr.currency ")
                .append(" where s.status !=-1 and s.tenant_id = ?");
        conditionMap.put(conditionMap.size() + 1, SecurityUtils.getTenantId());
        onboardFteSql.append(conditionMap.size()).append(AND_SYMBOL);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(onboardFteSql, conditionMap);
        conditionOnBoardedByYears(onboardFteSql, reportParam, conditionMap);
        conditionOnBoardedByJobType(onboardFteSql, reportParam, conditionMap);
        conditionOnBoardedFteByCurrency(onboardFteSql, reportParam, conditionMap);
        conditionOnBoardedOrOfferAcceptedByCompanies(onboardFteSql, reportParam, conditionMap);
        appendPermissionAndTeamUserRoleCondition(onboardFteSql, reportParam, conditionMap, true);
        sb.append(UNION_ALL).append(StrUtil.subBefore(onboardFteSql, AND_SYMBOL, true));
    }

    private void appendSearchType(StringBuilder sb, ReportSalesParamDto reportParam) {
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            sb.append(" STR_TO_DATE(DATE_ADD(trpioa.created_date, INTERVAL(-WEEKDAY(trpioa.created_date)) DAY), '%Y-%m-%d') as bill_index");
        } else {
            sb.append(reportParam.getType().name()).append("(s.start_date) as bill_index ");
        }
    }

    private void createSalesSqlByOnboardContract(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder onboardContractSql = new StringBuilder();
        onboardContractSql.append(" select s.talent_recruitment_process_id as application_id, ");
        if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
            onboardContractSql.append(" (ec.from_usd_rate * scr.total_bill_amount) as total_bill_amount, ");
        } else {
            onboardContractSql.append(" scr.total_bill_amount as total_bill_amount, ");
        }
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            onboardContractSql.append(" 'Onboard' as bill_type, year(trpioa.created_date) as bill_year, ");
        } else {
            onboardContractSql.append(" 'Onboard' as bill_type, year(s.start_date) as bill_year, ");
        }
        appendSearchType(onboardContractSql, reportParam);
        onboardContractSql.append(" from start_contract_rate scr ")
                .append(" inner join start s on s.id = scr.start_id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            onboardContractSql.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ");
        }
        onboardContractSql.append(" inner join job j on j.id = s.job_id ")
                .append(" Inner join enum_currency ec on ec.id = scr.currency ")
                .append(" inner join recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" where s.status !=-1 and s.tenant_id = ?");
        conditionMap.put(conditionMap.size() + 1, SecurityUtils.getTenantId());
        onboardContractSql.append(conditionMap.size()).append(AND_SYMBOL);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(onboardContractSql, conditionMap);
        //condition years
        conditionOnBoardedByYears(onboardContractSql, reportParam, conditionMap);
        //jobType
        conditionOnBoardedByJobType(onboardContractSql, reportParam, conditionMap);
        //currency
        conditionOnBoardedContractByCurrency(onboardContractSql, reportParam, conditionMap);
        //companies
        conditionOnBoardedOrOfferAcceptedByCompanies(onboardContractSql, reportParam, conditionMap);
        appendPermissionAndTeamUserRoleCondition(onboardContractSql, reportParam, conditionMap, true);
        sb.append(UNION_ALL).append(StrUtil.subBefore(onboardContractSql, AND_SYMBOL, true));
    }

    private void createSalesSqlByOfferAcceptedContract(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder offerAcceptedSql = new StringBuilder();
        offerAcceptedSql.append(" select trpicfc.talent_recruitment_process_id as application_id, ");
        if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
            offerAcceptedSql.append(" (ec.from_usd_rate * trpicfc.gp) as total_bill_amount, ");
        } else {
            offerAcceptedSql.append(" trpicfc.gp as total_bill_amount, ");
        }
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            offerAcceptedSql.append(" 'Offer Accepted' as bill_type, year(trpioa.created_date) as bill_year, ");
        } else {
            offerAcceptedSql.append(" 'Offer Accepted' as bill_type, year(trpod.onboard_date) as bill_year, ");
        }
        appendOfferAcceptedSearchType(offerAcceptedSql, reportParam);
        offerAcceptedSql.append(" from talent_recruitment_process_ipg_contract_fee_charge trpicfc ")
                .append(" inner join talent_recruitment_process trp on trp.id = trpicfc.talent_recruitment_process_id ")
                .append(" inner join job j on j.id = trp.job_id ")
                .append(" inner join recruitment_process rp on rp.id = trp.recruitment_process_id ")
                .append(" inner join talent_recruitment_process_onboard_date trpod on trpod.talent_recruitment_process_id = trp.id ")
        ;
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            offerAcceptedSql.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = trp.id ");
        }
        offerAcceptedSql.append(" Inner join enum_currency ec on ec.id = trpod.currency ")
                .append(" where trp.tenant_id = ?1 ").append(AND_SYMBOL);
        conditionMap.put(1, SecurityUtils.getTenantId());
        appendOfferAcceptedCondition(offerAcceptedSql, reportParam, conditionMap, sb);

    }

    private void appendOfferAcceptedSearchType(StringBuilder offerAcceptedSql, ReportSalesParamDto reportParam) {
        if (Objects.equals(reportParam.getType(), ReportSalesType.WEEK)) {
            offerAcceptedSql.append(" STR_TO_DATE(DATE_ADD(trpioa.created_date, INTERVAL(-WEEKDAY(trpioa.created_date)) DAY), '%Y-%m-%d') as bill_index");
        } else {
            offerAcceptedSql.append(reportParam.getType().name()).append("(trpod.onboard_date) as bill_index");
        }
    }

    private void conditionOnBoardedOrOfferAcceptedByNotInCompanies(StringBuilder offerAcceptedSql, Map<Integer, Object> conditionMap) {
        offerAcceptedSql.append(" NOT EXISTS (select 1 from company c where c.id = j.company_id and c.id in ?");
        conditionMap.put(conditionMap.size() + 1, IPG_COMPANY_IDS);
        offerAcceptedSql.append(conditionMap.size()).append(")").append(AND_SYMBOL);
    }

    private void conditionOnBoardedOrOfferAcceptedByCompanies(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (CollUtil.isNotEmpty(reportParam.getCompanies())) {
            sb.append(" exists ( ")
                    .append("select 1 from company c where c.id = j.company_id and c.id in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getCompanies());
            sb.append(conditionMap.size()).append(" ) ").append(AND_SYMBOL);
        }
    }

    private void createCompanyFilterSql(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        StringBuilder offerAcceptedSqlSb = new StringBuilder();
        StringBuilder onboardedContractSqlSb = new StringBuilder();
        StringBuilder onboardedFteSqlSb = new StringBuilder();
        String commonSql = " select c.id, c.full_business_name as name from company c inner join job j on j.company_id = c.id inner join recruitment_process rp on rp.id = j.recruitment_process_id ";
        String offerAcceptedSql = createOfferAcceptedSearchCompanySql(offerAcceptedSqlSb, commonSql, reportParam, conditionMap);
        String onboardedContractSql = createOnboardedContractSearchCompanySql(onboardedContractSqlSb, commonSql, reportParam, conditionMap);
        String onboardedFteSql = createOnboardedFteSearchCompanySql(onboardedFteSqlSb, commonSql, reportParam, conditionMap);
        String groupBy = " group by c.id ";
        sb.append(offerAcceptedSql).append(groupBy).append(onboardedContractSql).append(groupBy).append(onboardedFteSql).append(groupBy);
    }

    private String createOnboardedFteSearchCompanySql(StringBuilder onboardedFteSqlSb, String commonSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        onboardedFteSqlSb.append(" union ")
                .append(commonSql)
                .append(" inner join start s on s.company_id = c.id ")
                .append(" inner join start_fte_rate sfr on sfr.start_id = s.id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            onboardedFteSqlSb.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ");
        }
        onboardedFteSqlSb.append(" where s.tenant_id = ?");
        conditionMap.put(conditionMap.size() + 1, SecurityUtils.getTenantId());
        onboardedFteSqlSb.append(conditionMap.size()).append(AND_SYMBOL);
        conditionOnBoardedByYearsAndJobType(onboardedFteSqlSb, reportParam, conditionMap);
        conditionOnBoardedFteByCurrency(onboardedFteSqlSb, reportParam, conditionMap);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(onboardedFteSqlSb, conditionMap);
        return StrUtil.subBefore(onboardedFteSqlSb, AND_SYMBOL, true);
    }

    private String createOnboardedContractSearchCompanySql(StringBuilder onboardedContractSqlSb, String commonSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        onboardedContractSqlSb.append(" union ")
                .append(commonSql)
                .append(" inner join start s on s.company_id = c.id ")
                .append(" inner join start_contract_rate scr on scr.start_id = s.id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            onboardedContractSqlSb.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id ");
        }
        onboardedContractSqlSb.append(" where s.tenant_id = ?");
        conditionMap.put(conditionMap.size() + 1, SecurityUtils.getTenantId());
        onboardedContractSqlSb.append(conditionMap.size()).append(AND_SYMBOL);
        conditionOnBoardedByYearsAndJobType(onboardedContractSqlSb, reportParam, conditionMap);
        conditionOnBoardedContractByCurrency(onboardedContractSqlSb, reportParam, conditionMap);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(onboardedContractSqlSb, conditionMap);
        return StrUtil.subBefore(onboardedContractSqlSb, AND_SYMBOL, true);
    }

    private String createOfferAcceptedSearchCompanySql(StringBuilder offerAcceptedSqlSb, String commonSql, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        offerAcceptedSqlSb.append(commonSql)
                .append(" inner join talent_recruitment_process trp on trp.job_id = j.id ")
                .append(" inner join talent_recruitment_process_onboard_date trpod on trp.id = trpod.talent_recruitment_process_id ");
        if (CollUtil.isEmpty(reportParam.getYears())) {
            offerAcceptedSqlSb.append(" inner join talent_recruitment_process_ipg_offer_accept trpioa on trpioa.talent_recruitment_process_id = trp.id ");
        }
        offerAcceptedSqlSb.append(" where trp.tenant_id = ?1 and ");
        conditionMap.put(1, SecurityUtils.getTenantId());
        offerAcceptedSqlSb.append(" NOT EXISTS (SELECT 1 FROM start s WHERE s.talent_recruitment_process_id = trp.id) ").append(AND_SYMBOL);
        conditionOfferAcceptedByYears(offerAcceptedSqlSb, reportParam, conditionMap);
        conditionOnBoardedOrOfferAcceptedByJobType(offerAcceptedSqlSb, reportParam, conditionMap);
        conditionOfferAcceptedByCurrency(offerAcceptedSqlSb, reportParam, conditionMap);
        conditionOnBoardedOrOfferAcceptedByNotInCompanies(offerAcceptedSqlSb, conditionMap);
        return StrUtil.subBefore(offerAcceptedSqlSb, AND_SYMBOL, true);
    }

    private void conditionOfferAcceptedByCurrency(StringBuilder offerAcceptedSqlSb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (ObjectUtil.isNotNull(reportParam.getCountry())) {
            offerAcceptedSqlSb.append(" trpod.currency in ?");
            if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
                conditionMap.put(conditionMap.size() + 1, NO_CHINA_LIST);
            } else {
                conditionMap.put(conditionMap.size() + 1, CollUtil.newArrayList(reportParam.getCountry()));
            }
            offerAcceptedSqlSb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOnBoardedContractByCurrency(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (ObjectUtil.isNotNull(reportParam.getCountry())) {
            sb.append(" scr.currency in ?");
            if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
                conditionMap.put(conditionMap.size() + 1, NO_CHINA_LIST);
            } else {
                conditionMap.put(conditionMap.size() + 1, CollUtil.newArrayList(reportParam.getCountry()));
            }
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOnBoardedFteByCurrency(StringBuilder onboardedFteSqlSb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (ObjectUtil.isNotNull(reportParam.getCountry())) {
            onboardedFteSqlSb.append(" sfr.currency in ?");
            if (Objects.equals(CurrencyConstants.NON_CHINA, reportParam.getCountry())) {
                conditionMap.put(conditionMap.size() + 1, NO_CHINA_LIST);
            } else {
                conditionMap.put(conditionMap.size() + 1, CollUtil.newArrayList(reportParam.getCountry()));
            }
            onboardedFteSqlSb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOfferAcceptedByYears(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (CollUtil.isNotEmpty(reportParam.getYears())) {
            sb.append(" year(trpod.onboard_date) in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getYears());
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        } else {
            Instant mondayFrom = reportParam.mondayFrom();
            Instant sundayTo = reportParam.sundayTo();
            if (ObjectUtil.isNotEmpty(mondayFrom) && ObjectUtil.isNotEmpty(sundayTo)) {
                sb.append(" trpioa.created_date BETWEEN ?");
                conditionMap.put(conditionMap.size() + 1, mondayFrom);
                sb.append(conditionMap.size()).append(AND_SYMBOL).append("?");
                conditionMap.put(conditionMap.size() + 1, sundayTo);
                sb.append(conditionMap.size()).append(AND_SYMBOL);
            } else {
                sb.append(" trpioa.created_date BETWEEN DATE_ADD(NOW(), INTERVAL -6 MONTH) AND NOW() ").append(AND_SYMBOL);
            }
        }
    }

    private void conditionOnBoardedByYearsAndJobType(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        conditionOnBoardedByYears(sb, reportParam, conditionMap);
        conditionOnBoardedByJobType(sb, reportParam, conditionMap);
    }

    private void conditionOnBoardedOrOfferAcceptedByJobType(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (CollUtil.isNotEmpty(reportParam.getJobType())) {
            sb.append(" rp.job_type in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getJobType().stream().map(JobType::toDbValue).collect(Collectors.toList()));
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOnBoardedByJobType(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (CollUtil.isNotEmpty(reportParam.getJobType())) {
            sb.append(" s.position_type in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getJobType().stream().map(JobType::toDbValue).collect(Collectors.toList()));
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOnBoardedByJobType(StringBuilder sb, SaleDetailDTO reportParam, Map<Integer, Object> conditionMap) {
        if (ObjectUtil.isNotEmpty(reportParam.getJobType())) {
            sb.append(" s.position_type in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getJobType().stream().map(JobType::toDbValue).toList());
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }


    private void conditionOnBoardedOrOfferAcceptedByJobType(StringBuilder sb, SaleDetailDTO reportParam, Map<Integer, Object> conditionMap) {
        if (ObjectUtil.isNotEmpty(reportParam.getJobType())) {
            sb.append(" rp.job_type in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getJobType().stream().map(JobType::toDbValue).toList());
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        }
    }

    private void conditionOnBoardedByYears(StringBuilder sb, ReportSalesParamDto reportParam, Map<Integer, Object> conditionMap) {
        if (CollUtil.isNotEmpty(reportParam.getYears())) {
            sb.append(" year(s.start_date) in ?");
            conditionMap.put(conditionMap.size() + 1, reportParam.getYears());
            sb.append(conditionMap.size()).append(AND_SYMBOL);
        } else {
            Instant mondayFrom = reportParam.mondayFrom();
            Instant sundayTo = reportParam.sundayTo();
            if (ObjectUtil.isNotEmpty(mondayFrom) && ObjectUtil.isNotEmpty(sundayTo)) {
                sb.append(" trpioa.created_date BETWEEN ?");
                conditionMap.put(conditionMap.size() + 1, mondayFrom);
                sb.append(conditionMap.size()).append(AND_SYMBOL).append("?");
                conditionMap.put(conditionMap.size() + 1, sundayTo);
                sb.append(conditionMap.size()).append(AND_SYMBOL);
            } else {
                sb.append(" trpioa.created_date >= DATE_ADD(NOW(), INTERVAL - 6 MONTH)").append(AND_SYMBOL);
            }
        }
    }

}
