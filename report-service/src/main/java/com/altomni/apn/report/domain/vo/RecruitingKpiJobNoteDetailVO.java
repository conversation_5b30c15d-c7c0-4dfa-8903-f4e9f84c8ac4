package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiJobNoteDetailVO {

    @Id
    private Long id;

    private Long jobId;

    private String jobTitle;

    private String note;

    private Long createdBy;

    private Long lastModifiedBy;

    private Instant createdDate;

    private Instant lastModifiedDate;

    @Transient
    private boolean isPrivateJob;

    private Long pteamId;

}
