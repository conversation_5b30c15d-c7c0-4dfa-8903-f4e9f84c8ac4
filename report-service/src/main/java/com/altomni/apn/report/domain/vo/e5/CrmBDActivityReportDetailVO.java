package com.altomni.apn.report.domain.vo.e5;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrmBDActivityReportDetailVO implements Serializable {

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户公司id
     */
    private Long accountCompanyId;

    /**
     * 线索公司id
     */
    private Long salesLeadCompanyId;

    /**
     * 联系时间
     */
    private String contactTime;

    /**
     * 备注
     */
    private String note;

    /**
     * 客户联系人
     */
    private List<String> accountContactId;

    private Map<Long,String> accountContacts;

    private Map<Long,String> leadContacts;


    /**
     * 服务类型
     */
    private List<Integer> serviceTypeIds;

    /**
     * BD所有者
     */
    private List<Long> bdOwnerIds;

    /**
     * 销售线索负责人
     */
    private List<Long> salesLeadOwnerIds;


    /**
     * 销售线索id
     */
    private Long salesLeadId;


    /**
     * 客户模块销售线索id
     */
    private Long accountBusinessId;


    /**
     * 线索联系人
     */
    private List<String> salesLeadContactId;

    private String model;

    private Long contactType;

}
