package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportQuarterlyOffboardingDetailVO implements Serializable {

    private String id;

    private String recruiter;

    private String clientAccount;

    private BigInteger clientId;

    private BigInteger candidateId;

    private String candidateName;

    private String endDay;

    private String weekDay;

    private String hourlyGp;

    private String totalGp;

    private String totalRevenue;

    private String currency;

    private String currencyName;

    private String symbol;

    @JsonIgnore
    private Integer weekDays;

    @JsonIgnore
    private Date endDays;

    private ReportQuarterlyOnboardingTotalVO total;
}
