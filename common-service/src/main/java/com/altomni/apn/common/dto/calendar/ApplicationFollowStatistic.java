package com.altomni.apn.common.dto.calendar;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ApplicationFollowStatistic {
    private List<SystemCalendarStatisticItem> notSubmitToClient = new ArrayList<>();
    private Integer notSubmitToClientTotal = 0;
    private List<SystemCalendarStatisticItem> submitToClientNotUpdateStatus = new ArrayList<>();
    private Integer submitToClientNotUpdateStatusTotal = 0;
    private List<SystemCalendarStatisticItem> offerPassNotUpdateStatus = new ArrayList<>();
    private Integer offerPassNotUpdateStatusTotal = 0;
    private List<SystemCalendarStatisticItem> paymentOverdue = new ArrayList<>();
    private Integer paymentOverdueTotal = 0;

    public void count() {
        notSubmitToClientTotal = notSubmitToClient.size();
        submitToClientNotUpdateStatusTotal = submitToClientNotUpdateStatus.size();
        offerPassNotUpdateStatusTotal = offerPassNotUpdateStatus.size();
        paymentOverdueTotal = paymentOverdue.size();
    }
}
