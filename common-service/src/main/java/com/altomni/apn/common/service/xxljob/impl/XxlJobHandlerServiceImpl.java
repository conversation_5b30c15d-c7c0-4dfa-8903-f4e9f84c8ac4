package com.altomni.apn.common.service.xxljob.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.config.xxljob.XxlJobHandler;
import com.altomni.apn.common.domain.calendar.CalendarEvent;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageDeleteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.message.Message;
import com.altomni.apn.common.domain.message.MessageUserRelation;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.calendar.CalendarEventAttendeeDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventRelationDTO;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.xxljob.*;
import com.altomni.apn.common.enumeration.enums.InvoiceTypeEnum;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.enumeration.reportSubscriptions.DataPeriod;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushMethod;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.repository.calendar.CalendarEventRepository;
import com.altomni.apn.common.repository.message.MessageRepository;
import com.altomni.apn.common.repository.message.MessageUserRelationRepository;
import com.altomni.apn.common.repository.reportsubscription.ReportSubscriptionCustomRepository;
import com.altomni.apn.common.repository.statistic.TalentRecruitmentProcessStopStatisticsRepository;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.service.calendar.CalendarEventService;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.service.job.JobService;
import com.altomni.apn.common.service.lark.LarkClient;
import com.altomni.apn.common.service.lark.LarkMessageNotification;
import com.altomni.apn.common.service.report.ReportService;
import com.altomni.apn.common.service.sse.SseClient;
import com.altomni.apn.common.service.talent.TalentService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.service.xxljob.AsyncSseService;
import com.altomni.apn.common.service.xxljob.XxlJobHandlerService;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.calendar.CalendarEventAttendeeVO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionVO;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantMessageConfigConstants.*;
import static com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum.TEAM_MEMBER_OVERDUE_ONBOARDING_DAYS;
import static com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum.TEAM_MEMBER_UNSUBMITTED_CANDIDATE_REMINDER_TIME;
import static com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum.*;

@Slf4j
@RefreshScope
@Service("xxlJobHandlerService")
public class XxlJobHandlerServiceImpl implements XxlJobHandlerService {

    @Resource
    private CalendarEventService calendarEventService;

    @Resource
    private MessageRepository messageRepository;

    @Resource
    private MessageUserRelationRepository messageUserRelationRepository;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private SseClient sseClient;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    @Resource
    private UserService userService;

    @Resource
    private JobService jobService;

    @Resource
    private LarkMessageNotification larkMessageNotification;

    @Resource
    private TalentService talentService;

    @Value("${application.team-warm-permission-id:917}")
    private Long teamWarmPermissionId;

    @Value("${application.team-unOnboard-sendTime:10}")
    private Integer unOnboardSendTime;

    @Value("${application.team-unsubmit-talent-send-day:1}")
    private Integer unSubmitTalentSendDay;

    @Resource
    private ReportSubscriptionCustomRepository reportSubscriptionCustomRepository;

    @Resource
    private ReportService reportService;

    @Resource
    private LarkClient larkClient;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Resource
    private CampaignService campaignService;

    /**
     * report subscription
     * @param reportSubscriptionHandlerDTO
     */
    @XxlJobHandler(values = {REPORT_SUBSCRIPTION})
    @Transactional(rollbackFor = Exception.class)
    public void reportSubscriptionHandler(XxlJobReportSubscriptionHandlerDTO reportSubscriptionHandlerDTO) {
        log.info("report subscriptionHandler is start, id = {}, userId = {}, tenantId = {}", reportSubscriptionHandlerDTO.getSubscriptionId(), reportSubscriptionHandlerDTO.getUserId(), reportSubscriptionHandlerDTO.getTenantId());
        if (!checkUser(reportSubscriptionHandlerDTO.getUserId())) {
            return;
        }
        ReportSubscriptionVO reportSubscriptionVO = reportSubscriptionCustomRepository.getReportSubscriptionDetailById(reportSubscriptionHandlerDTO.getSubscriptionId());
        if (reportSubscriptionVO == null) {
            log.info("report subscriptionHandler is end, id = {}, userId = {}, tenantId = {}, report subscription is not exist", reportSubscriptionHandlerDTO.getSubscriptionId(), reportSubscriptionHandlerDTO.getUserId(), reportSubscriptionHandlerDTO.getTenantId());
            return;
        }
        //判断订阅是否有结束时间
        LocalDate subEndDate = reportSubscriptionVO.getEndDate();
        LocalDate nowLocalDateForTimezone = Instant.now().atZone(ZoneId.of(reportSubscriptionHandlerDTO.getTimezone())).toLocalDate();
        if (subEndDate != null && subEndDate.isBefore(nowLocalDateForTimezone)) {
            log.info("report subscriptionHandler is end, id = {}, userId = {}, tenantId = {}, report subscription is expired", reportSubscriptionHandlerDTO.getSubscriptionId(), reportSubscriptionHandlerDTO.getUserId(), reportSubscriptionHandlerDTO.getTenantId());
            //删除数据
            xxlJobService.deleteXxlJob(reportSubscriptionHandlerDTO.getXxlJobId());
            return;
        }
        List<UserBriefDTO> userBriefDTOList = userService.getBriefUsersByIds(List.of(reportSubscriptionHandlerDTO.getUserId())).getBody();
        if (CollUtil.isEmpty(userBriefDTOList)) {
            log.info("report subscriptionHandler is end, user is no found");
            return;
        }
        LocalDate[] dates = calculateDatesWithPrevious(reportSubscriptionVO.getDataPeriod(), nowLocalDateForTimezone);
        log.info("report subscriptionHandler param , id = {}, nowLocalDate = {}, param = {}", reportSubscriptionHandlerDTO.getSubscriptionId(), nowLocalDateForTimezone, dates);
        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
        searchDto.setXxlJobFlag(true);
        searchDto.setTimezone(reportSubscriptionHandlerDTO.getTimezone());
        searchDto.setGroupByFieldList(List.of(getGroupByFieldTypeByDataPeriod(reportSubscriptionVO.getDataPeriod())));
        searchDto.setSearchUserId(reportSubscriptionHandlerDTO.getUserId());
        searchDto.setSearchTenantId(reportSubscriptionHandlerDTO.getTenantId());
        searchDto.setDateType(RecruitingKpiDateType.EVENT);
        searchDto.setUserIdList(reportSubscriptionVO.getUserIdList());
        searchDto.setTeamIdList(reportSubscriptionVO.getTeamIdList());
        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
        Map<String, LinkedList<Pair<String, Long>>> currentMapList;
        Map<String, LinkedList<Pair<String, Long>>> lastMapList;
        if (ReportType.BY_USER == reportSubscriptionVO.getReportType()) {
            searchDto.setStartDate(dates[0].toString());
            searchDto.setEndDate(dates[1].toString());
            List<RecruitingKpiByUserVO> voList = reportService.searchRecruitingKpiReportByUser(searchDto).getBody();
            RecruitingKpiByUserVO nowVo = new RecruitingKpiByUserVO();
            if (CollUtil.isNotEmpty(voList)) {
                nowVo = voList.get(0);
            }
            searchDto.setStartDate(dates[2].toString());
            searchDto.setEndDate(dates[3].toString());
            List<RecruitingKpiByUserVO> lastVoList = reportService.searchRecruitingKpiReportByUser(searchDto).getBody();
            RecruitingKpiByUserVO lastVo = new RecruitingKpiByUserVO();
            if (CollUtil.isNotEmpty(lastVoList)) {
                lastVo = lastVoList.get(0);
            }
            currentMapList = getStringLinkedListMap(dates[0].toString(), dates[1].toString(), nowVo.getOpenings(), nowVo.getTalentNum(), nowVo.getSubmitToJobNum(), nowVo.getSubmitToClientNum(), nowVo.getInterviewNum(), nowVo.getOfferNum(), nowVo.getOnboardNum(), null, false);
            lastMapList = getStringLinkedListMap(dates[2].toString(), dates[3].toString(), lastVo.getOpenings(), lastVo.getTalentNum(), lastVo.getSubmitToJobNum(), lastVo.getSubmitToClientNum(), lastVo.getInterviewNum(), lastVo.getOfferNum(), lastVo.getOnboardNum(), null, false);
        } else {
            searchDto.setStartDate(dates[0].toString());
            searchDto.setEndDate(dates[1].toString());
            List<RecruitingKpiByCompanyVO> voList = reportService.searchRecruitingKpiReportByCompany(searchDto).getBody();
            RecruitingKpiByCompanyVO nowVo = new RecruitingKpiByCompanyVO();
            if (CollUtil.isNotEmpty(voList)) {
                nowVo = voList.get(0);
            }
            searchDto.setStartDate(dates[2].toString());
            searchDto.setEndDate(dates[3].toString());
            List<RecruitingKpiByCompanyVO> lastVoList = reportService.searchRecruitingKpiReportByCompany(searchDto).getBody();
            RecruitingKpiByCompanyVO lastVo = new RecruitingKpiByCompanyVO();
            if (CollUtil.isNotEmpty(lastVoList)) {
                lastVo = lastVoList.get(0);
            }
            currentMapList = getStringLinkedListMap(dates[0].toString(), dates[1].toString(), nowVo.getOpenings(), nowVo.getTalentNum(), nowVo.getSubmitToJobNum(), nowVo.getSubmitToClientNum(), nowVo.getInterviewNum(), nowVo.getOfferNum(), nowVo.getOnboardNum(), nowVo.getCompanyNum(), true);
            lastMapList = getStringLinkedListMap(dates[2].toString(), dates[3].toString(), lastVo.getOpenings(), lastVo.getTalentNum(), lastVo.getSubmitToJobNum(), lastVo.getSubmitToClientNum(), lastVo.getInterviewNum(), lastVo.getOfferNum(), lastVo.getOnboardNum(), lastVo.getCompanyNum(), true);
        }
        //发送消息
        byte[] imageBytes = ChartUtil.getByteFromChart(currentMapList, lastMapList);
        List<PushMethod> pushMethodList = reportSubscriptionVO.getPushMethodList();
        for (PushMethod pushMethod : pushMethodList) {
            sendReportSubscriptionMessage(reportSubscriptionVO, userBriefDTOList.get(0), pushMethod, imageBytes, reportSubscriptionVO.getReportType(), dates);
        }
    }

    private void sendReportSubscriptionMessage(ReportSubscriptionVO reportSubscriptionVO, UserBriefDTO userBriefDTO, PushMethod pushMethod, byte[] imageBytes, ReportType reportType, LocalDate[] dates) {
        String linkUrl = baseUrl + (reportType == ReportType.BY_USER? "/reports/detailV3/KpiByUser": "/reports/detailV3/KpiByCompany") + "?searchTime=" + dates[0].toString() + "," + dates[1].toString() + "&subscriptionId=" + reportSubscriptionVO.getId();
        if (pushMethod == PushMethod.LARK) {
            Map<String, String> emailUserIdMap = larkClient.searchLarkUserIdByEmail(List.of(userBriefDTO.getEmail()));
            String larkUserId = emailUserIdMap.get(userBriefDTO.getEmail());
            if (StrUtil.isBlank(larkUserId)) {
                log.info("search lark userId is not found from lark, email = {}", userBriefDTO.getEmail());
                return;
            }
            String imageKey = larkClient.uploadImages(imageBytes);
            if (StrUtil.isBlank(imageKey)) {
                log.info("update images to lark is error");
                return;
            }
            larkClient.sendFeedCardUrl(imageKey, larkUserId, linkUrl, reportSubscriptionVO.getName());
        } else {
            String html = """
                    <html lang="en">
                      <head>
                        <meta charset="UTF-8" />
                        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                        <title>Document</title>
                        <style>
                          .tempBox {
                            padding: 12px 10px;
                          }
                          .tempBox h1 {
                            color: rgb(23 207 181);
                            font-size: 16px;
                          }
                          .tempBox a {
                            color: #1890ff;
                            width: 104px;
                            height: 30px;
                            border-radius: 4px;
                            border: 1px solid #1890ff;
                            text-decoration: none;
                            text-align: center;
                            line-height: 30px;
                            display: inline-block;
                            font-size: 14px;
                          }
                          .tempBox img {
                            margin-bottom: 20px;
                          }
                        </style>
                      </head>
                      <body>
                        <div class="tempBox">
                          <h1>{subscriptionName}</h1>
                          <img
                            src="{imageBase64}"
                            alt=""
                            srcset=""
                          />
                          <div><a href="{linkUrl}">Click to view</a></div>
                        </div>
                      </body>
                    </html>
                    """;

            String base64String = Base64.getEncoder().encodeToString(imageBytes);
            Map<String, String> map = new HashMap<>(16);
            map.put("imageBase64", "data:image/jpg;base64," + base64String);
            map.put("linkUrl", linkUrl);
            map.put("subscriptionName", reportSubscriptionVO.getName());
            html = StrUtil.format(html, map);
            try {
                log.info(" report subscription send email is start , userId = {}, email = {}", userBriefDTO.getId(), userBriefDTO.getEmail());
                campaignService.sendHtmlMail(supportSender, CollUtil.newArrayList(userBriefDTO.getEmail()), null, null, reportSubscriptionVO.getName(), html, null, null, true, false, true);
                log.info(" report subscription send email is success , userId = {}, email = {}", userBriefDTO.getId(), userBriefDTO.getEmail());
            } catch (Exception e) {
                log.error(" report subscription send email is error = {}", ExceptionUtil.getAllExceptionMsg(e));
            }
        }
    }


    private Map<String, LinkedList<Pair<String, Long>>> getStringLinkedListMap(String startDate, String endDate,
            Long openings, Long talentNum, Long submitToJobNum, Long submitToClientNum, Long interviewNum, Long offerNum, Long onboardNum, Long companyNum, boolean isByCompanyFlag) {
        Map<String, LinkedList<Pair<String, Long>>> map = new HashMap<>(16);
        LinkedList<Pair<String, Long>> pairLinkedList = new LinkedList<>();
        Function<Long, Long> getSumOrDefaultFunction = sum -> Optional.ofNullable(sum).orElse(0L);
        if (BooleanUtil.isTrue(isByCompanyFlag)) {
            pairLinkedList.addLast(new Pair<>("Sum of Companies", getSumOrDefaultFunction.apply(companyNum)));
        }
        pairLinkedList.addLast(new Pair<>("Sum of Openings", getSumOrDefaultFunction.apply(openings)));
        pairLinkedList.addLast(new Pair<>("Created Candidates", getSumOrDefaultFunction.apply(talentNum)));
        pairLinkedList.addLast(new Pair<>("Submitted to Job", getSumOrDefaultFunction.apply(submitToJobNum)));
        pairLinkedList.addLast(new Pair<>("Submitted to Client", getSumOrDefaultFunction.apply(submitToClientNum)));
        pairLinkedList.addLast(new Pair<>("Sum of Interview", getSumOrDefaultFunction.apply(interviewNum)));
        pairLinkedList.addLast(new Pair<>("Offered", getSumOrDefaultFunction.apply(offerNum)));
        pairLinkedList.addLast(new Pair<>("On Boarded", getSumOrDefaultFunction.apply(onboardNum)));
        map.put(startDate + "~" + endDate, pairLinkedList);
        return map;
    }



    /**
     * 日程消息提醒处理器
     * @param xxlJobCalendarEventMessageHandlerDTO
     */
    @Override
    @XxlJobHandler(values = {CALENDAR})
    @Transactional(rollbackFor = Exception.class)
    public void calendarEventMessageHandler(XxlJobCalendarEventMessageHandlerDTO xxlJobCalendarEventMessageHandlerDTO) {
        log.info("send message for calendar event is start, eventId = {}", xxlJobCalendarEventMessageHandlerDTO.getEventId());
        CalendarEventVO calendarEventVO = calendarEventService.getCalendarEventById(xxlJobCalendarEventMessageHandlerDTO.getEventId());
        if (calendarEventVO == null) {
            return;
        }
        List<CalendarEventAttendeeVO> attendees = calendarEventVO.getAttendees();
        Message message = new Message();
        message.setCnTitle(calendarEventVO.getTitle());
        message.setEnTitle(calendarEventVO.getTitle());
        message.setContent(calendarEventVO.getDescription());
        message.setType(MessageTypeEnum.SYSTEM_MESSAGE);
        message.setTenantId(calendarEventVO.getTenantId());
        JSONObject contextJson = MessageUtil.setContextJsonWithPrimitiveMessage(CALENDAR, message, 1);
        message.setContent(JSONUtil.toJsonStr(contextJson));
        messageRepository.save(message);
        List<MessageUserRelation> messageUserRelationList = new ArrayList<>();
        attendees.forEach(attendee -> {
            //提醒
            if (Objects.equals(attendee.getIsReminder(), CalendarEventAttendeeReminderTypeEnum.REMINDER)) {
                MessageUserRelation messageUserRelation = new MessageUserRelation();
                messageUserRelation.setUserId(attendee.getUserId());
                messageUserRelation.setMessageId(message.getId());
                messageUserRelationList.add(messageUserRelation);
            }
        });
        messageUserRelationRepository.saveAll(messageUserRelationList);
        //delete xxlJob
        xxlJobService.deleteXxlJob(xxlJobCalendarEventMessageHandlerDTO.getXxlJobId());
        log.info("send message for calendar event is success, eventId = {}", xxlJobCalendarEventMessageHandlerDTO.getEventId());
        //push sse
        attendees.forEach(attendee -> sseClient.pushSseReminderMessageRefresh(attendee.getUserId(), xxlJobCalendarEventMessageHandlerDTO.getTenantId()));
    }

    /**
     * 职位提醒，为提交候选人，为面试
     * @param xxlJobReminderForJobHandlerDTO
     */
    @Override
//    @XxlJobHandler(values = {JOB_NO_SUMMIT_CANDIDATE_WARN, JOB_NO_INTERVIEW_WARN})
    @Transactional(rollbackFor = Exception.class)
    public void reminderForJobHandler(XxlJobReminderForJobHandlerDTO xxlJobReminderForJobHandlerDTO) {
        //TODO 待删除
        log.info("send message for reminder job is start, param = {}", xxlJobReminderForJobHandlerDTO);
//        if (ObjectUtil.isNotEmpty(xxlJobReminderForJobHandlerDTO.getJobId())) {
//            String jobTitle = jobService.getJobTitleByJobId(xxlJobReminderForJobHandlerDTO.getJobId()).getBody();
//            Map<String, String> map = new HashMap<>();
//            map.put("jobTitle", jobTitle);
//            map.put("day", xxlJobReminderForJobHandlerDTO.getReminderConfig() + "");
//            JSONObject contentJson = MessageUtil.setContextJsonWithMap(xxlJobReminderForJobHandlerDTO.getXxlJobType(), map, 1);
//            xxlJobReminderForJobHandlerDTO.setContent(JSONUtil.toJsonStr(contentJson));
//        }
//        createMessage(xxlJobReminderForJobHandlerDTO);
//        //延时
//        Instant triggerTimeInstant = xxlJobReminderForJobHandlerDTO.getSendTime().plus(xxlJobReminderForJobHandlerDTO.getReminderConfig(), ChronoUnit.DAYS);
//        List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
//        paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder().xxlJobId(xxlJobReminderForJobHandlerDTO.getXxlJobId()).timezone(xxlJobReminderForJobHandlerDTO.getTimezone())
//                .reminderConfig(xxlJobReminderForJobHandlerDTO.getReminderConfig() + "").sendTime(triggerTimeInstant).cron(DateUtil.getCron(triggerTimeInstant)).build());
//        xxlJobService.updateJobsBySendTime(paramDtoList);
//        //push sse
//        sseClient.pushSseReminderMessageRefresh(xxlJobReminderForJobHandlerDTO.getUserId(), xxlJobReminderForJobHandlerDTO.getTenantId());
        log.info("send message for reminder job is success, param = {}", xxlJobReminderForJobHandlerDTO);
    }

    /**
     * admin 消息处理
     * @param dto
     */
    @Override
    @XxlJobHandler(values = {ADMIN_MESSAGE})
    @Transactional(rollbackFor = Exception.class)
    public void adminMessageHandler(XxlJobAdminMessageHandlerDTO dto) {
        log.info("send message for admin message is start, param = {}", dto);
        if (!checkUser(dto.getUserId())) {
            return;
        }
        //修改 admin message 的状态为 no delete
        MessageUserRelation messageUserRelation = new MessageUserRelation();
        messageUserRelation.setUserId(dto.getUserId());
        messageUserRelation.setMessageId(dto.getMessageId());
        messageUserRelationRepository.save(messageUserRelation);
        //delete message
        xxlJobService.deleteXxlJob(dto.getXxlJobId());
        //push sse
        sseClient.pushSseReminderMessageRefresh(dto.getUserId(), dto.getTenantId());
        log.info("send message for admin message is success, param = {}", dto);
    }

    /**
     * 候选人逾期未入职，候选人入职未开发票
     * @param xxlJobReminderForApplicationDTO
     */
    @Override
    @XxlJobHandler(values = {TALENT_NO_ONBOARD_WARN, TALENT_ONBOARD_NO_INVOICE_WARN})
    @Transactional(rollbackFor = Exception.class)
    public void reminderForApplicationHandler(XxlJobReminderForApplicationDTO xxlJobReminderForApplicationDTO) {
        log.info("send message for reminder application is start, param = {}", xxlJobReminderForApplicationDTO);
        if (!checkUser(xxlJobReminderForApplicationDTO.getUserId())) {
            return;
        }
        if (ObjectUtil.isNotEmpty(xxlJobReminderForApplicationDTO.getTalentId())) {
            String fullName = talentService.findFullNameByTalentId(xxlJobReminderForApplicationDTO.getTalentId()).getBody();
            Map<String, String> map = new HashMap<>();
            map.put("talentName", fullName);
            JSONObject contentJson = MessageUtil.setContextJsonWithMap(xxlJobReminderForApplicationDTO.getXxlJobType(), map, 1);
            xxlJobReminderForApplicationDTO.setContent(JSONUtil.toJsonStr(contentJson));
        }
        createMessage(xxlJobReminderForApplicationDTO);
        //delete message
        xxlJobService.deleteXxlJob(xxlJobReminderForApplicationDTO.getXxlJobId());
        //push sse
        sseClient.pushSseReminderMessageRefresh(xxlJobReminderForApplicationDTO.getUserId(), xxlJobReminderForApplicationDTO.getTenantId());
        log.info("send message for reminder application is success, param = {}", xxlJobReminderForApplicationDTO);
    }

    /**
     * 发票逾期提醒，fte和contract
     * @param xxlJobReminderForInvoiceOverDueDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @XxlJobHandler(values = {TALENT_FTE_INVOICE_OVERDUE_WARN, TALENT_CONTRACT_INVOICE_OVERDUE_WARN})
    public void reminderForInvoiceOverDueHandler(XxlJobReminderForInvoiceOverDueDTO xxlJobReminderForInvoiceOverDueDTO) {
        log.info("send message for reminder invoice overDue is start, param = {}", xxlJobReminderForInvoiceOverDueDTO);
        if (!checkUser(xxlJobReminderForInvoiceOverDueDTO.getUserId())) {
            return;
        }
        if (ObjectUtil.isNotNull(xxlJobReminderForInvoiceOverDueDTO.getTalentId())) {
            String fullName = talentService.findFullNameByTalentId(xxlJobReminderForInvoiceOverDueDTO.getTalentId()).getBody();
            Map<String, String> map = new HashMap<>();
            map.put("talentName", fullName);
            map.put("invoiceNo", xxlJobReminderForInvoiceOverDueDTO.getInvoiceNo());
            JSONObject contentJson = MessageUtil.setContextJsonWithMap(xxlJobReminderForInvoiceOverDueDTO.getXxlJobType(), map, 1);
            xxlJobReminderForInvoiceOverDueDTO.setContent(JSONUtil.toJsonStr(contentJson));
        }
        createMessage(xxlJobReminderForInvoiceOverDueDTO);
        //delete message
        xxlJobService.deleteXxlJob(xxlJobReminderForInvoiceOverDueDTO.getXxlJobId());
        //push sse
        sseClient.pushSseReminderMessageRefresh(xxlJobReminderForInvoiceOverDueDTO.getUserId(), xxlJobReminderForInvoiceOverDueDTO.getTenantId());
        log.info("send message for reminder invoice overDue is success, param = {}", xxlJobReminderForInvoiceOverDueDTO);
    }

    /**
     * 团队未提交候选人数量提醒
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @XxlJobHandler(values = {TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN})
    public void reminderForUnSubmitCandidatesForTeamHandler(XxlJobReminderForUnSubmitCandidatesForTeamHandlerDTO dto) {
        log.info("send message for unSubmit candidates for team is start, param = {}", dto);
        if (!checkUser(dto.getUserId())) {
            return;
        }

        // 获取当前的Instant时间戳
        Instant currentInstant = Instant.now();
        //计算的时间为utc的上周日的12点到上上周日的12点
        ZonedDateTime zonedDateTime = currentInstant.atZone(ZoneId.of("UTC"))
                .with(TemporalAdjusters.previous(DayOfWeek.SUNDAY)).withHour(12).withMinute(0).withSecond(0).withNano(0);
        Instant startTime = zonedDateTime.plus(-7, ChronoUnit.DAYS).toInstant();
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG, dto.getTenantId()).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        Integer unSubmitCount = Integer.parseInt(fieldToValueMap.getOrDefault(TenantMessageMinderConfigFieldCodeEnum.TEAM_MEMBER_UNSUBMITTED_CANDIDATES_COUNT.toDbValue(), TEAM_MEMBER_UNSUBMITTED_DEFAULT_CANDIDATES_COUNT));
        List<XxlJobUnSumbitTalentForTeamDTO> xxlJobUnSubmitTalentForTeamDTOList = xxlJobRelationRepository
                .findUnSubmitToJobCountByUserIdAndCreatedDateAndPrivilegeId(dto.getUserId(), startTime, zonedDateTime.toInstant(), teamWarmPermissionId, unSubmitCount);
        if (CollUtil.isEmpty(xxlJobUnSubmitTalentForTeamDTOList)) {
            log.info("send message for unSubmit candidates for team is success, no message");
            return;
        }
        List<Message> messageList = new ArrayList<>();
        //集合成一条message
        List<String> userNameList = xxlJobUnSubmitTalentForTeamDTOList.stream().map(unSubmitDto -> {
            return CommonUtils.formatFullName(unSubmitDto.getFirstName(), unSubmitDto.getLastName());
        }).toList();
        //先删除上次提醒
        talentRecruitmentProcessStopStatisticsRepository.softDeleteMessageByTypeAndUserId(TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN.name(), dto.getUserId(), MessageDeleteEnum.DELETED.toDbValue(), MessageFavoriteEnum.NOT_FAVORITE.toDbValue(), MessageDeleteEnum.NOT_DELETED.toDbValue());
        Message message = new Message();
        message.setType(MessageTypeEnum.TEAM_WARNING);
        message.setTenantId(dto.getTenantId());
        Map<String, String> map = new HashMap<>();
        map.put("userName", StrUtil.join(", ", userNameList));
        map.put("number", unSubmitCount + "");
        JSONObject contentJson = MessageUtil.setContextJsonWithMap(dto.getXxlJobType(), map, 1);
        message.setContent(JSONUtil.toJsonStr(contentJson));
        messageList.add(message);
        messageRepository.saveAll(messageList);
        saveMessageUserRelationList(messageList, dto.getUserId());
        // 延续到下一个触发时间点
        List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
        Instant sendTime = dto.getSendTime().plus(7, ChronoUnit.DAYS);
        paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder().xxlJobId(dto.getXxlJobId()).reminderConfig(dto.getReminderConfig())
                .sendTime(sendTime).cron(DateUtil.getCron(sendTime)).timezone("UTC").build());
        xxlJobService.updateJobsBySendTime(paramDtoList);
        sseClient.pushSseReminderMessageRefresh(dto.getUserId(), dto.getTenantId());
        log.info("send message for unSubmit candidates for team is success, param = {}", dto);
    }

    private boolean checkUser(Long userId) {
        List<UserBriefDTO> userBriefDTOList = userService.getBriefUsersByIds(List.of(userId)).getBody();
        if (CollUtil.isEmpty(userBriefDTOList)) {
            return false;
        }
        UserBriefDTO userBriefDTO = userBriefDTOList.get(0);
        return userBriefDTO.isActivated();
    }

    private void saveMessageUserRelationList(List<Message> messageList, Long userId) {
        if (CollUtil.isNotEmpty(messageList)) {
            List<MessageUserRelation> messageUserRelationList = messageList.stream().map(msg -> {
                MessageUserRelation messageUserRelation = new MessageUserRelation();
                messageUserRelation.setMessageId(msg.getId());
                messageUserRelation.setUserId(userId);
                return messageUserRelation;
            }).toList();
            messageUserRelationRepository.saveAll(messageUserRelationList);
        }
    }

    private void createMessage(XxlJobBaseDTO xxlJobBaseDTO) {
        Message message = new Message();
        BeanUtil.copyProperties(xxlJobBaseDTO, message, "sendTime");
        messageRepository.save(message);
        MessageUserRelation messageUserRelation = new MessageUserRelation();
        messageUserRelation.setUserId(xxlJobBaseDTO.getUserId());
        messageUserRelation.setMessageId(message.getId());
        messageUserRelationRepository.save(messageUserRelation);
    }


    /**
     * 每个小时执行一次， 团队未入职提醒
     */
    @Override
    @XxlJobHandler(values = {TEAM_OVERDUE_ONBOARD_WARN})
    public void createNoOnboardForTeam(XxlJobBaseDTO xxlJobBaseDTO) {
        log.info("reminder no onboard for team is start");
        //获取有查看团队权限的用户信息
        List<XxlJobForTeamDTO> xxlJobForTeamDTOList = xxlJobRelationRepository.findUserTeamDTOByPrivilegeId(teamWarmPermissionId);
        if (CollUtil.isNotEmpty(xxlJobForTeamDTOList)) {
            Instant now = Instant.now();
            //根据租户分组
            Map<Long, List<XxlJobForTeamDTO>> tenantListMap = xxlJobForTeamDTOList.stream().collect(Collectors.groupingBy(XxlJobForTeamDTO::getTenantId));
            tenantListMap.forEach((tenantId, tenantList) -> {
                //租户配置
                TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG, tenantId).getBody();
                Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
                int overDueOnBoardDays = Integer.parseInt(fieldToValueMap.getOrDefault(TEAM_MEMBER_OVERDUE_ONBOARDING_DAYS.toDbValue(), TEAM_MEMBER_OVERDUE_ONBOARDING_DEFAULT_DAYS));
                log.info("create no Onboard for team reminder message, tenantId = {}, overDueOnBoardDays = {}", tenantId, overDueOnBoardDays);
                Map<Long, List<XxlJobForTeamDTO>> teamListMap = tenantList.stream().collect(Collectors.groupingBy(XxlJobForTeamDTO::getTeamId));
                teamListMap.forEach((teamId, teamList) -> {
                    //不同的team的用户成员不一样,参与的流程不一致
                    List<XxlJobOnboardDateByTeamDTO> xxlJobOnboardDateByTeamDTOList = xxlJobRelationRepository.findOnboardDateByTeamId(teamId);
                    if (CollUtil.isNotEmpty(xxlJobOnboardDateByTeamDTOList)) {
                        List<Message> messages = new ArrayList<>();
                        teamList.forEach(xxlJobForTeamDTO -> {
                            //用户有自定义的timezone
                            if (StrUtil.isBlank(xxlJobForTeamDTO.getTimezone())) {
                                return;
                            }
                            ZonedDateTime zonedDateTime = now.atZone(ZoneId.of(xxlJobForTeamDTO.getTimezone()));
                            log.info("create no Onboard for team reminder message, tenantId = {}, teamId = {}, userId = {}, zonedDateTime = {}, unOnboardSendTime = {}, flag = {} ", tenantId, teamId, xxlJobForTeamDTO.getUserId(), zonedDateTime, unOnboardSendTime, Objects.equals(zonedDateTime.getHour(), unOnboardSendTime));
                            if (!Objects.equals(zonedDateTime.getHour(), unOnboardSendTime)) {
                                return;
                            }
                            xxlJobOnboardDateByTeamDTOList.forEach(onboardDateDTO -> {
                                if (ObjectUtil.isNull(onboardDateDTO.getOnboardDate())) {
                                    log.info("create no Onboard for team reminder , onboardDateDto = {}", JSONUtil.toJsonStr(onboardDateDTO));
                                    return;
                                }
                                int days = Period.between(onboardDateDTO.getOnboardDate(), zonedDateTime.toLocalDate()).getDays();
                                // onboardDate 必须 小于当前时间,且是配置天数的倍数时,发送提醒
                                if (days > 0 && days % overDueOnBoardDays == 0) {
                                    log.info("create no Onboard for team reminder message is success, tenantId = {}, teamId = {}, userId = {}", tenantId, teamId, xxlJobForTeamDTO.getUserId());
                                    Message message = new Message();
                                    message.setTenantId(tenantId);
                                    message.setType(MessageTypeEnum.TEAM_WARNING);
                                    Map<String, String> map = new HashMap<>();
                                    map.put("userName", CommonUtils.formatFullName(onboardDateDTO.getFirstName(), onboardDateDTO.getLastName()));
                                    map.put("talentName", onboardDateDTO.getFullName());
                                    JSONObject contentJson = MessageUtil.setContextJsonWithMap(xxlJobBaseDTO.getXxlJobType(), map, 1);
                                    message.setContent(JSONUtil.toJsonStr(contentJson));
                                    messages.add(message);
                                }
                            });
                            if (CollUtil.isNotEmpty(messages)) {
                                messageRepository.saveAll(messages);
                                saveMessageUserRelationList(messages, xxlJobForTeamDTO.getUserId());
                                sseClient.pushSseReminderMessageRefresh(xxlJobForTeamDTO.getUserId(), tenantId);
                            }
                        });
                    }
                });
            });
        }
    }


    /**
     * 初始化未提交候选人提醒
     */
    @Override
    public void initCreateOrUpdateUnSubmittedCandidatesForTeam() {
        log.info("init reminder un submitted candidates for team is start");
        List<XxlJobForTeamDTO> xxlJobForTeamDTOList = xxlJobRelationRepository.findUserTeamDTOByPrivilegeId(teamWarmPermissionId);
        xxlJobForTeamDTOList = xxlJobForTeamDTOList.stream().filter(xxlJobForTeamDTO -> StrUtil.isNotBlank(xxlJobForTeamDTO.getTimezone())).toList();
        if (CollUtil.isNotEmpty(xxlJobForTeamDTOList)) {
            //所有租户
            Map<Long, List<XxlJobForTeamDTO>> tenantListMap = xxlJobForTeamDTOList.stream().collect(Collectors.groupingBy(XxlJobForTeamDTO::getTenantId));
            tenantListMap.forEach(this::doHandlerUnSubmitCandidatesForTeam);
        } else {
            List<XxlJobRelation> oldXxlJobList = xxlJobRelationRepository.findAllByType(TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN);
            if (CollUtil.isNotEmpty(oldXxlJobList)) {
                xxlJobService.deleteXxlJobIdList(oldXxlJobList.stream().map(XxlJobRelation::getXxlJobId).toList());
            }
        }
    }

    /**
     * 团队未提交候选人数提醒
     */
    @Override
    public void createOrUpdateUnSubmittedCandidatesForTeam(Long tenantId) {
        log.info("reminder un submitted candidates for team is start");
        List<XxlJobForTeamDTO> xxlJobForTeamDTOList = xxlJobRelationRepository.findUserTeamDTOByPrivilegeIdAndTenantIdList(teamWarmPermissionId, CollUtil.newArrayList(tenantId));
        xxlJobForTeamDTOList = xxlJobForTeamDTOList.stream().filter(xxlJobForTeamDTO -> StrUtil.isNotBlank(xxlJobForTeamDTO.getTimezone())).toList();
        if (CollUtil.isNotEmpty(xxlJobForTeamDTOList)) {
            doHandlerUnSubmitCandidatesForTeam(tenantId, xxlJobForTeamDTOList);
        } else {
            List<XxlJobRelation> oldXxlJobList = xxlJobRelationRepository.findAllByTypeAndTenantId(TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN, tenantId);
            if (CollUtil.isNotEmpty(oldXxlJobList)) {
                xxlJobService.deleteXxlJobIdList(oldXxlJobList.stream().map(XxlJobRelation::getXxlJobId).toList());
            }
        }
    }

    private void doHandlerUnSubmitCandidatesForTeam(Long tenantId, List<XxlJobForTeamDTO> xxlJobForTeamDTOList) {
        //不同租户配置不一样
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        String unSubmitCandidateReminderTime = fieldToValueMap.getOrDefault(TEAM_MEMBER_UNSUBMITTED_CANDIDATE_REMINDER_TIME.toDbValue(), MESSAGE_DEFAULT_REMINDER_TIME);
        log.info("reminder un submitted candidates for team , tenantId = {}, unSubmitCandidateReminderTime = {}", tenantId, unSubmitCandidateReminderTime);
        List<Long> userIdList = xxlJobForTeamDTOList.stream().map(XxlJobForTeamDTO::getUserId).toList();
        Map<Long, XxlJobForTeamDTO> userIdMap = xxlJobForTeamDTOList.stream().collect(Collectors.toMap(XxlJobForTeamDTO::getUserId, a->a, (a1,a2) -> a1));
        //查询出已经生成的任务,并删除
        List<XxlJobRelation> oldXxlJobList = xxlJobRelationRepository.findAllByTypeAndTenantId(TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN, tenantId);
        if (CollUtil.isNotEmpty(oldXxlJobList)) {
            List<Integer> deleteXxlJobIdList = oldXxlJobList.stream().map(XxlJobRelation::getXxlJobId).filter(ObjectUtil::isNotEmpty).toList();
            xxlJobService.deleteXxlJobIdList(deleteXxlJobIdList);
        }
        //重新生成
        if (CollUtil.isNotEmpty(userIdList)) {
            //装作 xxl-job 任务
            List<XxlJobApnDTO> xxlJobApnDTOList = new ArrayList<>();
            userIdList.forEach(userId -> {
                XxlJobForTeamDTO xxlJobForTeamDTO = userIdMap.get(userId);
                ZonedDateTime currentDateTime = ZonedDateTime.now(ZoneId.of(xxlJobForTeamDTO.getTimezone()));
                // 检查当前时间是否是周一
                String[] times = unSubmitCandidateReminderTime.split(":");
                int hours = Integer.parseInt(times[0]);
                int minutes = Integer.parseInt(times[1]);
                if (currentDateTime.getDayOfWeek().getValue() == unSubmitTalentSendDay) {
                    // 如果是周一并且当前时间已经是9:00 AM之后，就获取下一个周一  format "10:00"
                    if (currentDateTime.toLocalTime().isAfter(LocalTime.of(hours, minutes))) {
                        // 下一个星期一
                        currentDateTime = currentDateTime.plusWeeks(1);
                    }
                } else {
                    // 获取下一个周一
                    currentDateTime = currentDateTime.with(TemporalAdjusters.next(DayOfWeek.of(unSubmitTalentSendDay)));
                }
                Instant triggerTime = currentDateTime.with(LocalTime.of(hours, minutes)).toInstant();
                log.info("reminder un submitted candidates for team add xxl job, tenantId = {}, userId = {}, unSubmitCandidateReminderTime = {}, currentDateTime = {}, triggerTime = {}", tenantId, userId, unSubmitCandidateReminderTime, currentDateTime, triggerTime);
                XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
                xxlJobApnDTO.setJobDesc("【预警：团队未提交候选人人数未达标】#tenantId=" + tenantId + "#userId=" + userId);
                //回填xxl-job 参数
                XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
                xxlJobApnParamDTO.setXxlJobType(TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN);
                xxlJobApnParamDTO.setReferenceId(userId);
                xxlJobApnParamDTO.setSendTime(triggerTime);
                xxlJobApnParamDTO.setReminderConfig(unSubmitCandidateReminderTime);
                xxlJobApnParamDTO.setTimezone(xxlJobForTeamDTO.getTimezone());
                xxlJobApnParamDTO.setTenantId(tenantId);
                xxlJobApnParamDTO.setUserId(userId);
                xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
                xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
                Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
                xxlJobApnParamDTO.setXxlJobParam(paramMap);
                xxlJobApnDTOList.add(xxlJobApnDTO);
            });
            xxlJobService.createXxlJobs(xxlJobApnDTOList);
        }
    }

    /**
     * 日程消息提醒处理器
     * @param xxlJobBaseDTO
     */
    @Override
//    @XxlJobHandler(values = {APPLICATION_NO_UPDATE_REMINDER})
    @Transactional(rollbackFor = Exception.class)
    public void applicationNoUpdateReminder(XxlJobReminderForApplicationNodeTypeDTO xxlJobBaseDTO) {
        //TODO 待删除
        log.info("send message for application no update start, param = {}", xxlJobBaseDTO);
//        int reminderConfig = StrUtil.isBlank(xxlJobBaseDTO.getReminderConfig())? 7: Integer.parseInt(xxlJobBaseDTO.getReminderConfig());
//        String fullName = talentService.findFullNameByTalentId(xxlJobBaseDTO.getTalentId()).getBody();
//        String jobName = jobService.getJobTitleByJobId(xxlJobBaseDTO.getJobId()).getBody();
//        Map<String, String> map = new HashMap<>();
//        map.put("jobTitle", jobName);
//        map.put("talentName", fullName);
//        map.put("applicationStatus", xxlJobBaseDTO.getStatus());
//        map.put("day", reminderConfig + "");
//        JSONObject contentJson = MessageUtil.setContextJsonWithMap(xxlJobBaseDTO.getXxlJobType(), map, 1);
//        xxlJobBaseDTO.setContent(JSONUtil.toJsonStr(contentJson));
//        createMessage(xxlJobBaseDTO);
//
//        // 延续到下一个触发时间点
//        List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
//        Instant sendTime = xxlJobBaseDTO.getSendTime().plus(reminderConfig, ChronoUnit.DAYS);
//        paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder().xxlJobId(xxlJobBaseDTO.getXxlJobId()).reminderConfig(reminderConfig + "")
//                .sendTime(sendTime).cron(DateUtil.getCron(sendTime)).timezone("UTC").build());
//        xxlJobService.updateJobsBySendTime(paramDtoList);
//        sseClient.pushSseReminderMessageRefresh(xxlJobBaseDTO.getUserId(), xxlJobBaseDTO.getTenantId());
        log.info("send message for unSubmit candidates for team is success, param = {}", xxlJobBaseDTO);
    }

    @Resource
    private TalentRecruitmentProcessStopStatisticsRepository talentRecruitmentProcessStopStatisticsRepository;


    @Value("${xxl.job.application.reminder.day:7}")
    private Long xxlJobApplicationReminderDay;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private AsyncSseService asyncSseService;

    @Override
    @XxlJobHandler(values = {APPLICATION_NO_UPDATE_REMINDER})
    @Transactional(rollbackFor = Exception.class)
    public void applicationStopReminder() {
        log.info("Application stop reminder scheduled tasks begin, config xxlJobApplicationReminderDay:{}.", xxlJobApplicationReminderDay);
        //拿到当前时区
        Set<String> currentZeroHourTimezones = getCurrentZeroHourTimezones();
        //统计新增以及更新的停滞流程
        talentRecruitmentProcessStopStatisticsRepository.insertOrUpdateStopStatistics(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay);
        //删除目前已结束的停滞流程
        talentRecruitmentProcessStopStatisticsRepository.deleteStopStatistics(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay);
        //先删除上次提醒
        talentRecruitmentProcessStopStatisticsRepository.softDeleteMessageByType(APPLICATION_NO_UPDATE_REMINDER.name(), currentZeroHourTimezones, MessageDeleteEnum.DELETED.toDbValue(), MessageFavoriteEnum.NOT_FAVORITE.toDbValue(), MessageDeleteEnum.NOT_DELETED.toDbValue());
        List<UserStoppedProcessCount> counts = talentRecruitmentProcessStopStatisticsRepository.findUserStoppedProcessCounts(currentZeroHourTimezones);
        //批处理保存message提高效率
        processAndSaveStoppedProcessCounts(counts);
        //刷新sse
        asyncSseService.refreshSseAsync(counts);
        log.info("Application stop reminder scheduled tasks end.");
    }

    @Autowired
    private NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public Long findDefaultConfig(String fieldName) {
        String sql = """
            SELECT
                jt.value
            FROM tenant_default_config tcc,
            JSON_TABLE(
                config_value,
                '$[*]' COLUMNS(
                    field VARCHAR(100) PATH '$.field',
                    value INT PATH '$.value'
                )
            ) as jt
            WHERE tcc.config_code = 3
            AND jt.field = :fieldName
            """;

        MapSqlParameterSource parameters = new MapSqlParameterSource()
                .addValue("fieldName", fieldName);

        // 如果确定只返回一个值，使用queryForObject
        try {
            return namedParameterJdbcTemplate.queryForObject(sql, parameters, Long.class);
        } catch (EmptyResultDataAccessException e) {
            return null; // 或者返回默认值
        }
    }

    private final RowMapper<UserJobStopCount> rowMapper = (rs, rowNum) -> {
        // 立即从 ResultSet 中获取所有数据
        Long tenantId = rs.getLong("tenantId");
        Long userId = rs.getLong("userId");
        Long count = rs.getLong("staleJobCount");
        String timezone = rs.getString("timezone");

        return new UserJobStopCount() {
            @Override
            public Long getTenantId() {
                return tenantId;
            }

            @Override
            public Long getUserId() {
                return userId;
            }

            @Override
            public Long getCount() {
                return count;
            }

            @Override
            public String getTimezone() {
                return timezone;
            }
        };
    };

    private static String notInterviewSql = """
            SELECT
                u.tenant_id as tenantId,
                user_list.user_id as userId,
                COUNT(DISTINCT j.id) as count,
                COALESCE(u.custom_timezone, 'UTC') as timezone
            FROM job j
            LEFT JOIN (
                SELECT id as job_id, puser_id as user_id
                FROM job
            ) user_list ON j.id = user_list.job_id
            LEFT JOIN user u ON user_list.user_id = u.id
            LEFT JOIN talent_recruitment_process trp ON j.id = trp.job_id
            LEFT JOIN (
                SELECT
                    talent_recruitment_process_id,
                    MAX(created_date) as last_interview_date
                FROM talent_recruitment_process_interview
                GROUP BY talent_recruitment_process_id
            ) trpi ON trp.id = trpi.talent_recruitment_process_id
            LEFT JOIN (
                SELECT
                    tcc.tenant_id,
                    jt.value
                FROM tenant_custom_config tcc,
                JSON_TABLE(
                    config_value,
                    '$[*]' COLUMNS(
                        field VARCHAR(100) PATH '$.field',
                        value INT PATH '$.value'
                    )
                ) as jt
                WHERE tcc.config_code = 3
                AND jt.field = 'POSITION_UNINTERVIEWED_DAYS'
            ) config ON config.tenant_id = u.tenant_id
            WHERE j.status = 0
            AND (
                (trpi.last_interview_date IS NULL AND DATEDIFF(NOW(), j.created_date) <= COALESCE(config.value, :betweenDay)) 
                OR DATEDIFF(NOW(), trpi.last_interview_date) >= COALESCE(config.value, :betweenDay)
            )
            AND COALESCE(u.custom_timezone, 'UTC') IN (:timezones)
            AND j.pteam_id NOT IN (
                SELECT COALESCE(jp.id, -1)
                FROM job_project jp
                WHERE jp.tenant_id = j.tenant_id
            )
            GROUP BY user_list.user_id
            """;

    private static String notsubmitSql = """
            SELECT 
                u.tenant_id as tenantId,
                user_list.user_id as userId,
                COUNT(DISTINCT j.id) as count,
                COALESCE(u.custom_timezone, 'UTC') as timezone
            FROM job j
            LEFT JOIN (
                SELECT j.id as job_id, j.puser_id as user_id
                FROM job j
            ) user_list ON j.id = user_list.job_id
            LEFT JOIN user u ON user_list.user_id = u.id
            LEFT JOIN talent_recruitment_process trp ON j.id = trp.job_id
            LEFT JOIN (
                SELECT
                    tcc.tenant_id,
                    jt.value
                FROM tenant_custom_config tcc,
                JSON_TABLE(
                    config_value,
                    '$[*]' COLUMNS(
                        field VARCHAR(100) PATH '$.field',
                        value INT PATH '$.value'
                    )
                ) as jt
                WHERE tcc.config_code = 3
                AND jt.field = 'POSITION_UNSUBMITTED_CANDIDATE_DAYS'
            ) config ON config.tenant_id = u.tenant_id
            WHERE j.status = 0 
            AND (
                (trp.created_date IS NULL AND DATEDIFF(NOW(), j.created_date) <= COALESCE(config.value, :betweenDay)) 
                OR DATEDIFF(NOW(), trp.created_date) >= COALESCE(config.value, :betweenDay)
            )
            AND COALESCE(u.custom_timezone, 'UTC') IN (:timezones)
            AND j.pteam_id NOT IN (
                SELECT COALESCE(jp.id, -1)
                FROM job_project jp
                WHERE jp.tenant_id = j.tenant_id
            )
            GROUP BY user_list.user_id
            """;

    public List<UserJobStopCount> findJobStatisticByTimezones(String sql, Set<String> timezones, Long betweenDay) {
        Query dataQuery = entityManager.createNativeQuery(sql);

        dataQuery.setParameter("betweenDay", betweenDay);
        dataQuery.setParameter("timezones", timezones);
        List<Object[]> resultList = dataQuery.getResultList();
        return resultList.stream().map(row -> {
            UserJobStopCount count = new UserJobStopCount();
            count.setTenantId(convertToLong(row[0]));
            count.setUserId(convertToLong(row[1]) );
            count.setCount(convertToLong(row[2]));
            count.setTimezone((String)(row[3]));
            return count;
        }).collect(Collectors.toList());
    }

    private Long convertToLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        return Long.parseLong(value.toString());
    }


    @Override
    @XxlJobHandler(values = {JOB_NO_SUMMIT_CANDIDATE_WARN, JOB_NO_INTERVIEW_WARN})
    @Transactional(rollbackFor = Exception.class)
    public void jobStagnationReminder() {
        Long xxlJobJobProcessNotSubmittedReminderDay = findDefaultConfig("POSITION_UNSUBMITTED_CANDIDATE_DAYS");
        Long xxlJobJobInterviewNotSubmittedReminderDay = findDefaultConfig("POSITION_UNINTERVIEWED_DAYS");
        log.info("Job stop reminder scheduled tasks begin. config xxlJobJobProcessNotSubmittedReminderDay:{}, xxlJobJobInterviewNotSubmittedReminderDay:{}", xxlJobJobProcessNotSubmittedReminderDay, xxlJobJobInterviewNotSubmittedReminderDay);
        Set<String> currentZeroHourTimezones = getCurrentZeroHourTimezones();
        List<UserJobStopCount> notSubmitJobByTimezones = findJobStatisticByTimezones(notsubmitSql, currentZeroHourTimezones, xxlJobJobProcessNotSubmittedReminderDay);
        List<UserJobStopCount> notInterviewJobsByTimezones = findJobStatisticByTimezones(notInterviewSql, currentZeroHourTimezones, xxlJobJobInterviewNotSubmittedReminderDay);

        //先删除上次提醒
        talentRecruitmentProcessStopStatisticsRepository.softDeleteMessageByType(JOB_NO_SUMMIT_CANDIDATE_WARN.name(), currentZeroHourTimezones, MessageDeleteEnum.DELETED.toDbValue(), MessageFavoriteEnum.NOT_FAVORITE.toDbValue(), MessageDeleteEnum.NOT_DELETED.toDbValue());
        talentRecruitmentProcessStopStatisticsRepository.softDeleteMessageByType(JOB_NO_INTERVIEW_WARN.name(), currentZeroHourTimezones, MessageDeleteEnum.DELETED.toDbValue(), MessageFavoriteEnum.NOT_FAVORITE.toDbValue(), MessageDeleteEnum.NOT_DELETED.toDbValue());
        processJobStoppedCounts(notSubmitJobByTimezones, JOB_NO_SUMMIT_CANDIDATE_WARN);
        processJobStoppedCounts(notInterviewJobsByTimezones, JOB_NO_INTERVIEW_WARN);
//        刷新sse
        asyncSseService.refreshSseAsync(getRefreshSseUser(notSubmitJobByTimezones, notInterviewJobsByTimezones));
        log.info("Job stop reminder scheduled tasks end.");
    }


    //清理更新时间大于多少天的消息
    @Value("${xxl.job.clean-up-message-interval.day:180}")
    private Long xxlJobCleanUpMessageIntervalDay;

    @Override
    @Transactional
    @XxlJobHandler(values = {REGULARLY_CLEAN_UP_MESSAGES})
    public void regularlyCleanUpMessages() {
        log.info("Regularly clean up messages scheduled tasks begin.");
        talentRecruitmentProcessStopStatisticsRepository.softDeleteMessageAndRelationsByDays(xxlJobCleanUpMessageIntervalDay, MessageDeleteEnum.DELETED.toDbValue(), MessageFavoriteEnum.NOT_FAVORITE.toDbValue(), MessageDeleteEnum.NOT_DELETED.toDbValue());
        log.info("Regularly clean up messages scheduled tasks begin.");
    }

    @Override
    @XxlJobHandler(values = {APN_ANNOUNCEMENT_MESSAGE})
    public void sendAPNAnnouncement(XxlJobApnAnnouncementDTO xxlJobApnAnnouncementDTO) throws InterruptedException, IOException {
        log.info("xxl-job handler send APN announcement");
        String token = larkMessageNotification.getToken();
        if(token == null || StringUtils.isBlank(token)) log.error("xxl-job handler send APN announcement get lark token is null!");
        List<String> userEmails = userService.getAllTenantUserEmails().getBody();
        for(int i = 0; userEmails != null && i < userEmails.size(); i += 20) {
            List<String> emails = userEmails.subList(i, Math.min(i + 20, userEmails.size()));
            List<String> larkUserIds = larkMessageNotification.obtainUserIdsByEmail(emails, token);
            String announcement = xxlJobApnAnnouncementDTO.getAnnouncementEN() + "\n\n" + xxlJobApnAnnouncementDTO.getAnnouncementCN();
            larkMessageNotification.batchSendLarkMessage(announcement, larkUserIds, token);
            Thread.sleep(300);
        }
        List<Integer> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(APN_ANNOUNCEMENT_MESSAGE, xxlJobApnAnnouncementDTO.getPlatformAnnouncementId()).stream().map(XxlJobRelation::getXxlJobId).toList();
        xxlJobService.deleteXxlJobIdList(xxlJobRelationList);
    }

    @Override
    @XxlJobHandler(values = {TALENT_DECLASSIFY})
    public void talentAutoDeclassify(XxlJobBaseDTO xxlJobBaseDTO) {
        log.info("xxl-job handler talent auto declassify");
        Integer xxlJobId = xxlJobBaseDTO.getXxlJobId();
        XxlJobRelation xxlJobRelation = xxlJobRelationRepository.findXxlJobRelationByXxlJobId(xxlJobId);
        Long talentId = xxlJobRelation.getReferenceId();
        String xxlJobParam = xxlJobRelation.getXxlJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(xxlJobParam);
        String declassifyReason = jsonObject.getStr("declassifyReason");
        if (StringUtils.isBlank(declassifyReason)) {
            log.error("xxl-job handler talent auto declassify get declassifyReason is null!");
        }
        TalentDeclassifyType talentDeclassifyType = TalentDeclassifyType.valueOf(declassifyReason);
        Long additionalParams = jsonObject.getLong("additionalParams");
        TalentAutoDeclassifyDto talentAutoDeclassifyDto = new TalentAutoDeclassifyDto();
        talentAutoDeclassifyDto.setTalentId(talentId);
        talentAutoDeclassifyDto.setDeclassifyType(talentDeclassifyType);
        talentAutoDeclassifyDto.setTalentRecruitmentProcessId(additionalParams);
        talentService.autoDeclassifyTalent(talentAutoDeclassifyDto);
    }

    private final static String SYSTEM_CALENDAR_BEGIN_DATETIME = "2025-05-10 00:00:00.000";

    @Resource
    private CalendarEventRepository calendarEventRepository;

    @Override
    @XxlJobHandler(values = {SYSTEM_CALENDAR_JOB_FOLLOW})
    public void systemCalendarJobFollow() {
        log.info("System calendar job follow scheduled tasks begin. ");
        List<Long> allTenant = xxlJobRelationRepository.findAllTenant();
        List<CalendarEvent> notSubmitTalentCalendarEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.NO_SUBMIT_TALENT);
        List<CalendarEvent> noInterviewCalendarEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.NO_INTERVIEW);
        for(Long tenantId : allTenant) {
            String configValue = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG, tenantId).getBody().getConfigValue();
            Map<String, String> configMap = convertToFieldMap(configValue);
            Long xxlJobJobProcessNotSubmittedReminderDay = Long.valueOf(configMap.get("POSITION_UNSUBMITTED_CANDIDATE_DAYS"));
            Long xxlJobJobInterviewNotSubmittedReminderDay = Long.valueOf(configMap.get("POSITION_UNINTERVIEWED_DAYS"));
            List<SystemCalendarResult> notSubmitTalentJob = xxlJobRelationRepository.findNotSubmitTalentJob(SYSTEM_CALENDAR_BEGIN_DATETIME, xxlJobJobProcessNotSubmittedReminderDay, tenantId);
            updateExistAttendee(notSubmitTalentCalendarEvent, notSubmitTalentJob, CalendarTypeEnum.NO_SUBMIT_TALENT);
            for(SystemCalendarResult record : notSubmitTalentJob) {
                calendarEventService.createCalendarEvent(getSystemCalendarEventDto(record.getJobId(), CalendarTypeEnum.NO_SUBMIT_TALENT, getAttendees(record), getRelation(record), record.getTenantId()));
            }
            List<SystemCalendarResult> notInterviewJob = xxlJobRelationRepository.findNotInterviewJob(SYSTEM_CALENDAR_BEGIN_DATETIME, xxlJobJobInterviewNotSubmittedReminderDay, tenantId);
            updateExistAttendee(noInterviewCalendarEvent, notInterviewJob, CalendarTypeEnum.NO_INTERVIEW);
            for(SystemCalendarResult record : notInterviewJob) {
                calendarEventService.createCalendarEvent(getSystemCalendarEventDto(record.getJobId(), CalendarTypeEnum.NO_INTERVIEW, getAttendees(record), getRelation(record), record.getTenantId()));
            }
        }

        log.info("System calendar job follow scheduled tasks end.");
    }

    public static Map<String, String> convertToFieldMap(String json) {
        Map<String, String> result = new HashMap<>();
        JSONArray array = JSONUtil.parseArray(json);

        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            result.put(
                    obj.getStr("field"),
                    obj.get("value").toString()
            );
        }

        return result;
    }

    private void updateExistAttendee(List<CalendarEvent> calendarEvent, List<SystemCalendarResult> newResult, CalendarTypeEnum calendarTypeEnum) {
        updateExistAttendee(calendarEvent, newResult, calendarTypeEnum, null);
    }

    private void updateExistAttendee(List<CalendarEvent> calendarEvent, List<SystemCalendarResult> newResult, CalendarTypeEnum calendarTypeEnum, InvoiceTypeEnum type) {
        Map<Long, List<CalendarEvent>> existCalendarEvent = calendarEvent.stream()
                .collect(Collectors.groupingBy(CalendarEvent::getReferenceId));
        Iterator<SystemCalendarResult> iterator = newResult.iterator();
        while (iterator.hasNext()) {
            SystemCalendarResult result = iterator.next();
            Long referenceId = getReferenceId(result, calendarTypeEnum, type);
            if(existCalendarEvent.containsKey(referenceId)) {
                List<CalendarEventAttendeeDTO> attendees = getAttendees(result);
                List<CalendarEvent> existEvent = existCalendarEvent.get(referenceId);
                calendarEventService.updateAttendees(existEvent, attendees);
                iterator.remove();
            }
        }
    }

    private Long getReferenceId(SystemCalendarResult result, CalendarTypeEnum calendarTypeEnum, InvoiceTypeEnum type) {
        switch (calendarTypeEnum) {
            case NOT_SUBMIT_TO_CLIENT:
            case SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS:
            case OFFER_PASS_NOT_UPDATE_STATUS:
                return result.getTalentRecruitmentProcessId();
            case PAYMENT_OVERDUE:
                return InvoiceTypeEnum.getTypeInvoiceId(result.getInvoiceId(), type);
            case NO_SUBMIT_TALENT:
            case NO_INTERVIEW:
                return result.getJobId();
            case FOLLOW_UP_RECORD_UPDATE:
                return getBusinessFollowUpdateStatusReferenceId(result.getCompanyId(), result.getCompanyContactId());
            case CONTACT_JOB_CHANGE:
                return result.getCompanyContactId();
            case EXPECTED_ORDER_EXPIRATION:
                return result.getBusinessId();
            case CONTRACT_NEARING_EXPIRATION:
            case CONTRACT_EXPIRED:
                return result.getContractId();
        }
        return -1L;
    }

    public static Long getBusinessFollowUpdateStatusReferenceId(Long companyId, Long contactId) {
        String merge = String.valueOf(companyId) + String.valueOf(contactId);
        return Long.valueOf(merge);
    }

    @Override
    @XxlJobHandler(values = {SYSTEM_CALENDAR_TALENT_APPLICATION_FOLLOW})
    public void systemCalendarTalentApplicationFollow() {
        log.info("System calendar talent application follow scheduled tasks start.");
        //候选人流程跟进-24小时未推荐至客户/拒绝
        List<CalendarEvent> notToClientEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT);
        List<SystemCalendarResult> notToClient = xxlJobRelationRepository.findStopApplication(SYSTEM_CALENDAR_BEGIN_DATETIME, 1L, NodeType.SUBMIT_TO_JOB.toDbValue());
        updateExistAttendee(notToClientEvent, notToClient, CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT);
        for(SystemCalendarResult record : notToClient) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(record.getTalentRecruitmentProcessId(), CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT, getAttendees(record), getRelation(record), record.getTenantId()));
        }
        //候选人流程跟进-推荐至客户后72小时未更新状态
        List<CalendarEvent> notUpdateStatusProcessEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS);
        List<SystemCalendarResult> notUpdateStatus = xxlJobRelationRepository.findStopApplication(SYSTEM_CALENDAR_BEGIN_DATETIME, 3L, NodeType.SUBMIT_TO_CLIENT.toDbValue());
        updateExistAttendee(notUpdateStatusProcessEvent, notUpdateStatus, CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS);
        for(SystemCalendarResult record : notUpdateStatus) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(record.getTalentRecruitmentProcessId(), CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS, getAttendees(record), getRelation(record), record.getTenantId()));
        }
        //候选人流程跟进-已超过入职日期，流程状态未更新

        List<CalendarEvent> notOnboardingProcessEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS);
        List<SystemCalendarResult> notOnboarding = xxlJobRelationRepository.findNotOnBoardingApplication(SYSTEM_CALENDAR_BEGIN_DATETIME, NodeType.OFFER_ACCEPT.toDbValue());
        updateExistAttendee(notOnboardingProcessEvent, notOnboarding, CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS);
        for(SystemCalendarResult record : notOnboarding) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(record.getTalentRecruitmentProcessId(), CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS, getAttendees(record), getRelation(record), record.getTenantId()));
        }
        //候选人流程跟进-回款逾期
        List<CalendarEvent> notPaidProcessEvent = calendarEventRepository.findUncompletedAndOverdueByCalendarType(CalendarTypeEnum.PAYMENT_OVERDUE);
        List<SystemCalendarResult> invoiceUnpaid = xxlJobRelationRepository.findInvoiceUnpaid(SYSTEM_CALENDAR_BEGIN_DATETIME);
        updateExistAttendee(notPaidProcessEvent, invoiceUnpaid, CalendarTypeEnum.PAYMENT_OVERDUE, InvoiceTypeEnum.INVOICE);
        for(SystemCalendarResult record : invoiceUnpaid) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(InvoiceTypeEnum.getTypeInvoiceId(record.getInvoiceId(), InvoiceTypeEnum.INVOICE), CalendarTypeEnum.PAYMENT_OVERDUE, getAttendees(record), getRelation(record), record.getTenantId()));
        }
        List<SystemCalendarResult> invoicingApplicationInfoUnpaid = xxlJobRelationRepository.findInvoicingApplicationInfoUnpaid(SYSTEM_CALENDAR_BEGIN_DATETIME);
        updateExistAttendee(notPaidProcessEvent, invoicingApplicationInfoUnpaid, CalendarTypeEnum.PAYMENT_OVERDUE, InvoiceTypeEnum.INVOICING_APPLICATION_INFO);
        for(SystemCalendarResult record : invoicingApplicationInfoUnpaid) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(InvoiceTypeEnum.getTypeInvoiceId(record.getInvoiceId(), InvoiceTypeEnum.INVOICING_APPLICATION_INFO), CalendarTypeEnum.PAYMENT_OVERDUE, getAttendees(record), getRelation(record), record.getTenantId()));
        }
        List<SystemCalendarResult> tGroupInvoiceUnpaid = xxlJobRelationRepository.findTGroupInvoiceUnpaid(SYSTEM_CALENDAR_BEGIN_DATETIME);
        updateExistAttendee(notPaidProcessEvent, tGroupInvoiceUnpaid, CalendarTypeEnum.PAYMENT_OVERDUE, InvoiceTypeEnum.T_GROUP_INVOICE);
        for(SystemCalendarResult record : tGroupInvoiceUnpaid) {
            calendarEventService.createCalendarEvent(getSystemCalendarEventDto(InvoiceTypeEnum.getTypeInvoiceId(record.getInvoiceId(), InvoiceTypeEnum.T_GROUP_INVOICE), CalendarTypeEnum.PAYMENT_OVERDUE, getAttendees(record), getRelation(record), record.getTenantId()));
        }

        log.info("System calendar talent application follow scheduled tasks end.");
    }

    private List<CalendarEventRelationDTO> getRelation(SystemCalendarResult record) {
        List<CalendarEventRelationDTO> relationList = new ArrayList<>();
        if(record.getJobId() != null) {
            CalendarEventRelationDTO job = new CalendarEventRelationDTO();
            job.setRelationId(record.getJobId());
            job.setRelationName(record.getJobTitle());
            job.setRelationType(CalendarRelationEnum.JOB);
            relationList.add(job);
        }
        if(record.getCompanyId() != null) {
            CalendarEventRelationDTO company = new CalendarEventRelationDTO();
            company.setRelationId(record.getCompanyId());
            company.setRelationName(record.getCompanyName());
            company.setRelationType(CalendarRelationEnum.COMPANY);
            company.setCompanyStatus(record.getActive());
            relationList.add(company);
        }
        if(record.getTalentId() != null) {
            CalendarEventRelationDTO talent = new CalendarEventRelationDTO();
            talent.setRelationId(record.getTalentId());
            talent.setRelationName(record.getTalentName());
            talent.setRelationType(CalendarRelationEnum.CANDIDATE);
            relationList.add(talent);
        }
        if(record.getCompanyContactId() != null) {
            CalendarEventRelationDTO companyContact = new CalendarEventRelationDTO();
            companyContact.setRelationId(record.getCompanyContactId());
            companyContact.setRelationName(record.getCompanyContactName());
            companyContact.setRelationType(CalendarRelationEnum.COMPANY_CONTACT);
            relationList.add(companyContact);
        }
        if(record.getTalentRecruitmentProcessId() != null) {
            CalendarEventRelationDTO talentProcessId = new CalendarEventRelationDTO();
            talentProcessId.setRelationId(record.getTalentRecruitmentProcessId());
            talentProcessId.setRelationType(CalendarRelationEnum.TALENT_RECRUITMENT_PROCESS);
            relationList.add(talentProcessId);
        }
        if(record.getInvoiceId() != null) {
            CalendarEventRelationDTO invoice = new CalendarEventRelationDTO();
            invoice.setRelationId(record.getInvoiceId());
            invoice.setRelationName(record.getInvoiceName());
            invoice.setRelationType(CalendarRelationEnum.INVOICE);
            relationList.add(invoice);
        }

        return relationList;
    }

    private List<CalendarEventAttendeeDTO> getAttendees(SystemCalendarResult record) {
        if(record.getUserIdList() == null || record.getUserIdList().isEmpty()) {
            return new ArrayList<>();
        }
        return record.getUserIdList().stream().map(userId -> {
            CalendarEventAttendeeDTO attendeeDTO = new CalendarEventAttendeeDTO();
            attendeeDTO.setUserId(userId);
            attendeeDTO.setIsOrganizer(CalendarEventAttendeeTypeEnum.PARTICIPANT);
            attendeeDTO.setIsReminder(CalendarEventAttendeeReminderTypeEnum.NO_REMINDER);
            return attendeeDTO;
        }).collect(Collectors.toList());
    }

    private CalendarEventDTO getSystemCalendarEventDto(Long referenceId, CalendarTypeEnum calendarTypeEnum, List<CalendarEventAttendeeDTO> attendees, List<CalendarEventRelationDTO> relationList, Long tenantId) {
        CalendarEventDTO eventDTO = new CalendarEventDTO();
        eventDTO.setTypeId(20);
        eventDTO.setStatus(CalendarStatusEnum.TO_BE_COMPLETED);
        eventDTO.setAttendees(attendees);
        eventDTO.setCalendarType(calendarTypeEnum);
        eventDTO.setRelationList(relationList);
        Instant now = Instant.now();
        eventDTO.setStartTime(now);
        eventDTO.setEndTime(getTodayAt2330(now, ZoneId.of("UTC")));
        eventDTO.setGoToId(0L);
        eventDTO.setReferenceId(referenceId);
        eventDTO.setTitle(calendarTypeEnum.getComment());
        eventDTO.setTenantId(tenantId);
        return eventDTO;
    }

    /**
     * 获取指定Instant对应的当天23:30:00的Instant
     * @param instant 参考时间点
     * @param zoneId 要使用的时区
     * @return 当天23:30:00的Instant
     */
    public static Instant getTodayAt2330(Instant instant, ZoneId zoneId) {
        // 将Instant转换为指定时区的LocalDate
        LocalDate today = instant.atZone(zoneId).toLocalDate();

        // 将LocalDate与时间23:30:00组合成LocalDateTime
        LocalDateTime todayAt2330 = today.atTime(23, 30, 0);

        // 将LocalDateTime转回Instant
        return todayAt2330.atZone(zoneId).toInstant();
    }

    private void processJobStoppedCounts(List<UserJobStopCount> notSubmitJobByTimezones, XxlJobRelationTypeEnum xxlJobRelationTypeEnum) {
        int batchSize = 50;
        int i = 0;
        Instant now = Instant.now();
        for (UserJobStopCount count : notSubmitJobByTimezones) {
            Message message = new Message();
            message.setTenantId(count.getTenantId());
            message.setType(MessageTypeEnum.PERSONAL_WARNING);
            message.setContent(getJobStopMessageContent(count.getCount(), xxlJobRelationTypeEnum));
            message.setCreatedBy("system");
            message.setCreatedDate(now);
            message.setLastModifiedBy("system");
            message.setLastModifiedDate(now);
            entityManager.persist(message);
            MessageUserRelation messageUserRelation = new MessageUserRelation();
            messageUserRelation.setUserId(count.getUserId());
            messageUserRelation.setMessageId(message.getId());
            entityManager.persist(messageUserRelation);

            if (++i % batchSize == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }


    private String getJobStopMessageContent(long count, XxlJobRelationTypeEnum xxlJobRelationTypeEnum) {
        // 创建主 JSONObject
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("notifyType", xxlJobRelationTypeEnum.name());
        jsonObject.put("type", MessageTypeEnum.PERSONAL_WARNING.name());
        jsonObject.put("version", 1);

        // 创建 param 子对象
        JSONObject paramObject = new JSONObject();
        paramObject.put("stoppedJobCount", count);

        // 将 param 对象添加到主 JSONObject
        jsonObject.put("param", paramObject);

        // 输出结果
        return jsonObject.toString();
    }

    private List<? extends SseUserRefresh> getRefreshSseUser(List<UserJobStopCount> notSubmitJobByTimezones, List<UserJobStopCount> notInterviewJobsByTimezones) {
        List<SseUserRefresh> ret = new ArrayList<>();
        notSubmitJobByTimezones.forEach(c -> {
            SseUserRefresh sseUser = new SseUserRefresh();
            sseUser.setUserId(c.getUserId());
            sseUser.setTenantId(c.getTenantId());
            if(!ret.contains(sseUser)) {
                ret.add(sseUser);
            }
        });
        notInterviewJobsByTimezones.forEach(c -> {
            SseUserRefresh sseUser = new SseUserRefresh();
            sseUser.setUserId(c.getUserId());
            sseUser.setTenantId(c.getTenantId());
            if(!ret.contains(sseUser)) {
                ret.add(sseUser);
            }
        });
        return ret;
    }

    private Set<String> getCurrentZeroHourTimezones() {
        // 获取当前UTC时间
        ZonedDateTime utcNow = ZonedDateTime.now(ZoneOffset.UTC);
        int targetHour = utcNow.getHour();

        // 获取所有时区
        Set<String> allZones = ZoneId.getAvailableZoneIds();
        return allZones.stream()
                .filter(zoneId -> {
                    ZoneId zone = ZoneId.of(zoneId);
                    ZoneOffset offset = zone.getRules().getOffset(utcNow.toInstant());

                    // 将偏移转换为小时
                    int offsetHours = offset.getTotalSeconds() / 3600;

                    // 如果偏移的小时数等于目标小时，就包含这个时区
                    return offsetHours == targetHour;
                })
                .sorted()
                .collect(Collectors.toSet());
    }

    public void processAndSaveStoppedProcessCounts(List<UserStoppedProcessCount> counts) {
        int batchSize = 50;
        int i = 0;
        Instant now = Instant.now();
        for (UserStoppedProcessCount count : counts) {
            Message message = new Message();
            message.setTenantId(count.getTenantId());
            message.setType(MessageTypeEnum.PERSONAL_WARNING);
            message.setContent(getProcessStopMessageContent(count.getStoppedProcessCount()));
            message.setCreatedBy("system");
            message.setCreatedDate(now);
            message.setLastModifiedBy("system");
            message.setLastModifiedDate(now);
            entityManager.persist(message);
            MessageUserRelation messageUserRelation = new MessageUserRelation();
            messageUserRelation.setUserId(count.getUserId());
            messageUserRelation.setMessageId(message.getId());
            entityManager.persist(messageUserRelation);

            if (++i % batchSize == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }

    private String getProcessStopMessageContent(long count) {
        // 创建主 JSONObject
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("notifyType", APPLICATION_NO_UPDATE_REMINDER.name());
        jsonObject.put("type", MessageTypeEnum.PERSONAL_WARNING.name());
        jsonObject.put("version", 1);

        // 创建 param 子对象
        JSONObject paramObject = new JSONObject();
        paramObject.put("stoppedProcessCount", count);

        // 将 param 对象添加到主 JSONObject
        jsonObject.put("param", paramObject);

        // 输出结果
        return jsonObject.toString();
    }

    public LocalDate[] calculateDatesWithPrevious(DataPeriod period, LocalDate nowLocalDateForTimezone) {
        LocalDate[] dates = new LocalDate[4]; // Array to hold startDate, endDate, previousStartDate, previousEndDate
        switch (period) {
            case TODAY:
                dates[0] = dates[1] = nowLocalDateForTimezone;
                dates[2] = dates[3] = nowLocalDateForTimezone.minusDays(1); // Yesterday
                break;
            case YESTERDAY:
                dates[0] = dates[1] = nowLocalDateForTimezone.minusDays(1);
                dates[2] = dates[3] = nowLocalDateForTimezone.minusDays(2); // The day before yesterday
                break;
            case THIS_WEEK:
                dates[0] = nowLocalDateForTimezone.with(DayOfWeek.MONDAY);
                dates[1] = nowLocalDateForTimezone.with(DayOfWeek.SUNDAY);
                LocalDate lastMonday = nowLocalDateForTimezone.minusWeeks(1).with(DayOfWeek.MONDAY);
                dates[2] = lastMonday;
                dates[3] = lastMonday.plusDays(6); // End of last week
                break;
            case LAST_WEEK:
                LocalDate prevMonday = nowLocalDateForTimezone.minusWeeks(1).with(DayOfWeek.MONDAY);
                dates[0] = prevMonday;
                dates[1] = prevMonday.plusDays(6);
                LocalDate prevPrevMonday = prevMonday.minusWeeks(1);
                dates[2] = prevPrevMonday;
                dates[3] = prevPrevMonday.plusDays(6); // End of the week before last
                break;
            case THIS_MONTH:
                dates[0] = nowLocalDateForTimezone.withDayOfMonth(1);
                dates[1] = nowLocalDateForTimezone.with(TemporalAdjusters.lastDayOfMonth());
                LocalDate firstDayOfLastMonth = nowLocalDateForTimezone.minusMonths(1).withDayOfMonth(1);
                dates[2] = firstDayOfLastMonth;
                dates[3] = firstDayOfLastMonth.with(TemporalAdjusters.lastDayOfMonth()); // End of last month
                break;
            case LAST_MONTH:
                LocalDate firstDayOfThisMonth = nowLocalDateForTimezone.withDayOfMonth(1);
                LocalDate firstDayOfTwoMonthsAgo = firstDayOfThisMonth.minusMonths(1);
                dates[0] = firstDayOfTwoMonthsAgo;
                dates[1] = firstDayOfTwoMonthsAgo.with(TemporalAdjusters.lastDayOfMonth());
                LocalDate firstDayOfThreeMonthsAgo = firstDayOfTwoMonthsAgo.minusMonths(1);
                dates[2] = firstDayOfThreeMonthsAgo;
                dates[3] = firstDayOfThreeMonthsAgo.with(TemporalAdjusters.lastDayOfMonth()); // End of two months ago
                break;
        }
        return dates;
    }

    private RecruitingKpiGroupByFieldType getGroupByFieldTypeByDataPeriod(DataPeriod dataPeriod) {
        return switch (dataPeriod) {
            case TODAY,YESTERDAY -> RecruitingKpiGroupByFieldType.DAY;
            case THIS_WEEK, LAST_WEEK -> RecruitingKpiGroupByFieldType.WEEK;
            case THIS_MONTH, LAST_MONTH -> RecruitingKpiGroupByFieldType.MONTH;
        };
    }

}
