package com.altomni.apn.canal.disruptor.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.constants.LogEventType;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.entity.RecordDataInfo;
import com.altomni.apn.canal.entity.TalentProcessRecord;
import com.altomni.apn.canal.entity.TalentRecord;
import com.altomni.apn.canal.repository.TalentRecruitmentProcessRepository;
import com.altomni.apn.canal.repository.TalentRepository;
import com.altomni.apn.canal.repository.TalentResumeRelationRepository;
import com.altomni.apn.canal.repository.dto.TalentAdditionalToTalentId;
import com.altomni.apn.canal.repository.dto.TalentRecruitmentProcessToJobName;
import com.altomni.apn.canal.service.changerecord.TalentRecordContextHolder;
import com.altomni.apn.canal.service.changerecord.talent.TalentChangeRecordService;
import com.altomni.apn.canal.service.changerecord.talent.impl.TalentChangeRecordServiceImpl;
import com.altomni.apn.canal.service.elastic.EsRecordService;
import com.altomni.apn.common.aop.user.SimpleUserAspect;
import com.altomni.apn.common.domain.application.TalentRecruitmentProcessForCanal;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.talent.TalentResumeRelation;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;
import static com.altomni.apn.canal.service.changerecord.talent.impl.TalentChangeRecordServiceImpl.TALENT_RECRUITMENT_PROCESS_ID;
import static com.altomni.apn.canal.service.changerecord.talent.impl.TalentChangeRecordServiceImpl.*;

@Slf4j
@Component
public class TalentRecordHandler implements EventHandler<CanalEvent> {

    private final TalentRepository talentRepository;
    private final EsRecordService esRecordService;
    private final TalentChangeRecordService talentChangeRecordService;
    private final TalentResumeRelationRepository talentResumeRelationRepository;
    private final TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;

    public TalentRecordHandler(TalentRepository talentRepository,
                               EsRecordService esRecordService,
                               TalentChangeRecordService talentChangeRecordService,
                               TalentResumeRelationRepository talentResumeRelationRepository,
                               TalentRecruitmentProcessRepository talentRecruitmentProcessRepository) {
        this.talentRepository = talentRepository;
        this.esRecordService = esRecordService;
        this.talentRecruitmentProcessRepository = talentRecruitmentProcessRepository;
        this.talentChangeRecordService = talentChangeRecordService;
        this.talentResumeRelationRepository = talentResumeRelationRepository;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> TalentRecordSyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Map<Long, TalentRecord> talentRecordMap = new HashMap<>();
        try {
            List<EventContent> talentChangeEvent = eventContents.stream()
                    .filter(eventContent -> TALENT.equals(eventContent.tableName()) || TALENT_RECORD_TABLES.contains(eventContent.tableName())).toList();
            if (talentChangeEvent.isEmpty()) {
                return;
            }

            Map<Long, Long> recruitmentProcessTalentIdMap = getRecruitmentProcessTalentIdMap(talentChangeEvent);
            Map<Long, Long> talentAdditional2TalentIdMap = getTalentAdditional2TalentIdMap(talentChangeEvent);
            Map<Long, String> submitToJobResumeFileNameMap = getSubmitToJobResumeFileNameMap(talentChangeEvent);
            Map<Long, String> talentRecruitmentProcessJobNameMap = getTalentRecruitmentProcessJobNameMap(talentChangeEvent);

            TalentRecordContextHolder talentRecordContextHolder = new TalentRecordContextHolder(
                    recruitmentProcessTalentIdMap, talentAdditional2TalentIdMap, submitToJobResumeFileNameMap, talentRecruitmentProcessJobNameMap);

            talentChangeEvent.forEach(eventContent -> {
                String tableName = eventContent.tableName();
                CanalEntry.RowChange rowChange = eventContent.rowChange();
                CanalEntry.EventType eventType = rowChange.getEventType();
                log.info("[APN: EsFillerTalentRecordService] record talent, tableName: {}", tableName);

                List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                parseTalentRecordMap(tableName, eventType, rowDataList, talentRecordContextHolder, talentRecordMap);
            });
            if (talentRecordMap.isEmpty()) {
                return;
            }

            syncTalentRecord(talentRecordMap);
        } catch (Exception ex) {
            log.error("APN RecordTalent: failed to process talent record", ex);
            for (Long next : talentRecordMap.keySet()) {
                TalentRecord talentRecord = talentRecordMap.get(next);
                if (talentRecord != null) {
                    log.error("APN RecordTalent: failed process talent Id: {} Tenant Id:{}", talentRecord.getTalentId(), talentRecord.getTenantId(), ex);
                }
            }
        }
    }

    private void parseTalentRecordMap(String tableName, CanalEntry.EventType eventType, List<CanalEntry.RowData> rowDataList, TalentRecordContextHolder talentRecordContextHolder, Map<Long, TalentRecord> talentRecordMap) {
        for (CanalEntry.RowData rowData : rowDataList) {
            List<TalentProcessRecord> talentProcessRecords = new ArrayList<>();
            String createdBy = "";
            String createdTime = "";
            Long talentId = talentChangeRecordService.recordTalentId(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, talentRecordContextHolder);
            //记录tenantId信息 比如timesheet在lastModifiedBy时无法获取
            RecordDataInfo recordDataInfo = new RecordDataInfo();
            Boolean initalTalent = false;
            List<JSONObject> esDocuments = new ArrayList<>();
            if (eventType == CanalEntry.EventType.INSERT) {
                createdTime = talentChangeRecordService.recordCreatedTime(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdTime);
                createdBy = talentChangeRecordService.recordCreatedBy(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdBy, recordDataInfo);
                initalTalent = talentChangeRecordService.recordInsertTalentRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, talentId, createdTime, createdBy, esDocuments, talentProcessRecords, talentRecordContextHolder);
                Long tenantId = getTenantIdFromCreatedBy(createdBy);
                if(tenantId != null && recordDataInfo.getTenantId() == null) {
                    recordDataInfo.setTenantId(tenantId);
                }

            } else if (eventType == CanalEntry.EventType.UPDATE) {
                createdTime = talentChangeRecordService.recordCreatedTime(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdTime);
                createdBy = talentChangeRecordService.recordCreatedBy(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdBy, recordDataInfo);
                talentChangeRecordService.recordUpdateTalentRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, true, talentId, createdTime, createdBy, esDocuments, talentProcessRecords, talentRecordContextHolder);
                Long tenantId = getTenantIdFromCreatedBy(createdBy);
                if(tenantId != null && recordDataInfo.getTenantId() == null) {
                    recordDataInfo.setTenantId(tenantId);
                }
            } else if (eventType == CanalEntry.EventType.DELETE) {
                talentChangeRecordService.recordDeleteTalentRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, esDocuments);
            }
            log.info("[APN: EsFillerTalentRecordService] talent record input, talentId: {}, tenantId: {}, createdTime: {}, createdBy: {}, esDocuments: {}", talentId, recordDataInfo.getTenantId(), createdTime, createdBy, esDocuments);
            if (talentId != null) {
                TalentRecord talentRecord = talentRecordMap.get(talentId);
                if (talentRecord == null) {
                    talentRecord = new TalentRecord();
                    talentRecord.setTalentId(talentId);
                }
                talentRecord.setInitial(initalTalent);
                if (StringUtil.isNotEmpty(createdTime)) {
                    talentRecord.setCreateTime(createdTime);
                }
                if (recordDataInfo.getTenantId() != null) {
                    talentRecord.setTenantId(recordDataInfo.getTenantId());
                }
                if (StringUtil.isNotEmpty(createdBy)) {
                    talentRecord.setCreatedBy(createdBy);
                }
                talentRecord.addEsDocuments(esDocuments);
                talentRecord.addTalentProcessRecordList(talentProcessRecords);
                talentRecordMap.put(talentId, talentRecord);
            }
        }
    }

    private Long getTenantIdFromCreatedBy(String createdBy) {
        //看是1965650_20014_0格式还是1154,20014格式
        if (StringUtils.isNotEmpty(createdBy)) {
            if (SimpleUserAspect.isTimesheetUser(createdBy)) {
                String[] idStrings = createdBy.split("_");
                if (idStrings.length == 3) {
                    return Long.parseLong(idStrings[1]);
                }
            } else {
                String[] idStrings = createdBy.split(",");
                if (idStrings.length == 2) {
                    return Long.parseLong(idStrings[1]);
                }
            }
        }

        return null;
    }

    private void syncTalentRecord(Map<Long, TalentRecord> talentRecordMap) {
        log.info("[APN: EsFillerTalentRecordService] syncTalentRecord input, talentRecordMap: {}", JSON.toJSON(talentRecordMap));
        Set<Long> lastProcessRecords = talentRecordMap.values().stream().map(talentRecord -> lastProcessRecord(talentRecord.getTalentProcessRecords()))
                .filter(Optional::isPresent).map(Optional::get).map(TalentProcessRecord::getTalentProcessId).collect(Collectors.toSet());
        Map<Long, String> processJobNameMap = lastProcessRecords.isEmpty() ?
                Collections.emptyMap() :
                talentRecruitmentProcessRepository.findAllByRecruitmentProcessIdInWithJobName(lastProcessRecords)
                        .stream().collect(Collectors.toMap(TalentRecruitmentProcessToJobName::talentRecruitmentProcessId, TalentRecruitmentProcessToJobName::jobName, (a1, a2) -> a1));
        Map<Long, Long> interviewMaxProgressMap = lastProcessRecords.isEmpty() ?
                Collections.emptyMap() :
                talentRecruitmentProcessRepository.getMaxInterviewProgressesAsMap(lastProcessRecords);

        Map<Long, List<JSONObject>> syncTalentRecordsMap = new HashMap<>();
        AtomicReference<Long> tenantId = new AtomicReference<>();
        talentRecordMap.forEach((talentId, talentRecord) -> {

            log.debug("syncTalentRecord, talentId: {}, processJobNameMap: {}, interviewMaxProgressMap: {}", talentId, JSON.toJSONString(processJobNameMap), JSON.toJSONString(interviewMaxProgressMap));
            addProcessChange(talentRecord.getEsDocuments(), talentRecord.getTalentProcessRecords(), processJobNameMap, interviewMaxProgressMap);

            if (talentRecord.getEsDocuments().isEmpty()) {
                return;
            }
            JSONObject records = new JSONObject();
            records.putIfAbsent("talentId", talentId);
            resolveDodax(talentRecord);
            List<JSONObject> eliminatedEsDocuments = eliminateDuplicateChanges(talentRecord.getEsDocuments());
            eliminatedEsDocuments = filterChanges(eliminatedEsDocuments);
            log.info("[APN: EsFillerTalentRecordService] Before saveTalentUpdateToEs, talent id: {}, tenantId: {}, esDocuments: {}", talentId, talentRecord.getTenantId(), JSON.toJSONString(eliminatedEsDocuments));
            records.putIfAbsent("changeFields", talentRecord.isInitial() ? talentChangeRecordService.generateNewTalent(talentId) : eliminatedEsDocuments);
//                records.putIfAbsent("createdDate", talentRecord.getCreatedTime());
//                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
                    .withZone(ZoneId.systemDefault());
            Instant now = Instant.now();
            String formatted = formatter.format(now);
            //TODO canal时间获取后就给加了时区时间，暂未解决，先用日志创建时候的时间
            Instant timestamp = TalentChangeRecordServiceImpl.formatTime(formatted);
            records.putIfAbsent("createdDate", timestamp);
            records.putIfAbsent("@timestamp", timestamp);
            records.putIfAbsent("createdBy", getUserIdFromCreatedBy(talentRecord.getCreatedBy()));
            if (talentRecord.getTenantId() != null) {
                tenantId.set(talentRecord.getTenantId());
            }
            Long currentTenantId = talentRecord.getTenantId() == null ? tenantId.get() : talentRecord.getTenantId();
            syncTalentRecordsMap.computeIfAbsent(currentTenantId, k -> new ArrayList<>());
            syncTalentRecordsMap.get(currentTenantId).add(records);
        });
        if (syncTalentRecordsMap.isEmpty()) {
            return;
        }
        esRecordService.batchSaveTalentUpdateToEs(syncTalentRecordsMap);
    }

    private void resolveDodax(TalentRecord talentRecord) {
        List<JSONObject> esDocuments = talentRecord.getEsDocuments();
        if(esDocuments == null || esDocuments.isEmpty()) {
            return;
        }

        // 用于存储DODAX相关的状态
        Map<String, Set<String>> keyEventTypes = new HashMap<>();

        // 第一次遍历：收集DODAX相关的key和它们对应的eventType
        for (JSONObject doc : esDocuments) {
            String key = doc.getStr("key");
            String eventType = doc.getStr("eventType") == null ? "" : doc.getStr("eventType");
            if (DODAX_CANDIDATE_ID.equalsIgnoreCase(key) || DODAX_OWNERSHIP_PERIOD.equalsIgnoreCase(key)) {
                keyEventTypes.computeIfAbsent(key, k -> new HashSet<>()).add(eventType);
            }
        }

        // 如果没有DODAX相关的文档，直接返回
        if (keyEventTypes.isEmpty()) {
            return;
        }

        // 检查是否所有操作都是insert
        boolean allInserts = keyEventTypes.values().stream()
                .allMatch(types -> types.size() == 1 && types.contains("insert"));

        // 如果全是insert操作，直接返回，不做任何处理
        if (allInserts) {
            return;
        }

        // 存储需要删除的update文档
        Set<JSONObject> documentsToRemove = new HashSet<>();

        // 第一次遍历：找出所有需要删除的文档
        for (JSONObject doc : esDocuments) {
            String key = doc.getStr("key");
            String eventType = doc.getStr("eventType");

            if ((DODAX_CANDIDATE_ID.equalsIgnoreCase(key) || DODAX_OWNERSHIP_PERIOD.equalsIgnoreCase(key))
                    && "insert".equals(eventType)) {

                String insertChangedTo = doc.getStr("changedTo");

                // 找到对应的update记录
                Optional<JSONObject> matchingUpdateOpt = esDocuments.stream()
                        .filter(other -> key.equalsIgnoreCase(other.getStr("key"))
                                && "update".equals(other.getStr("eventType"))
                                && insertChangedTo.equals(other.getStr("changedFrom")))
                        .findFirst();

                if (matchingUpdateOpt.isPresent()) {
                    // 记录要删除的insert和update文档
                    documentsToRemove.add(doc);
                    documentsToRemove.add(matchingUpdateOpt.get());
                }
            }
        }

        // 使用单个迭代器删除所有标记的文档
        if (!documentsToRemove.isEmpty()) {
            Iterator<JSONObject> iterator = esDocuments.iterator();
            while (iterator.hasNext()) {
                JSONObject doc = iterator.next();
                if (documentsToRemove.contains(doc)) {
                    iterator.remove();
                    log.info("[DEBUG] Removed document: {}", JSON.toJSON(doc));
                }
            }
        }

        log.info("[DEBUG] Remaining documents: {}", JSON.toJSON(esDocuments));
    }

    private Optional<TalentProcessRecord> lastProcessRecord(List<TalentProcessRecord> talentProcessRecords) {
        if (talentProcessRecords.isEmpty()) {
            return Optional.empty();
        }
        List<TalentProcessRecord> filterInActive = talentProcessRecords.stream()
                .filter(p -> !NodeStatus.INACTIVE.equals(p.getNextStatus()))
                .sorted(Comparator.comparing(t -> t.getCurrentNodeType().toDbValue()))
                .toList();
        if (filterInActive.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(filterInActive.get(filterInActive.size() - 1));
    }

    private void addProcessChange(List<JSONObject> eliminatedEsDocuments, List<TalentProcessRecord> talentProcessRecords, Map<Long, String> processJobNameMap, Map<Long, Long> interviewMaxProgressMap) {
        Optional<TalentProcessRecord> talentProcessRecordOpt = lastProcessRecord(talentProcessRecords);
        if (talentProcessRecordOpt.isEmpty()) {
            return;
        }
        TalentProcessRecord last = talentProcessRecordOpt.get();
        JSONObject esDocumentJson = new JSONObject();
        String jobName = processJobNameMap.get(last.getTalentProcessId());
        esDocumentJson.put("key", jobName);
        esDocumentJson.put("fieldId", last.getTalentProcessId());
        esDocumentJson.put("eventType", LogEventType.EVENT_STATUS_UPDATE);
        esDocumentJson.put("changedFrom", "");
        if (NodeType.INTERVIEW.equals(last.getCurrentNodeType())) {
            if (NodeStatus.ELIMINATED == last.getNextStatus()) {
                esDocumentJson.put("changedTo", NodeStatus.ELIMINATED.name());
            } else {
                Long interviewMaxProgress = interviewMaxProgressMap.get(last.getTalentProcessId());
                esDocumentJson.put("changedTo", NodeType.INTERVIEW.name() + "-" + interviewMaxProgress);
            }
        } else {
            esDocumentJson.put("changedTo", NodeStatus.ELIMINATED == last.getNextStatus() ? NodeStatus.ELIMINATED.name() : last.getCurrentNodeType().name());
        }
        eliminatedEsDocuments.add(esDocumentJson);
    }

    private List<JSONObject> filterChanges(List<JSONObject> eliminatedEsDocuments) {
        JSONObject talentUpdate = null;
        eliminatedEsDocuments = eliminatedEsDocuments.stream().filter(t -> !t.isEmpty()).collect(Collectors.toList());
        for (JSONObject jsonObject : eliminatedEsDocuments) {
            if (TalentChangeRecordServiceImpl.convertSnakeCaseToCamelCase(TalentChangeRecordServiceImpl.MOTIVATION_ID).equals(jsonObject.getStr("key"))) {
                talentUpdate = jsonObject;
            }
        }
        JSONObject talentMotivationInsert = null;
        for (JSONObject jsonObject : eliminatedEsDocuments) {
            if (TalentChangeRecordServiceImpl.convertSnakeCaseToCamelCase(TalentChangeRecordServiceImpl.NOTE_MOTIVATION_ID).equals(jsonObject.getStr("key")) && LogEventType.EVENT_TYPE_INSERT.equals(jsonObject.getStr("eventType"))) {
                talentMotivationInsert = jsonObject;
            }
        }
        if (talentMotivationInsert == null) {
            return eliminatedEsDocuments;
        }
        if (talentUpdate == null) {
            eliminatedEsDocuments.remove(talentMotivationInsert);
        } else {
            eliminatedEsDocuments.remove(talentUpdate);
        }
        return eliminatedEsDocuments;
    }

    private List<JSONObject> eliminateDuplicateChanges(List<JSONObject> esDocuments) {
        //删除esDocuments列表中具有特定键的重复项，同时保留那些键在excludeKeys列表中的对象
        Collection<String> union = CollUtil.union(NodeType.getAllNames(), ContactType.getAllNames());
        union.add(DODAX_CANDIDATE_ID);
        union.add(DODAX_OWNERSHIP_PERIOD);
        List<String> excludeKeys = new ArrayList<>(union);

        List<JSONObject> ret = esDocuments.stream()
                .filter(obj -> excludeKeys.contains(obj.getStr("key")))
                .collect(Collectors.toList());
        List<JSONObject> eliminated = esDocuments.stream()
                .filter(obj -> !excludeKeys.contains(obj.getStr("key")))  // Exclude the objects with certain keys.
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                obj -> obj.getStr("key"),  // The key extractor.
                                obj -> obj,  // The value mapper.
                                (obj1, obj2) -> obj1  // The merge function (in case of key collision, keep the first one).
                        ),
                        map -> new ArrayList<>(map.values())  // Convert the map back to a list.
                ));
        ret.addAll(eliminated);
        return ret;
    }

    private String getUserIdFromCreatedBy(String createdBy) {
        if (StringUtils.isNotEmpty(createdBy)) {
            String[] idStrings = createdBy.split(",");
            if (idStrings.length == 2) {
                return idStrings[0];
            } else {
                return createdBy;
            }
        }
        return "unknown";
    }

    /**
     * 批量查询流程节点id和候选人id的映射关系
     */
    private Map<Long, Long> getRecruitmentProcessTalentIdMap(List<EventContent> eventContents) {
        Set<String> talentRecruitmentProcessTables = Set.of(TALENT_RECRUITMENT_PROCESS_INTERVIEW, TALENT_RECRUITMENT_PROCESS_NODE, TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB);
        Set<Long> talentRecruitmentProcessIds = eventContents
                .stream()
                .filter(eventContent -> talentRecruitmentProcessTables.contains(eventContent.tableName()))
                .flatMap(eventContent -> eventContent.rowChange().getRowDatasList().stream())
                .flatMap(rowData -> rowData.getAfterColumnsList().stream())
                .filter(column -> TALENT_RECRUITMENT_PROCESS_ID.equals(column.getName()) && !column.getValue().isEmpty())
                .map(CanalEntry.Column::getValue).map(Long::parseLong)
                .collect(Collectors.toSet());
        if (talentRecruitmentProcessIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return talentRecruitmentProcessRepository.findAllByIdIn(talentRecruitmentProcessIds)
                .stream()
                .collect(Collectors.toMap(TalentRecruitmentProcessForCanal::getId, TalentRecruitmentProcessForCanal::getTalentId));
    }

    /**
     * 批量查询候选人附加信息id和候选人id的映射关系
     */
    private Map<Long, Long> getTalentAdditional2TalentIdMap(List<EventContent> eventContents) {
        Set<Long> talentAdditionalIds = eventContents
                .stream()
                .filter(eventContent -> TALENT_ADDITIONAL_INFO.equals(eventContent.tableName()))
                .flatMap(eventContent -> eventContent.rowChange().getRowDatasList().stream())
                .flatMap(rowData -> rowData.getAfterColumnsList().stream())
                .filter(column -> ID.equals(column.getName()) && !column.getValue().isEmpty())
                .map(CanalEntry.Column::getValue).map(Long::parseLong)
                .collect(Collectors.toSet());
        if (talentAdditionalIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return talentRepository.findTalentIdsByAdditionalIds(talentAdditionalIds)
                .stream()
                .collect(Collectors.toMap(TalentAdditionalToTalentId::additionalId, TalentAdditionalToTalentId::talentId));
    }

    /**
     * 获取提交到 job时，候选人简历文件名的映射关系
     */
    private Map<Long, String> getSubmitToJobResumeFileNameMap(List<EventContent> eventContents) {
        Set<Long> talentResumeRelationIds = eventContents.stream()
                .filter(eventContent -> TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB.equals(eventContent.tableName()))
                .filter(eventContent -> CanalEntry.EventType.UPDATE.equals(eventContent.rowChange().getEventType()))
                .flatMap(eventContent -> eventContent.rowChange().getRowDatasList().stream())
                .flatMap(rowData -> Stream.concat(rowData.getAfterColumnsList().stream(), rowData.getBeforeColumnsList().stream()))
                .filter(column -> TALENT_RESUME_RELATION_ID.equals(column.getName()) && column.getUpdated())
                .map(CanalEntry.Column::getValue).map(Long::parseLong)
                .collect(Collectors.toSet());
        if (talentResumeRelationIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return talentResumeRelationRepository.findAllById(talentResumeRelationIds)
                .stream().collect(Collectors.toMap(TalentResumeRelation::getId, TalentResumeRelation::getFileName));
    }

    /**
     * 面试流程改变时，批量获取jobName
     */
    private Map<Long, String> getTalentRecruitmentProcessJobNameMap(List<EventContent> eventContents) {
        Set<Long> processIds = eventContents.stream()
                .filter(eventContent -> TALENT_RECRUITMENT_PROCESS_INTERVIEW.equals(eventContent.tableName()))
                .flatMap(eventContent -> eventContent.rowChange().getRowDatasList().stream())
                .flatMap(rowData -> rowData.getAfterColumnsList().stream())
                .filter(column -> TALENT_RECRUITMENT_PROCESS_ID.equals(column.getName()) && !column.getValue().isEmpty())
                .map(CanalEntry.Column::getValue).map(Long::parseLong)
                .collect(Collectors.toSet());
        if (processIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return talentRecruitmentProcessRepository.findAllByRecruitmentProcessIdInWithJobName(processIds).stream()
                .collect(Collectors.toMap(TalentRecruitmentProcessToJobName::talentRecruitmentProcessId, TalentRecruitmentProcessToJobName::jobName, (a1, a2) -> a1));
    }




}
