package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class CompanyClientNoteSyncHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncCompanyService syncCompanyService;

    public CompanyClientNoteSyncHandler(CanalService canalService, SyncCompanyService syncCompanyService) {
        this.canalService = canalService;
        this.syncCompanyService = syncCompanyService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> CompanyClientNoteSyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> companyClientNoteIds = eventContents.stream()
                .filter(eventContent -> COMPANY_CLIENT_NOTE.equals(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());
        if (companyClientNoteIds.isEmpty()) {
            return;
        }
        syncCompanyClientNotesToEs(companyClientNoteIds);
    }


    private void syncCompanyClientNotesToEs(Set<Long> pendingCompanyClientNotesToSync) {
        log.info("startSyncCompanyClientNoteToEs {}", pendingCompanyClientNotesToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingCompanyClientNotesToSync, 50)) {
                syncCompanyService.synchronizeCompanyClientNotes(partition, RabbitMqConstant.MESSAGE_PRIORITY_THREE, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] sync_company_client_note_to_es error: {}", ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingCompanyClientNotesToSync, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_THREE);
        }
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (COMPANY_CLIENT_NOTE.equals(tableName)) {
            return companyClientNoteChange(columns, tableName, update);
        }
        return Optional.empty();
    }


    private Optional<Long> companyClientNoteChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String noteId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("companyClientNoteChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
            // for fill back
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                return Optional.empty();
            }
            if (name.equals(ID)) {
                noteId = column.getValue();
            }
        }
        log.info("[canal] received changed companyClientNoteId: {} from table: {}", noteId, tableName);
        return Optional.ofNullable(noteId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }
}
