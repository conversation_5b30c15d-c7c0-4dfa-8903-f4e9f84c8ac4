package com.altomni.apn.voip.service.alicloud.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nls.client.AccessToken;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.service.alicloud.AliCloudASRService;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.io.IOException;

@Deprecated
@Service
public class AliCloudASRServiceImpl implements AliCloudASRService {

    private Logger log = LoggerFactory.getLogger(AliCloudASRServiceImpl.class);

    private final ApplicationProperties properties;

    private final OkHttpClient okHttpClient;

    private static final float SAMPLE_RATE = 16000f;

    private static final String WAV_TYPE = "wav";

    private IAcsClient iAcsClient;


    public AliCloudASRServiceImpl(ApplicationProperties properties) {
        this.properties = properties;
        this.okHttpClient = new OkHttpClient().newBuilder().build();

    }

    @Override
    public String aliCloudFastVoicemailTranscription(byte[] audioData) throws IOException {
        AccessToken accessToken = new AccessToken(properties.getAliCloudAccessKey(), properties.getAliCloudSecretKey());
        accessToken.apply();
        String url = properties.getAliCloudFileTranscriptionEndPoint() +
                "?appkey=" + properties.getAliCloudAppKey() +
                "&token=" + accessToken.getToken() +
                "&format=" + WAV_TYPE +
                "&sample_rate=" + SAMPLE_RATE +
                "&first_channel_only=" + true;
        RequestBody requestBody = RequestBody.create(audioData, MediaType.parse("application/octet-stream"));
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = this.okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) log.error("alicloud retrieve audio file transcription error: {}", response);
            if(response.body() == null) log.error("lark service retrieve token error. The Get Token return null");
            JSONObject jsonResponse = new JSONObject(response.body().string());
            JSONArray sentences = jsonResponse.getJSONArray("flash_result");
            StringBuilder combinedText = new StringBuilder();
            for (int i = 0; i < sentences.size(); i++) {
                JSONObject sentence = sentences.getJSONObject(i);
                combinedText.append(sentence.getStr("text"));
            }
            return combinedText.toString();
        }
        catch (IOException e) {
            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
        return "";
    }


    //only for demo

    // 地域ID，常量，固定值。
    public static final String REGIONID = "cn-shanghai";
    public static final String ENDPOINTNAME = "cn-shanghai";
    public static final String PRODUCT = "nls-filetrans";
    public static final String DOMAIN = "filetrans.cn-shanghai.aliyuncs.com";
    public static final String API_VERSION = "2018-08-17";  // 中国站版本
    public static final String POST_REQUEST_ACTION = "SubmitTask";
    public static final String GET_REQUEST_ACTION = "GetTaskResult";
    // 请求参数
    public static final String KEY_APP_KEY = "appkey";
    public static final String KEY_FILE_LINK = "file_link";
    public static final String KEY_VERSION = "version";
    public static final String KEY_ENABLE_WORDS = "enable_words";
    // 响应参数
    public static final String KEY_TASK = "Task";
    public static final String KEY_TASK_ID = "TaskId";
    public static final String KEY_STATUS_TEXT = "StatusText";
    public static final String KEY_RESULT = "Result";
    // 状态值
    public static final String STATUS_SUCCESS = "SUCCESS";
    private static final String STATUS_RUNNING = "RUNNING";
    private static final String STATUS_QUEUEING = "QUEUEING";


    @Override
    public String aliCloudVoicemailTranscription(String fileLink) throws IOException, ClientException {
        DefaultProfile.addEndpoint(ENDPOINTNAME, REGIONID, PRODUCT, DOMAIN);
        DefaultProfile profile = DefaultProfile.getProfile(REGIONID, properties.getAliCloudAccessKey(), properties.getAliCloudSecretKey());
        this.iAcsClient = new DefaultAcsClient(profile);

        //create transcribing task
        CommonRequest postRequest = new CommonRequest();
        postRequest.setDomain(DOMAIN);
        postRequest.setVersion(API_VERSION);
        postRequest.setAction(POST_REQUEST_ACTION);
        postRequest.setProduct(PRODUCT);
        JSONObject taskObject = new JSONObject();
        taskObject.put(KEY_APP_KEY, properties.getAliCloudAppKey());
        taskObject.put(KEY_FILE_LINK, fileLink);
        // 新接入请使用4.0版本，已接入（默认2.0）如需维持现状，请注释掉该参数设置。
        taskObject.put(KEY_VERSION, "4.0");
        String task = taskObject.toString();
        System.out.println(task);
        // 设置以上JSON字符串为Body参数。
        postRequest.putBodyParameter(KEY_TASK, task);
        // 设置为POST方式的请求。
        postRequest.setMethod(MethodType.POST);
        // postRequest.setHttpContentType(FormatType.JSON);    //当aliyun-java-sdk-core 版本为4.6.0及以上时，请取消该行注释

        String taskId = null;
        try {
            CommonResponse postResponse = iAcsClient.getCommonResponse(postRequest);
            System.err.println("提交录音文件识别请求的响应：" + postResponse.getData());
            if (postResponse.getHttpStatus() == 200) {
                JSONObject result =  JSONUtil.parseObj(postResponse.getData());
                String statusText = result.getStr(KEY_STATUS_TEXT);
                if (STATUS_SUCCESS.equals(statusText)) {
                    taskId = result.getStr(KEY_TASK_ID);
                }
            }
        } catch (ClientException e) {
            e.printStackTrace();
        }

        //getFileTransResult
        if(taskId != null) {
            CommonRequest getRequest = new CommonRequest();
            getRequest.setDomain(DOMAIN);
            getRequest.setVersion(API_VERSION);
            getRequest.setAction(GET_REQUEST_ACTION);
            getRequest.setProduct(PRODUCT);
            getRequest.putQueryParameter(KEY_TASK_ID, taskId);
            getRequest.setMethod(MethodType.GET);
            String result = null;
            while (true) {
                try {
                    CommonResponse getResponse = iAcsClient.getCommonResponse(getRequest);
                    System.err.println("识别查询结果：" + getResponse.getData());
                    if (getResponse.getHttpStatus() != 200) {
                        break;
                    }
                    JSONObject rootObj = JSONUtil.parseObj(getResponse.getData());
                    String statusText = rootObj.getStr(KEY_STATUS_TEXT);
                    if (STATUS_RUNNING.equals(statusText) || STATUS_QUEUEING.equals(statusText)) {
                        // 继续轮询，注意设置轮询时间间隔。
                        Thread.sleep(5000);
                    }
                    else {
                        // 状态信息为成功，返回识别结果；状态信息为异常，返回空。
                        if (STATUS_SUCCESS.equals(statusText)) {
                            JSONObject res = rootObj.getJSONObject(KEY_RESULT);
                            JSONArray sentencesArray = res.getJSONArray("Sentences");
                            result = sentencesArray.stream()
                                    .map(obj -> ((JSONObject) obj).getStr("Text"))
                                    .reduce("", String::concat);

                            // 状态信息为成功，但没有识别结果，则可能是由于文件里全是静音、噪音等导致识别为空。
                            if(result == null) {
                                result = "";
                            }
                        }
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return result;
        }
        return "";
    }


}
