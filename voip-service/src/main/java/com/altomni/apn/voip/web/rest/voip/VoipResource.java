package com.altomni.apn.voip.web.rest.voip;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import com.altomni.apn.common.vo.voip.VoipContactDetailVO;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.voip.domain.enumeration.CallPlatform;
import com.altomni.apn.voip.service.alicloud.AliCloudASRService;
import com.altomni.apn.voip.service.amazonconnect.AmazonConnectService;
import com.altomni.apn.voip.service.dto.openai.TranscriptionSummaryDTO;
import com.altomni.apn.voip.service.vo.openai.TranscriptionNoteVO;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteDTO;
import com.altomni.apn.voip.service.dto.voip.PhoneTranscriptionDTO;
import com.altomni.apn.voip.service.openai.OpenAIService;
import com.altomni.apn.voip.service.vo.voiptalent.VoipTalentContactVO;
import com.altomni.apn.voip.service.voicemail.VoicemailNotificationService;
import com.altomni.apn.voip.service.voip.VoipRecordService;
import com.altomni.apn.voip.service.voip.VoipTranscriptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Set;

/**
 * REST controller for managing voip.
 */
@RestController
@RequestMapping("/api/v3")
public class VoipResource {

    private final Logger log = LoggerFactory.getLogger(VoipResource.class);

    private static final String ENTITY_NAME = "voip";

    private final VoipTranscriptionService voipTranscriptionService;

    private final VoipRecordService voipRecordService;

    private final OpenAIService openAIService;

    private final AmazonConnectService amazonConnectService;

    public VoipResource(VoipTranscriptionService voipTranscriptionService, VoipRecordService voipRecordService, OpenAIService openAIService, AmazonConnectService amazonConnectService) {
        this.voipTranscriptionService = voipTranscriptionService;
        this.voipRecordService = voipRecordService;
        this.openAIService = openAIService;
        this.amazonConnectService = amazonConnectService;
    }


    @NoRepeatSubmit
    @GetMapping("/connect/voip-contact/{id}")
    public ResponseEntity<VoipContactDTO> getVoipContact(@PathVariable String id) {
        log.info("[APN: Voip @{}] REST request to get contact record by contact id: {}", SecurityUtils.getUserId(), id);
        VoipContactDTO contactRecord = voipRecordService.getVoipContact(id);
        return ResponseEntity.ok().body(contactRecord);
    }

    @NoRepeatSubmit
    @PostMapping("/connect/live-transcription/summary")
    public ResponseEntity<TranscriptionNoteVO> getLiveTranscriptionSummary(@RequestBody TranscriptionSummaryDTO transcriptionSummaryDTO) {
        log.info("[APN: Voip @{}] REST request to get live caption summary by phone : {}", SecurityUtils.getUserId(), transcriptionSummaryDTO);
        return openAIService.getLiveTranscriptionSummary(transcriptionSummaryDTO);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/transcriptions-record/{phoneCallId}")
    public ResponseEntity<PhoneTranscriptionDTO> getLiveTranscriptions(@PathVariable String phoneCallId) {
        log.info("[APN: Voip @{}] REST request to get live captions by phoneCallId : {}", SecurityUtils.getUserId(), phoneCallId);
        PhoneTranscriptionDTO transcription = voipTranscriptionService.getTranscription(phoneCallId);
        return ResponseEntity.ok().body(transcription);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/record-contact-detail/phoneCallId/{phoneCallId}")
    public ResponseEntity<VoipContactDetailVO> getVoipContactDetail(@PathVariable(value = "phoneCallId") String phoneCallId) {
        log.info("[APN: Voip @{}] REST request to get contact record detail by contact id: {}", SecurityUtils.getUserId(), phoneCallId);
        VoipContactDetailVO contactRecordDetail = voipRecordService.getVoipContactDetail(phoneCallId);
        return ResponseEntity.ok().body(contactRecordDetail);
    }

    @NoRepeatSubmit
    @PostMapping("/connect/record-contact-detail/by-phone-call-id-in")
    public ResponseEntity<List<VoipContactDetailVO>> getVoipContactDetailByPhoneCallIdIn(@RequestBody Set<String> phoneCallIds) {
        log.info("[APN: Voip @{}] REST request to get contact record detail by contact ids: {}", SecurityUtils.getUserId(), phoneCallIds);
        List<VoipContactDetailVO> contactRecordDetails = voipRecordService.getVoipContactDetails(phoneCallIds);
        return ResponseEntity.ok().body(contactRecordDetails);
    }

    @NoRepeatSubmit
    @PostMapping("/connect/record-contact")
    public ResponseEntity<VoipContactDTO> recordVoipContact(@RequestBody VoipContactDTO voipContactDTO) {
        log.info("[APN: Voip @{}] REST request to create contact record", SecurityUtils.getUserId());
        VoipContactDTO contactInfo = voipRecordService.recordVoipContact(voipContactDTO);
        return ResponseEntity.ok().body(contactInfo);
    }

    @NoRepeatSubmit
    @PutMapping("/connect/record-contact/calling-status")
    public ResponseEntity<VoipContactDTO> updateVoipContactPhoneCallStatus(@RequestBody VoipContactDTO voipContactDTO) {
        log.info("[APN: Voip @{}] REST request to update contact record phone call status", SecurityUtils.getUserId());
        VoipContactDTO contactInfo = voipRecordService.updateVoipContactPhoneCallingStatus(voipContactDTO);
        return ResponseEntity.ok().body(contactInfo);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/record-contact/last")
    public ResponseEntity<VoipContactDTO> getLastVoipContact() {
        log.info("[APN: Voip @{}] REST request to get last record contact ", SecurityUtils.getUserId());
        VoipContactDTO voipContactDTO = voipRecordService.getLastVoipContact();
        if(voipContactDTO == null) return ResponseEntity.notFound().build();
        return ResponseEntity.ok().body(voipContactDTO);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/contact/talent/{talentId}/platform/{platform}")
    public ResponseEntity<VoipTalentContactVO> getVoipTalentContactInfo(@PathVariable Long talentId, @PathVariable CallPlatform platform) throws IOException {
        log.info("[APN: Voip @{}] REST request to get voip talent contact info by talentId: {}", SecurityUtils.getUserId(), talentId);
        VoipTalentContactVO contactVO = voipRecordService.getVoipTalentContactVO(talentId, platform);
        return ResponseEntity.ok().body(contactVO);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/phone-recording/{phoneCallId}")
    public ResponseEntity<String> getPhoneRecording(@PathVariable String phoneCallId) {
        log.info("[APN: Voip @{}] REST request to get phone recording by phoneCallId: {}", SecurityUtils.getUserId(), phoneCallId);
        return amazonConnectService.getAmazonConnectRecording(phoneCallId);
    }

    @NoRepeatSubmit
    @PutMapping("/connect/phone-recording/delete")
    public ResponseEntity<Void> deletePhoneRecording(@RequestBody PhoneRecordDeleteDTO phoneRecordDeleteDTO) {
        log.info("[APN: Voip @{}] REST request to delete/keep phone recording : {}", SecurityUtils.getUserId(), phoneRecordDeleteDTO);
        return amazonConnectService.deleteAmazonConnectRecording(phoneRecordDeleteDTO);
    }

    @NoRepeatSubmit
    @GetMapping("/connect/contact/call-type")
    public ResponseEntity<EnumDictVO> getVoipCallTypeByTalentAndJob(@RequestParam("talentId") Long talentId, @RequestParam("jobId") Long jobId, @RequestParam("type") SortType type) throws URISyntaxException {
        log.info("[APN: Voip @{}] REST request to get call type by talentId : {} and jobId : {}", SecurityUtils.getUserId(), talentId, jobId);
        EnumDictVO callType = voipRecordService.getCallTypeByTalentAndJob(talentId, jobId, type);
        return ResponseEntity.ok().body(callType);
    }

    @NoRepeatSubmit
    @PutMapping("/connect/record-contact")
    public ResponseEntity<Void> updateVoipContact(@RequestBody VoipContactDTO voipContactDTO) {
        log.info("[APN: Voip @{}] REST request to update contact record: {}", SecurityUtils.getUserId(), voipContactDTO);
        voipRecordService.updateVoipContactRecord(voipContactDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/contact/search-by-report")
    public ResponseEntity<List<VoipContactDTO>> findAllByVoipReport(@RequestBody VoipReportDTO voipReportDTO) {
        log.info("[APN: Voip @{}] REST request to get all voip contact by voip report filter: {}", SecurityUtils.getUserId(), voipReportDTO);
        List<VoipContactDTO> voipContacts = voipRecordService.findAllByVoipReport(voipReportDTO);
        return ResponseEntity.ok().body(voipContacts);
    }

    @GetMapping("/contact/search-by-user-id/{userId}")
    public ResponseEntity<List<VoipContactDTO>> findAllByUser(@PathVariable Long userId) {
        log.info("[APN: Voip @{}] REST request to get all voip contact by userId: {}", SecurityUtils.getUserId(), userId);
        List<VoipContactDTO> voipContacts = voipRecordService.findAllByUserId(userId);
        return ResponseEntity.ok().body(voipContacts);

    }

    @PostMapping("/transcription/search-by-phone-call-ids")
    public ResponseEntity<List<PhoneTranscription>> findAllPhoneTranscriptionByPhoneCallIds(@RequestBody Set<String> phoneCallIds) {
        log.info("[APN: Voip @{}] REST request to get all voip transcription by voip phoneCallIds filter: {}", SecurityUtils.getUserId(), phoneCallIds);
        List<PhoneTranscription> voipPhoneTranscriptions = voipTranscriptionService.findAllPhoneTranscriptionByPhoneCallIds(phoneCallIds);
        return ResponseEntity.ok().body(voipPhoneTranscriptions);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
