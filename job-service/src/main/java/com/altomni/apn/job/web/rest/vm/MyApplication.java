package com.altomni.apn.job.web.rest.vm;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

/**
 * Created by <PERSON> on 9/19/2017.
 */
@Data
@Entity
public class MyApplication implements Serializable {

    @Id
    private Long id;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String company;

    private Long talentId;

    private String fullName;

    private String email;

    private String phone;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

}
