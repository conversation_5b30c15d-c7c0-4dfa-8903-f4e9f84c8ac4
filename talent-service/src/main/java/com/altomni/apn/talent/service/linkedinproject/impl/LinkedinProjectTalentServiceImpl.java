package com.altomni.apn.talent.service.linkedinproject.impl;


import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForTalentVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.LinkedinProjectTalentFilter;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.linkedinproject.*;
import com.altomni.apn.talent.domain.record.TalentTrackingNote;
import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import com.altomni.apn.talent.repository.linkedinproject.*;
import com.altomni.apn.talent.repository.record.TalentTrackingNoteRepository;
import com.altomni.apn.talent.repository.record.TalentTrackingRecordRepository;
import com.altomni.apn.talent.repository.talent.TalentNoteRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.dto.linkedinproject.*;
import com.altomni.apn.talent.service.linkedinproject.LinkedinProjectTalentService;
import com.altomni.apn.talent.service.redis.RedisService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentStatusCountVO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.config.constants.RedisConstants.AGENCY_NEW_APPLICATION_KEY_PATTERN;
import static com.altomni.apn.common.config.constants.RedisConstants.LINKEDIN_PROJECT_NEW_TALENT_KEY_PATTERN;

/**
 * Service Implementation for managing LinkedinProjectTalent.
 */
@Slf4j
@Service
@Transactional
public class LinkedinProjectTalentServiceImpl implements LinkedinProjectTalentService {

    private final LinkedinProjectTalentRepository linkedinProjectTalentRepository;

    private final LinkedinTalentRepository linkedinTalentRepository;

    private final LinkedinTalentContactRepository linkedinTalentContactRepository;

    private final LinkedinProjectRepository linkedinProjectRepository;

    private final TalentTrackingRecordRepository talentTrackingRecordRepository;

    private final TalentTrackingNoteRepository talentTrackingNoteRepository;

    private final UserService userService;

    private final ApplicationService applicationService;
//
//    private final JobRepositoryV2 jobRepository;

    private final TalentNoteRepository talentNoteRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    private final LinkedinProjectMemberRepository linkedinProjectMemberRepository;

    private final RedisService redisService;

    private static final int REDIS_NEW_LINKEDIN_TALENT_REMINDER_EXP_TIME = 30 * 24 * 60 * 60; //30 days

    public LinkedinProjectTalentServiceImpl(LinkedinProjectTalentRepository linkedinProjectTalentRepository, LinkedinTalentRepository linkedinTalentRepository,
                                            LinkedinTalentContactRepository linkedinTalentContactRepository, LinkedinProjectRepository linkedinProjectRepository,
                                            TalentTrackingRecordRepository talentTrackingRecordRepository, TalentTrackingNoteRepository talentTrackingNoteRepository,
                                            UserService userService, ApplicationService applicationService, TalentNoteRepository talentNoteRepository,
                                            CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties,
                                            LinkedinProjectMemberRepository linkedinProjectMemberRepository, RedisService redisService) {
        this.linkedinProjectTalentRepository = linkedinProjectTalentRepository;
        this.linkedinTalentRepository = linkedinTalentRepository;
        this.linkedinTalentContactRepository = linkedinTalentContactRepository;
        this.linkedinProjectRepository = linkedinProjectRepository;
        this.talentTrackingRecordRepository = talentTrackingRecordRepository;
        this.talentTrackingNoteRepository = talentTrackingNoteRepository;
        this.userService = userService;
        this.applicationService = applicationService;
        this.talentNoteRepository = talentNoteRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
        this.linkedinProjectMemberRepository = linkedinProjectMemberRepository;
        this.redisService = redisService;
    }


    private void verifyTenant(Long tenantId) {
        if (!SecurityUtils.isCurrentTenant(tenantId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYTENANT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private LinkedinProject verifyLinkedinProjectTalent(LinkedinProjectTalent linkedinProjectTalent) {
        if (linkedinProjectTalent.getLinkedinProjectId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYLINKEDINPROJECTTALENT_LINKEDINPROJECTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if (linkedinProjectTalent.getLinkedinTalentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYLINKEDINPROJECTTALENT_LINKEDINPROJECTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return verifyLinkedinProject(linkedinProjectTalent.getLinkedinProjectId());
    }

    private LinkedinProject verifyLinkedinProject(Long linkedinProjectId) {
        LinkedinProject linkedinProject = linkedinProjectRepository.findById(linkedinProjectId).orElse(null);
        if (linkedinProject == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYLINKEDINPROJECTTALENT_LINKEDINPROJECTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if (!SecurityUtils.isCurrentTenant(linkedinProject.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYTENANT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return linkedinProject;
    }

    /**
     * Save a linkedinProjectTalentDTO.
     *
     * @param linkedinProjectTalentDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public LinkedinProjectTalentVO create(LinkedinProjectTalentDTO linkedinProjectTalentDTO) {
        if (linkedinProjectTalentDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_CREATE_IDEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        LinkedinProjectTalent linkedinProjectTalent = LinkedinProjectTalent.fromLinkedinProjectTalentDTO(linkedinProjectTalentDTO);
        LinkedinProject linkedinProject = verifyLinkedinProjectTalent(linkedinProjectTalent);
        linkedinProjectTalent.setTenantId(SecurityUtils.getTenantId());
        LinkedinProjectTalent exists = linkedinProjectTalentRepository
            .findByLinkedinProjectIdAndLinkedinTalentId(linkedinProjectTalent.getLinkedinProjectId(), linkedinProjectTalent.getLinkedinTalentId());
        if (exists != null) {
            return toDto(exists);
        }
        linkedinProjectTalent.setCandidateStatus(CandidateStatus.ACTIVE);
        linkedinProjectTalent = linkedinProjectTalentRepository.save(linkedinProjectTalent);

        cacheNewLinkedinTalentReminderToRedis(linkedinProjectTalent, linkedinProject);
        return toDto(linkedinProjectTalent);

    }

    public void cacheNewLinkedinTalentReminderToRedis(LinkedinProjectTalent linkedinProjectTalent, LinkedinProject linkedinProject) {
        try {
            Set<Long> userIds = new HashSet<>();
            if (Visibility.VISIBLE_TO_PROJECT_MEMBERS.equals(linkedinProject.getVisibility())) {
                userIds = linkedinProjectMemberRepository.findByLinkedinProjectId(linkedinProjectTalent.getLinkedinProjectId()).stream().map(LinkedinProjectMember::getUserId).collect(Collectors.toSet());
            } else {
                userIds = userService.getAllBriefUsers().getBody().stream().map(UserBriefDTO::getId).collect(Collectors.toSet());
            }

            String key = String.format(LINKEDIN_PROJECT_NEW_TALENT_KEY_PATTERN, linkedinProjectTalent.getLinkedinProjectId());

            redisService.sadd(key, userIds, REDIS_NEW_LINKEDIN_TALENT_REMINDER_EXP_TIME);
        } catch (Exception e) {
            log.error("[LinkedinProjectTalentService: cacheNewLinkedinTalentReminderToRedis] error msg: {}", e.getMessage(), e);
        }
    }


    /**
     * Save a linkedinProjectTalent.
     *
     * @param linkedinProjectTalent the entity to save
     * @return the persisted entity
     */
    @Override
    public LinkedinProjectTalentVO update(LinkedinProjectTalentDTO linkedinProjectTalent) {
        LinkedinProjectTalent exists = linkedinProjectTalentRepository.findById(linkedinProjectTalent.getId()).orElse(null);
        if (exists == null) {
            throw new CustomParameterizedException("LinkedinProjectTalent not exist by id: " + linkedinProjectTalent.getId());
        }
        ServiceUtils.myCopyProperties(linkedinProjectTalent, exists, LinkedinProjectTalent.UpdateSkipProperties);
        return toDto(linkedinProjectTalentRepository.save(exists));
    }

    /**
     * Get all the linkedinProjectTalents.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<LinkedinProjectTalentVO> findAll(Long linkedinProjectId, LinkedinProjectTalentFilter filter, Pageable pageable) {
        verifyLinkedinProject(linkedinProjectId);
        if (LinkedinProjectTalentFilter.ACTIVE_CANDIDATES_WITH_CONTACT_INFO.equals(filter)) {
            return linkedinProjectTalentRepository.findByCandidateStatusActiveWithContactInfo(linkedinProjectId, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.ACTIVE_CANDIDATES_WITHOUT_CONTACT_INFO.equals(filter)) {
            return linkedinProjectTalentRepository.findByCandidateStatusActiveWithoutContactInfo(linkedinProjectId, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.ARCHIVED_CANDIDATES.equals(filter)) {
            return linkedinProjectTalentRepository.findByCandidateStatusArchived(linkedinProjectId, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.UN_CONTACTED.equals(filter)) {
            return linkedinProjectTalentRepository.findByContactStatus(linkedinProjectId, ContactStatus.UN_CONTACTED, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.UN_CONTACTED_CANDIDATES_WITH_CONTACT_INFO.equals(filter)) {
            return linkedinProjectTalentRepository.findByUnContactedCandidateStatusActiveWithContactInfo(linkedinProjectId, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.UN_CONTACTED_CANDIDATES_WITHOUT_CONTACT_INFO.equals(filter)) {
            return linkedinProjectTalentRepository.findByUnContactedCandidateStatusActiveWithoutContactInfo(linkedinProjectId, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.CONTACTED.equals(filter)) {
            return linkedinProjectTalentRepository.findByContactStatus(linkedinProjectId, ContactStatus.CONTACTED, pageable).map(this::toDto);
        } else if (LinkedinProjectTalentFilter.REPLIED.equals(filter)) {
            return linkedinProjectTalentRepository.findByContactStatus(linkedinProjectId, ContactStatus.REPLIED, pageable).map(this::toDto);
        }
        return new PageImpl<>(Lists.newArrayList());
    }

    @Override
    public Page<LinkedinProjectTalentVO> findLinkedinTalentsByProjectId(Long projectId, Boolean hasContactInfo, Boolean isInApplication, CandidateStatus candidateStatus, ContactStatus contactStatus, Pageable pageable) {
        verifyLinkedinProject(projectId);

        Specification<LinkedinProjectTalent> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("linkedinProjectId"), projectId));

            if (Objects.nonNull(hasContactInfo)) {
                predicates.add(cb.equal(root.get("hasContactInfo"), hasContactInfo));
            }
            if (Objects.nonNull(isInApplication)) {
                predicates.add(cb.equal(root.get("isInApplication"), isInApplication));
            }
            if (Objects.nonNull(candidateStatus)) {
                predicates.add(cb.equal(root.get("candidateStatus"), candidateStatus));
            }
            if (Objects.nonNull(contactStatus)) {
                predicates.add(cb.equal(root.get("contactStatus"), contactStatus));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<LinkedinProjectTalent> result = linkedinProjectTalentRepository.findAll(spec, pageable);

        // 转 VO
        return result.map(this::toDto);
    }

    private LinkedinProjectTalentVO toDto(LinkedinProjectTalent linkedinProjectTalent) {
        LinkedinProjectTalentVO result = new LinkedinProjectTalentVO();
        ServiceUtils.myCopyProperties(linkedinProjectTalent, result);
        List<LinkedinProjectTalent> projectTalents = linkedinProjectTalentRepository.findAllByLinkedinTalentId(linkedinProjectTalent.getLinkedinTalentId());
        if (CollectionUtils.isNotEmpty(projectTalents)) {
            result.setProjectsCount(projectTalents.size());
            result.setProjects(projectTalents.stream().map(this::toProjectVM).collect(Collectors.toList()));
        }
        List<LinkedinTalentContact> linkedinTalentContacts = linkedinTalentContactRepository.findAllByLinkedinTalentId(linkedinProjectTalent.getLinkedinTalentId());
        if (CollectionUtils.isNotEmpty(linkedinTalentContacts)) {
            List<String> contacts = linkedinTalentContacts.stream().map(LinkedinTalentContact::getContact).collect(Collectors.toList());
            List<TalentTrackingRecord> talentTrackingRecords = talentTrackingRecordRepository.findAllByTenantIdAndContactList(SecurityUtils.getTenantId(), contacts);
            if (CollectionUtils.isNotEmpty(talentTrackingRecords)) {
                result.setContactHistoriesCount(talentTrackingRecords.size());
                result.setContactHistories(talentTrackingRecords.stream().map(this::toContactVM).collect(Collectors.toList()));
            }
        }
        LinkedinTalent linkedinTalent = linkedinTalentRepository.findById(linkedinProjectTalent.getLinkedinTalentId()).orElse(null);
        if (linkedinTalent != null) {
            linkedinTalent.setContacts(linkedinTalentContacts);
            result.setLinkedinTalent(linkedinTalent);
            if (linkedinTalent.getApnTalentId() != null) {
                List<TalentRecruitmentProcessForTalentVO> applications = applicationService.getTalentRecruitmentProcessListByTalentId(linkedinTalent.getApnTalentId()).getBody();
                if (CollectionUtils.isNotEmpty(applications)) {
                    result.setAppliedJobsCount(applications.size());
                    result.setAppliedJobs(applications.stream().map(this::toAppliedJobVM).collect(Collectors.toList()));
                }
            }
        }
        List<LinkedinProjectTalentNoteVM> linkedinProjectTalentNoteVMs = getNotes(linkedinTalent);
        if (CollectionUtils.isNotEmpty(linkedinProjectTalentNoteVMs)) {
            result.setNotesCount(linkedinProjectTalentNoteVMs.size());
            result.setNotes(linkedinProjectTalentNoteVMs);
        }
        result.setHasContactInfo(BooleanUtils.isTrue(linkedinProjectTalent.getHasContactInfo()));
        result.setIsInApplication(BooleanUtils.isTrue(linkedinProjectTalent.getInApplication()));
        return result;
    }

    private LinkedinProjectTalentProjectVM toProjectVM(LinkedinProjectTalent linkedinProjectTalent) {
        LinkedinProjectTalentProjectVM result = new LinkedinProjectTalentProjectVM();
        LinkedinProject linkedinProject = linkedinProjectRepository.findById(linkedinProjectTalent.getLinkedinProjectId()).orElse(null);
        if (ObjectUtil.isNotEmpty(linkedinProject)) {
            result.setId(linkedinProject.getId());
            result.setName(linkedinProject.getName());
        }
        result.setContactStatus(linkedinProjectTalent.getContactStatus());
        return result;
    }

    private LinkedinProjectTalentContactVM toContactVM(TalentTrackingRecord talentTrackingRecord) {
        LinkedinProjectTalentContactVM result = new LinkedinProjectTalentContactVM();
        result.setContactType(talentTrackingRecord.getTrackingType());
        result.setTouchTime(talentTrackingRecord.getTouchTime());
        if (ObjectUtil.isNotEmpty(talentTrackingRecord.getUserId())) {
            User user = userService.getUserById(talentTrackingRecord.getUserId()).getBody();
            if (user != null && ObjectUtil.isNotEmpty(user.getId())) {
                result.setUserId(user.getId());
                result.setUser(user);
            }
        }
        return result;
    }

    private LinkedinProjectTalentAppliedJobVM toAppliedJobVM(TalentRecruitmentProcessForTalentVO application) {
        LinkedinProjectTalentAppliedJobVM result = new LinkedinProjectTalentAppliedJobVM();
        result.setStatus(application.getLastNodeType());
        result.setAppliedDate(application.getLastModifiedDate());
        result.setJobId(application.getJobId());
        result.setJobTitle(application.getJobTitle());
        return result;
    }

    private List<LinkedinProjectTalentNoteVM> getNotes(LinkedinTalent linkedinTalent) {
        List<LinkedinProjectTalentNoteVM> result = new ArrayList<>();
        if (linkedinTalent != null && linkedinTalent.getTrackingNotePlatformId() != null) {
            List<TalentTrackingNote> talentTrackingNotes = talentTrackingNoteRepository.findAllByTenantIdAndPlatformIdAndTrackingPlatform(SecurityUtils.getTenantId(), linkedinTalent.getTrackingNotePlatformId(), TrackingPlatform.LINKED_IN);
            if (CollectionUtils.isNotEmpty(talentTrackingNotes)) {
                result.addAll(talentTrackingNotes.stream().map(this::toTrackingNote).collect(Collectors.toList()));
            }
        }
        if (linkedinTalent != null && linkedinTalent.getApnTalentId() != null) {
            List<TalentNote> talentNotes = talentNoteRepository.findAllByTalentId(linkedinTalent.getApnTalentId());
            if (CollectionUtils.isNotEmpty(talentNotes)) {
                result.addAll(talentNotes.stream().map(this::toTalentNote).collect(Collectors.toList()));
            }
        }
        return result;
    }

    private LinkedinProjectTalentNoteVM toTrackingNote(TalentTrackingNote talentTrackingNote) {
        LinkedinProjectTalentNoteVM result = new LinkedinProjectTalentNoteVM();
        result.setNote(talentTrackingNote.getNote());
        result.setCreatedDate(talentTrackingNote.getCreatedDate());
        if (ObjectUtil.isNotEmpty(talentTrackingNote.getUserId())) {
            User user = userService.getUserById(talentTrackingNote.getUserId()).getBody();
            if (user != null && ObjectUtil.isNotEmpty(user.getId())) {
                result.setUserId(user.getId());
                result.setUser(user);
            }
        }
        return result;
    }

    private LinkedinProjectTalentNoteVM toTalentNote(TalentNote talentNote) {
        LinkedinProjectTalentNoteVM result = new LinkedinProjectTalentNoteVM();
        result.setNote(talentNote.getNote());
        result.setCreatedDate(talentNote.getCreatedDate());
        if (ObjectUtil.isNotEmpty(talentNote.getUserId())) {
            User user = userService.getUserById(talentNote.getUserId()).getBody();
            if (user != null && ObjectUtil.isNotEmpty(user.getId())) {
                result.setUserId(user.getId());
                result.setUser(user);
            }
        }
        return result;
    }

    @Override
    public LinkedinProjectTalentStatusCountVO statusCount(Long linkedinProjectId) {
        LinkedinProjectTalentStatusCountVO result = new LinkedinProjectTalentStatusCountVO();
        result.setActiveCandidatesWithContactInfo(linkedinProjectTalentRepository.countByCandidateStatusActiveWithContactInfo(linkedinProjectId));
        result.setActiveCandidatesWithoutContactInfo(linkedinProjectTalentRepository.countByCandidateStatusActiveWithoutContactInfo(linkedinProjectId));
        result.setArchivedCandidates(linkedinProjectTalentRepository.countByCandidateStatusArchived(linkedinProjectId));
        result.setUnContacted(linkedinProjectTalentRepository.countByContactStatus(linkedinProjectId, ContactStatus.UN_CONTACTED.toDbValue()));
        result.setUnContactedActiveCandidatesWithContactInfo(linkedinProjectTalentRepository.countByUnContactedCandidateStatusActiveWithContactInfo(linkedinProjectId));
        result.setUnContactedActiveCandidatesWithoutContactInfo(linkedinProjectTalentRepository.countByUnContactedCandidateStatusActiveWithoutContactInfo(linkedinProjectId));
        result.setContacted(linkedinProjectTalentRepository.countByContactStatus(linkedinProjectId, ContactStatus.CONTACTED.toDbValue()));
        result.setReplied(linkedinProjectTalentRepository.countByContactStatus(linkedinProjectId, ContactStatus.REPLIED.toDbValue()));
        result.setEmailedCandidates(linkedinProjectTalentRepository.countByEmailedCandidates(linkedinProjectId));
        return result;
    }
}
