package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectTalent;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectStatsDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the LinkedinProjectTalent entity.
 */
@Repository
public interface LinkedinProjectTalentRepository extends JpaRepository<LinkedinProjectTalent, Long>, JpaSpecificationExecutor<LinkedinProjectTalent> {

    LinkedinProjectTalent findByLinkedinProjectIdAndLinkedinTalentId(Long linkedinProjectId, String linkedinTalentId);

    List<LinkedinProjectTalent> findAllByLinkedinTalentId(String linkedinTalentId);

    Page<LinkedinProjectTalent> findAllByLinkedinProjectId(Long linkedinProjectId, Pageable pageable);

    List<LinkedinProjectTalent> findAllByLinkedinProjectId(Long linkedinProjectId);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.candidate_status = 0 and exists (select 1 from linkedin_talent_contact ltc " +
        " where ltc.linkedin_talent_id = t.linkedin_talent_id)", nativeQuery = true)
    Integer countByCandidateStatusActiveWithContactInfo(Long linkedinProjectId);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 " +
        " AND lpt.candidateStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus.ACTIVE" +
        " AND EXISTS (select ltc from LinkedinTalentContact ltc where lpt.linkedinTalentId = ltc.linkedinTalentId)")
    Page<LinkedinProjectTalent> findByCandidateStatusActiveWithContactInfo(Long linkedinProjectId, Pageable pageable);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.candidate_status = 0 and not exists (select 1 from linkedin_talent_contact ltc " +
        " where ltc.linkedin_talent_id = t.linkedin_talent_id)", nativeQuery = true)
    Integer countByCandidateStatusActiveWithoutContactInfo(Long linkedinProjectId);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 " +
        " AND lpt.candidateStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus.ACTIVE" +
        " AND NOT EXISTS (select ltc from LinkedinTalentContact ltc where lpt.linkedinTalentId = ltc.linkedinTalentId)")
    Page<LinkedinProjectTalent> findByCandidateStatusActiveWithoutContactInfo(Long linkedinProjectId, Pageable pageable);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.candidate_status = 1", nativeQuery = true)
    Integer countByCandidateStatusArchived(Long linkedinProjectId);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 " +
        " AND lpt.candidateStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus.ARCHIVED")
    Page<LinkedinProjectTalent> findByCandidateStatusArchived(Long linkedinProjectId, Pageable pageable);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.contact_status = ?2", nativeQuery = true)
    Integer countByContactStatus(Long linkedinProjectId, Integer contactStatus);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 AND lpt.contactStatus = ?2 ")
    Page<LinkedinProjectTalent> findByContactStatus(Long linkedinProjectId, ContactStatus contactStatus, Pageable pageable);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.candidate_status = 0 and t.contact_status = 0 " +
        " and exists (select 1 from linkedin_talent_contact ltc " +
        " where ltc.linkedin_talent_id = t.linkedin_talent_id)", nativeQuery = true)
    Integer countByUnContactedCandidateStatusActiveWithContactInfo(Long linkedinProjectId);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 " +
        " AND lpt.candidateStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus.ACTIVE " +
        " AND lpt.contactStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus.UN_CONTACTED " +
        " AND EXISTS (select ltc from LinkedinTalentContact ltc where lpt.linkedinTalentId = ltc.linkedinTalentId)")
    Page<LinkedinProjectTalent> findByUnContactedCandidateStatusActiveWithContactInfo(Long linkedinProjectId, Pageable pageable);

    @Query(value = "SELECT count(1) FROM linkedin_project_talent t WHERE t.linkedin_project_id = ?1 " +
        " and t.candidate_status = 0 and t.contact_status = 0 " +
        " and not exists (select 1 from linkedin_talent_contact ltc " +
        " where ltc.linkedin_talent_id = t.linkedin_talent_id)", nativeQuery = true)
    Integer countByUnContactedCandidateStatusActiveWithoutContactInfo(Long linkedinProjectId);

    @Query(value = "SELECT lpt FROM LinkedinProjectTalent lpt WHERE lpt.linkedinProjectId = ?1 " +
        " AND lpt.candidateStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus.ACTIVE " +
        " AND lpt.contactStatus = com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus.UN_CONTACTED " +
        " AND NOT EXISTS (select ltc from LinkedinTalentContact ltc where lpt.linkedinTalentId = ltc.linkedinTalentId)")
    Page<LinkedinProjectTalent> findByUnContactedCandidateStatusActiveWithoutContactInfo(Long linkedinProjectId, Pageable pageable);

    @Query(value = "SELECT count(distinct linkedin_talent_id) FROM linkedin_project_talent t " +
        " WHERE t.linkedin_project_id = ?1 " +
        " and exists (select 1 from linkedin_talent_contact ltc, talent_tracking_record ttr " +
        " where ltc.linkedin_talent_id = t.linkedin_talent_id and ltc.contact_type = 2 " +
        " and ltc.contact = ttr.contact and ttr.tracking_type = 0)", nativeQuery = true)
    Integer countByEmailedCandidates(Long linkedinProjectId);

    @Query("SELECT new com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectStatsDTO(" +
            "t.linkedinProjectId, " +
            "COUNT(t), " +
            "SUM(CASE WHEN t.hasContactInfo = true THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN t.isInApplication = true THEN 1 ELSE 0 END)) " +
            "FROM LinkedinProjectTalent t " +
            "WHERE t.tenantId = :tenantId AND t.linkedinProjectId = :linkedinProjectId")
    LinkedinProjectStatsDTO countStatsByTenantAndProject(@Param("tenantId") Long tenantId,
                                                         @Param("linkedinProjectId") Long linkedinProjectId);

    @Query("SELECT new com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectStatsDTO(" +
            "t.linkedinProjectId, " +
            "COUNT(t), " +
            "COALESCE(SUM(CASE WHEN t.hasContactInfo = true THEN 1 ELSE 0 END), 0), " +
            "COALESCE(SUM(CASE WHEN t.isInApplication = true THEN 1 ELSE 0 END), 0)) " +
            "FROM LinkedinProjectTalent t " +
            "WHERE t.tenantId = :tenantId AND t.linkedinProjectId IN :projectIds " +
            "GROUP BY t.linkedinProjectId")
    List<LinkedinProjectStatsDTO> countStatsByTenantAndProjectIds(@Param("tenantId") Long tenantId,
                                                                  @Param("projectIds") List<Long> projectIds);
}
