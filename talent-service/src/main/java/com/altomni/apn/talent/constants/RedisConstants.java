package com.altomni.apn.talent.constants;

/**
 * Application constants.
 * <AUTHOR>
 */
public final class RedisConstants {

    // request body key
    public static final String BODY_KEY_RESULT_UUID = "result_uuid";

    public static final String BODY_KEY_BLACKLISTED_ESIDS = "blacklisted_esIds";

    // redis storage data key
    public static final String DATA_KEY_RATER = "rater:";

    public static final String DATA_KEY_DATA = ":data";

    public static final String DATA_KEY_RECOMMENDATIONS = ":recommendations";

    public static final String DATA_KEY_RECOMMENDATIONS_BIZ = ":recommendations_biz";

    public static final String DATA_KEY_RECOMMENDATIONS_COMMON = ":recommendations_common";

    public static final String DATA_KEY_STATUS = ":status";

    public static final String DATA_KEY_TOTAL = ":total";

    public static final String DATA_KEY_REPORT = "report:";

    // parser
    public static final String DATA_KEY_PARSER = "parser:";

    public static final String DATA_KEY_RESUME = "resume:";

    public static final String DATA_KEY_LAST_BASIC_INFO = ":basic_info";

    public static final String DATA_KEY_LAST_EDUCATION = ":education";

    public static final String DATA_KEY_LAST_EXPERIENCE = ":experience";

    public static final String DATA_KEY_LAST_UPDATE_TIME = ":last_update_time";

    public static final String DATA_KEY_JD = "jd:";

    public static final String REDIS_DATA_TYPE_TALENT = "TALENT";

    public static final String DATA_KEY_FACE_IMAGE_DATA =":face_image_data";

    public static final String DATA_KEY_FACE_RECOGNITION_STATUS = ":face_recognition_status";



    // esfiller sync jobs and talents

    public static final String ESFILLER = "esfiller";

    public static final String SYNC_JOBS_TO_ES = ":sync_jobs_to_es";

    public static final String CONVERT_JOBS = ":convert_jobs";

    public static final String SYNC_TALENTS_TO_ES = ":sync_talents_to_es";

    public static final String CONVERT_TALENTS = ":convert_talents";

    public static final String SYNCED = ":synced:";

    public static final String CONVERTED = ":converted:";

    public static final String ERROR = ":error:";

    public static final String CODE = ":code";

    public static final String BODY = ":body";

    // 根据excel创建talent的任务队列
    public static final String CREATE_TALENT_BY_EXCEL_QUEUE = "create_talent_by_excel_queue_sing";

    // 用于保证多实例的情况下只有一个实例执行
    public static final String CREATE_TALENT_BY_EXCEL_NODE_QUEUE = "create_talent_by_excel_node_queue_sing";

    // 根据excel创建talent进度的redis key
    public static final String CREATE_TALENT_BY_EXCEL_PROGRESS = "create_talent_by_excel_progress_sing:";

    public static final String TALENT_EXCEL_SQL_RETRY_FLAG = "talent_excel_sql_retry_flag_sing:";

    
    public static final String DATA_KEY_METADATA = ":metadata";

    public static final String METADATA_KEY_TOKEN = "token";

    public static final String METADATA_KEY_CONTENT_TYPE = "contentType";

    public static final String METADATA_KEY_STATUS = "status";

    public static final String METADATA_KEY_FILENAME = "fileName";

    public static final String METADATA_KEY_TOTAL = "total";

    public static final String METADATA_KEY_START_ROW_INDEX = "startRowIndex";

    public static final String METADATA_KEY_CURRENTINDEX = "currentIndex";

    public static final String METADATA_KEY_TASK_START_DATE = "taskStartDate";

    public static final String DATA_KEY_EXCEL_PARSER = "parser:address_list:";

    public static final String DATA_KEY_EXCEL_PARSER_TOTAL_ROW = "total_rows";

    public static final String DATA_KEY_EXCEL_PARSER_START_ROW_INDEX = "start_row_index";

    public static final String DATA_KEY_SUCCESS_DATA = ":success_data";

    public static final String RELATE_JOB_FOLDER_LOCK_ = "RELATE_JOB_FOLDER_LOCK:";

    /**
     * Social Profile
     */
    public static final String DATA_KEY_LINKEDIN_REQUEST = "APN:SocialProfile:LINKEDIN:%s:request";
    public static final String DATA_KEY_LINKEDIN_STATUS = "APN:SocialProfile:LINKEDIN:%s:status";
    public static final String DATA_KEY_LINKEDIN_RESULT = "APN:SocialProfile:LINKEDIN:%s:result";

    public static final int REDIS_EXPIRE_TIME = 3600 * 360 * 2; //360 hours

    public static String esfillerJobSyncedKey(Integer partitionId) {
        return ESFILLER + SYNC_JOBS_TO_ES + SYNCED + partitionId;
    }

    public static String esfillerJobConvertedKey(Integer partitionId) {
        return ESFILLER + CONVERT_JOBS + CONVERTED + partitionId;
    }

    public static String esfillerTalentSyncedKey(Integer partitionId) {
        return ESFILLER + SYNC_TALENTS_TO_ES + SYNCED + partitionId;
    }

    public static String esfillerTalentConvertedKey(Integer partitionId) {
        return ESFILLER + CONVERT_TALENTS + CONVERTED + partitionId;
    }
}
