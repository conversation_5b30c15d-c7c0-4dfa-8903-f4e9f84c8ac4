package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.domain.permission.PermissionRolePrivilege;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRolePrivilegeVM;

import java.util.Set;

public interface PermissionRolePrivilegeService {

    PermissionRolePrivilege create(PermissionRolePrivilege rolePrivilege);

    //RolePrivilegeVM create(RolePrivilegeVM rolePrivilegeVM);

    Set<String> update(PermissionRolePrivilegeVM permissionRolePrivilegeVM);

    PermissionRolePrivilege update(PermissionRolePrivilege rolePrivilege);

    PermissionRolePrivilege get(Long id);

    void delete(Long id);
}
