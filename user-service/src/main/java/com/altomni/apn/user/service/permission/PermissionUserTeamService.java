package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamJobAndUserCountVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferPrimaryVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferSecondaryVM;

import java.util.Set;

public interface PermissionUserTeamService {

    void addUsersToTeam(Set<Long> userIds, Long teamId);

    void removeUsersFromTeam(Set<Long> userIds, Long teamId);

    void primaryTeamUsersTransfer(PermissionTeamUsersTransferPrimaryVM transferVM);

    void secondaryTeamUsersTransfer(PermissionTeamUsersTransferSecondaryVM transferVM);

    PermissionUserTeam findPrimaryTeamByUserId(Long userId);

    PermissionTeamJobAndUserCountVM searchJobAndUserByTeamId(Long teamId);

    Long getSuperiorLeader(Long userId);

    Set<Long> getAllUpperTeamLeaders(Long userId);
}
