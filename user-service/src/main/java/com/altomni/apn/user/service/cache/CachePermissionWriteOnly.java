package com.altomni.apn.user.service.cache;

import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.service.cache.CommonRedisService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CachePermissionWriteOnly {

    @Resource
    private CommonRedisService commonRedisService;

    @CacheEvict(cacheNames = {"Security:privilege-permission-tree"}, key = "'user:' + #userId")
    public void deletePrivilegePermissionTreeByUserId(Long userId){

    }

    @CacheEvict(cacheNames = {"Security:privilege-permission-set"}, key = "'user:' + #userId")
    public void deletePrivilegePermissionSetByUserId(Long userId){

    }

    @CacheEvict(cacheNames = {"Security:privilege-permission-set"}, allEntries = true)
    public void deleteAllPrivilegePermissionSet(){

    }

    @CacheEvict(cacheNames = {"Security:data-permission-rules"}, key = "'tenant:' + #tenantId")
    public void deletePermissionRulesByTenantId(Long tenantId){

    }

    public void deletePrivilegePermissionSetByUserIds(Collection<Long> userIds) {
        List<String> keys = userIds.stream().map(userId -> String.format(RedisConstants.DATA_KEY_PRIVILEGES_SET_USER, userId)).toList();
        commonRedisService.deleteBatch(keys);
    }
    public void deletePrivilegePermissionTreeByUserIds(Collection<Long> userIds) {
        List<String> keys = userIds.stream().map(userId -> String.format(RedisConstants.DATA_KEY_PRIVILEGES_TREE_USER, userId)).toList();
        commonRedisService.deleteBatch(keys);
    }


    //@CacheEvict(cacheNames = {"Security:privilege-APIs"}, key = "'user:' + #userId")
    public void deletePrivilegeByUserId(Long userId){
        String redisKey = String.format(RedisConstants.DATA_KEY_PRIVILEGES_USER, userId);
        commonRedisService.delete(redisKey);
    }

    public void deletePrivilegeByUserIds(Collection<Long> userIds){
        List<String> keys = userIds.stream().map(userId -> String.format(RedisConstants.DATA_KEY_PRIVILEGES_USER, userId)).toList();
        commonRedisService.deleteBatch(keys);
    }


    public void deletePublicApis(){
        commonRedisService.delete(RedisConstants.DATA_KEY_PRIVILEGES_PUBLIC);
    }

    @CacheEvict(cacheNames = {"Security:data-permission"}, key = "'user:' + #userId")
    public void deleteDataPermissionCacheByUserId(Long userId) {
        System.out.println("deleteCacheByUserId: " + userId);
    }

    @CacheEvict(cacheNames = {"Security:data-permission-client-contact"}, key = "'user:' + #userId")
    public void deleteClientContactDataPermissionCacheByUserId(Long userId) {
        System.out.println("deleteClientContactDataPermissionCacheByUserId: " + userId);
    }

    @CacheEvict(cacheNames = {"Security:data-permission-report"}, key = "'user:' + #userId")
    public void deleteReportDataPermissionCacheByUserId(Long userId) {
        System.out.println("deleteReportDataPermissionCacheByUserId: " + userId);
    }

    @CacheEvict(cacheNames = {"Security:data-permission-home-and-calendar"}, key = "'user:' + #userId")
    public void deleteHomeAndCalendarDataPermissionCacheByUserId(Long userId) {
        System.out.println("deleteHomeAndCalendarDataPermissionCacheByUserId: " + userId);
    }

    @CacheEvict(cacheNames = {"Security:data-permission-candidate-pipeline-management"}, key = "'user:' + #userId")
    public void deleteCandidatePipelineManagementPermissionCacheByUserId(Long userId) {
        System.out.println("deleteCandidatePipelineManagementPermissionCacheByUserId: " + userId);
    }

    @CacheEvict(cacheNames = {"Security:data-permission-china-invoicing"}, key = "'user:' + #userId")
    public void deleteChinaInvoicingDataPermissionCacheByUserId(Long userId) {
        System.out.println("deleteChinaInvoicingDataPermissionCacheByUserId: " + userId);
    }
}