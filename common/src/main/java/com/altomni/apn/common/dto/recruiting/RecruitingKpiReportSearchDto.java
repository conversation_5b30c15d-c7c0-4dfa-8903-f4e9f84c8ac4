package com.altomni.apn.common.dto.recruiting;

import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.KpiReportByUserStayedOver;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationAiTalentType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiReportSearchDto implements Serializable {

    private String startDate;

    private String endDate;

    private RecruitingKpiApplicationAiTalentType aiTalentType;

    //事件时间 EVENT(0), 添加时间 ADD(1)
    private RecruitingKpiDateType dateType;

    //groupBy 分组的 list, 根据顺序一次分组
    private List<RecruitingKpiGroupByFieldType> groupByFieldList;

    //application status search all/current
    private RecruitingKpiApplicationStatusType applicationStatusType;

    //teamIdList 和 userIdList 取并集
    private List<Long> teamIdList;

    private List<Long> userIdList;

    private String timezone;

    //在使用定时任务时,需要传入searchUserId和searchTenantId, 而且必须是header 判断内部调用
    private Long searchUserId;

    private Long searchTenantId;

    private SearchKpiJobDto job;

    private SearchKpiCompanyDto company;

    private SearchUserDto user;

    //设置一下私有职位
    private List<Long> privateJobIds;

    private List<Long> allPrivateJobIds;

    private List<Long> permissionTeamIdList;

    private TeamDataPermissionRespDTO permissionRespDTO;

    private List<Long> companyIdList;

    private boolean isXxlJobFlag = false;

    private boolean isE5ReportFlag = false;

    private StageKpiReportDto jobCountPositions;

    private List<StageKpiReportDto> talentStageList;

    private List<StageKpiReportDto> notesStageList;

    private List<KpiReportByUserStayedOver> stayedOverList;

    public String getStartDateUtc() {
        return getUtcByTimeZone(startDate + " 00:00:00");
    }

    public String getEndDateUtc() {
        return getUtcByTimeZone(endDate + " 23:59:59");
    }

    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).toString();
    }

    public boolean searchUserNotEmpty() {
        return user != null && user.isNotEmpty();
    }
}
