package com.altomni.apn.company.service.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.ContactTypeConstants;
import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import com.altomni.apn.common.domain.dict.TalentIndustryRelation;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.*;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.enums.EnumCompanyContactTagService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.constants.Constants;
import com.altomni.apn.company.domain.business.*;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.altomni.apn.company.domain.talent.*;
import com.altomni.apn.company.domain.vm.CompanyContactVM;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.domain.vm.TalentCompanyRelatedVM;
import com.altomni.apn.company.repository.application.ApplicationServiceRepository;
import com.altomni.apn.company.repository.business.*;
import com.altomni.apn.company.repository.company.CompanyAddtionalInfoRepository;
import com.altomni.apn.company.repository.company.CompanyIndustryRelationRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.location.CompanyLocationRepository;
import com.altomni.apn.company.repository.talent.*;
import com.altomni.apn.company.repository.user.CreditTransactionCompanyBriefRepository;
import com.altomni.apn.company.service.application.ApplicationClient;
import com.altomni.apn.company.service.business.SalesLeadClientContactService;
import com.altomni.apn.company.service.dto.contact.CompanyContactCheckDuplicatedTalentDTO;
import com.altomni.apn.company.service.dto.contact.CompanyContactSearchDTO;
import com.altomni.apn.company.service.dto.contact.CompanyContactTenantSearchDTO;
import com.altomni.apn.company.service.dto.contact.SalesLeadClientContactMigrateDTO;
import com.altomni.apn.company.service.dto.salesLead.AccountBusinessNameDTO;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.service.email.MailService;
import com.altomni.apn.company.service.jobdiva.HrJobdivaClient;
import com.altomni.apn.company.service.jobdiva.JobdivaClient;
import com.altomni.apn.company.service.talent.TalentService;
import com.altomni.apn.company.service.user.AuthorityService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.business.AccountBusinessNameBriefVO;
import com.altomni.apn.company.vo.company.TagVO;
import com.altomni.apn.company.vo.contact.CompanyContactCheckDataExistVO;
import com.altomni.apn.company.vo.contact.CompanyContactVO;
import com.altomni.apn.company.vo.contact.ContactOwnershipVO;
import com.altomni.apn.company.vo.contact.SalesLeadClientContactVO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfile;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfileContactInfoDTO;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.repository.user.CreditTransactionRepository;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.domain.enumeration.ContactType.*;
import static com.altomni.apn.common.domain.enumeration.TalentContactStatus.AVAILABLE;
import static com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType.SHARE;
import static com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType.TALENT_OWNER;

@Service
@Slf4j
public class SalesLeadClientContactServiceImpl implements SalesLeadClientContactService {

    @Resource
    private UserService userService;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private AccountBusinessContactRelationRepository accountBusinessContactRelationRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private JobdivaClient jobdivaClient;

    @Resource
    private AuthorityService authorityService;

    @Resource
    private HrJobdivaClient hrJobdivaClient;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private MailService mailService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private CompanyLocationRepository companyLocationRepository;

    @Resource
    private TalentService talentService;

    @Resource
    private TalentCompanyBriefRepository talentCompanyBriefRepository;

    @Resource
    private TalentCompanySyncBriefRepository talentCompanySyncBriefRepository;

    @Resource
    private TalentContactCompanyBriefRepository talentContactCompanyBriefRepository;

    @Resource
    private TalentContactSyncBriefRepository talentContactSyncBriefRepository;

    @Resource
    private TalentCurrentLocationCompanyBriefRepository talentCurrentLocationCompanyBriefRepository;

    @Resource
    private TalentCurrentLocationSyncBriefRepository talentCurrentLocationSyncBriefRepository;

    @Resource
    private TalentServiceRepository talentServiceRepository;

    @Resource
    public SalesLeadClientContactAddtionalInfoRepository salesLeadClientContactAddtionalInfoRepository;

    @Resource
    private CompanyAddtionalInfoRepository companyAddtionalInfoRepository;

    @Resource
    private EsFillerCompanyService esFillerCompanyService;

    @Resource
    private EnumCompanyContactTagService enumCompanyContactTagService;

    @Resource
    private ContactTagRelationRepository contactTagRelationRepository;

    @Resource
    private ContactServiceTypeRelationRepository contactServiceTypeRelationRepository;

    @Resource
    private CompanyIndustryRelationRepository companyIndustryRelationRepository;

    @Resource
    private TalentIndustryRelationCompanyBriefRepository talentIndustryRelationCompanyBriefRepository;

    @Resource
    private ApplicationClient applicationService;

    @Resource
    private ApplicationServiceRepository applicationServiceRepository;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private TalentOwnershipCompanyBriefRepository talentOwnershipCompanyBriefRepository;

    @Resource
    private TalentContactOwnershipBriefRepository talentContactOwnershipBriefRepository;

    @Resource
    private CreditTransactionCompanyBriefRepository creditTransactionCompanyBriefRepository;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    public static final String TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD = "TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD";

    private final String KEY_EXPERIENCES = "experiences";

    private final String KEY_TITLE = "title";

    private final String KEY_DEPARTMENT = "department";

    private final String CRM_CONTACT = "contacts";

    private final String CLIENT_CONTACT = "clientContacts";

    private final String CONTACT_INFO = "contactInfo";

    private final String CONTACT_LOCATION = "contactLocation";

    private final String CONTACT_OWNER = "contactOwners";

    private final String CONTACT_SHARE_USER = "contactShareUsers";

    private final String CONTACT_SHARE_TENANT = "contactShareTenant";

    private final String CONTACT_BUSINESS_RELATION = "contactBusinessRelations";

    private final String CONTACT_SERVICE_RELATION = "contactServiceRelation";


    private final String INDUSTRY = "industries";

    private final String CONTACT_TAGS = "contactTags";

    private final String CRM_CONTACT_URL = "/contact/api/v1/contact-apn";

    private final List<ContactType> checkContactTypes = Arrays.asList(EMAIL, PHONE, WHATSAPP, WECHAT, LINKEDIN, MAIMAI);

    private final List<ContactType> crossCheckContactTypes = Arrays.asList(PHONE, WHATSAPP, WECHAT);

    private final List<TalentOwnershipType> checkTalentOwners = Arrays.asList(SHARE, TALENT_OWNER);

    @Bean
    public PasswordEncoder getPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    public static String crmContactTagsUrl(Long contactId, Long companyId) {
        return String.format("/contact/api/v1/contact/%s/account-company/%s/tags/label", contactId, companyId);
    }

    private boolean compareContacts(List<TalentContactDTO> userInput, List<TalentContactDTO> es) {
        if (CollectionUtils.isEmpty(userInput)) {
            return false;
        }
        if (CollectionUtils.isEmpty(es)) {
            return true;
        }
        if (userInput.size() < es.size()) {
            return false;
        }
        if (userInput.size() > es.size()) {
            return true;
        }
        Set<String> set = new HashSet<>();
        for (TalentContactDTO contactDTO : userInput) {
            set.add(contactDTO.getTypeAndContact());
        }
        for (TalentContactDTO contactDTO : es) {
            if (!set.contains(contactDTO.getTypeAndContact())) {
                return true;
            }
        }
        return false;
    }

    private void updateCreditTalentIdForCommonPool(Long talentId, Long creditTransactionId) {
        CreditTransactionDTO creditTransactionDTO = new CreditTransactionDTO();
        creditTransactionDTO.setId(creditTransactionId);
        creditTransactionDTO.setTalentId(talentId);
        userService.updateCreditTalentIdForCommonPool(creditTransactionDTO);
    }


    public boolean updateTalentContactOnlyPhone(List<TalentContactDTO> contacts, Long talentId) {
        List<TalentContact> existContact = talentContactCompanyBriefRepository.findAllByTalentId(talentId);
        return updateTalentContact(contacts, existContact.stream().filter(c -> c.getType() == ContactType.PHONE && !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())).collect(Collectors.toList()), talentId);
    }

    public boolean updateTalentContact(List<TalentContactDTO> contacts, Long talentId) {
        List<TalentContact> existContact = talentContactCompanyBriefRepository.findAllByTalentId(talentId);
        checkExistApproverEmail(existContact, contacts);
        return updateTalentContact(contacts, existContact, talentId);
    }

    private void checkExistApproverEmail(List<TalentContact> existContacts, List<TalentContactDTO> updateContacts) {
        Set<String> emailSet = updateContacts.stream().filter(t -> t.getType().equals(EMAIL)).map(TalentContactDTO::getContact).collect(Collectors.toSet());
        // 检查更新部分contactList
        Set<String> contactList = existContacts.stream().filter(t -> t.getType().equals(EMAIL) && t.getStatus().equals(AVAILABLE)).map(TalentContact::getContact).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(contactList)) {
            contactList.removeAll(emailSet);
            List<TimeSheetUserDTO> timeSheetUserList = jobdivaClient.findByUsernameOrEmailList(contactList).getBody();
            if (CollUtil.isNotEmpty(timeSheetUserList)) {
                throw new CustomParameterizedException("Cannot modify approver's email.");
            }
        }

    }

    public boolean updateTalentContact(List<TalentContactDTO> contacts, List<TalentContact> existContact, Long talentId) {
        List<TalentContact> update2Active = existContact.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus()) && equalTalentContactFromDTO(contacts, c)).collect(Collectors.toList());
        update2Active.forEach(c -> contacts.forEach(ic -> {
            if (c.getType() == ic.getType() && org.apache.commons.lang3.StringUtils.equals(c.getContact(), ic.getContact())) {
                c.setSort(ic.getSort());
            }
        }));
        updateTalentContactStatus(update2Active, TalentContactStatus.AVAILABLE);
        List<TalentContact> update2Invalid = existContact.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus()) && !equalTalentContactFromDTO(contacts, c) && TalentContactStatus.AVAILABLE == c.getStatus()).collect(Collectors.toList());
        updateTalentContactStatus(update2Invalid, TalentContactStatus.INVALID);
        List<TalentContactDTO> addContact = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus()) && !equalTalentContactFromDB(existContact, c)).collect(Collectors.toList());

        //talentContacts
        if (CollUtil.isNotEmpty(addContact)) {
            List<TalentContactDTO> contactSet = new ArrayList<>();
            for (TalentContactDTO t : addContact.stream().filter(c ->
                    ContactTypeConstants.COMPREHENSIVE_CONTACT_TYPES.contains(c.getType())).collect(Collectors.toList())) {
                t.setTenantId(SecurityUtils.getTenantId());
                t.setTalentId(talentId);
                t.setStatus(TalentContactStatus.AVAILABLE);
                t.setId(null);
                contactSet.add(t);
            }
            talentContactCompanyBriefRepository.saveAllAndFlush(Convert.toList(TalentContact.class, contactSet));
        }
        return CollUtil.isNotEmpty(addContact);

    }

    private boolean equalTalentContactFromDB(List<TalentContact> talentContactList, TalentContactDTO talentContactDTO) {
        if (talentContactList == null || talentContactList.isEmpty()) {
            return false;
        }

        if (talentContactDTO == null) {
            return false;
        }
        for (TalentContact talentContact : talentContactList) {
            if (talentContact.getType() == talentContactDTO.getType() && org.apache.commons.lang3.StringUtils.equals(talentContact.getContact(), talentContactDTO.getContact()) && org.apache.commons.lang3.StringUtils.equals(talentContact.getDetails(), talentContactDTO.getDetails())) {
                return true;
            }
        }
        return false;
    }

    private void updateTalentContactStatus(List<TalentContact> updateContact, TalentContactStatus talentContactStatus) {
        for (TalentContact talentContact : updateContact) {
            talentContact.setStatus(talentContactStatus);
        }
        talentContactCompanyBriefRepository.saveAll(updateContact);
    }

    private boolean equalTalentContactFromDTO(List<TalentContactDTO> talentContactDTOList, TalentContact talentContact) {
        if (talentContactDTOList == null || talentContactDTOList.isEmpty()) {
            return false;
        }

        if (talentContact == null) {
            return false;
        }
        for (TalentContactDTO talentContactDTO : talentContactDTOList) {
            if (talentContactDTO.getType() == talentContact.getType() && org.apache.commons.lang3.StringUtils.equals(talentContactDTO.getContact(), talentContact.getContact()) && org.apache.commons.lang3.StringUtils.equals(talentContactDTO.getDetails(), talentContact.getDetails())) {
                return true;
            }
        }
        return false;
    }

    private void saveTalentIndustries(Long talentId, List<CompanyIndustryRelation> companyIndustryRelationList) {
        if (CollUtil.isEmpty(companyIndustryRelationList)) {
            return;
        }
        List<TalentIndustryRelation> talentIndustryRelationList = talentIndustryRelationCompanyBriefRepository.findAllByTalentId(talentId);

        if (CollUtil.isNotEmpty(talentIndustryRelationList)) {
            return;
        }


        talentIndustryRelationList = companyIndustryRelationList.stream().map(o -> {
            TalentIndustryRelation talentIndustryRelation = new TalentIndustryRelation();
            talentIndustryRelation.setTalentId(talentId);
            talentIndustryRelation.setEnumId(o.getEnumId());
            return talentIndustryRelation;
        }).collect(Collectors.toList());

        talentIndustryRelationCompanyBriefRepository.saveAllAndFlush(talentIndustryRelationList);

    }

    private List<TagVO> getContactTags(Long companyId, Long contactId, HttpHeaders headers) {
        List<ContactTagRelation> existContactTagRelationList = contactTagRelationRepository.findAllByAccountCompanyIdAndContactId(companyId, contactId);
        if (CollUtil.isEmpty(existContactTagRelationList)) {
            return new ArrayList<>();
        }
        List<String> crmContactTags = new ArrayList<>();
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", headers.getFirst(HttpHeaders.AUTHORIZATION));
            httpHeaders.set("Content-Type", "application/json");
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + crmContactTagsUrl(contactId, companyId), convertToOkHttpHeaders(httpHeaders));
            if (response != null && 200 == response.getCode()) {
                crmContactTags = JSONUtil.toList(JSONUtil.parseArray(response.getBody()), String.class);
            } else {
                throw new CustomParameterizedException(response.getBody());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("query crm contact tags error: {}", e.getMessage());
        }
        log.info("crmContactTags: {}", JSONUtil.toJsonStr(crmContactTags));
        final Set<String> authorizedTags = new HashSet<>(crmContactTags);
        return existContactTagRelationList.stream().map(o -> new TagVO(o.getTag(), o.getPermissionUserId(), authorizedTags.contains(o.getTag()))).collect(Collectors.toList());
    }

//    private void clientContactDetection(SalesLeadClientContact contact, List<String> emails, String fullName) {
//        if (CollUtil.isEmpty(emails) || ObjectUtil.isEmpty(fullName)) {
//            return;
//        }
//        if (contact.getApproverId() != null) {
//            TimeSheetUser timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.getTimeSheetUserById(contact.getApproverId()).getBody());
//            if (timeSheetUser != null) {
//
//                String email = emails.get(0);
//                TimeSheetUser timeSheetUserNew = Convert.convert(TimeSheetUser.class, jobdivaClient.findByUsernameOrEmail(email).getBody());
//                if (timeSheetUserNew != null && !timeSheetUserNew.getId().equals(timeSheetUser.getId())) {
//                    return;
//                }
//                if (contact.isActive()) {
//                    if (timeSheetUser.isActivated() && !timeSheetUser.getEmail().equals(email)) {
//                        timeSheetUser.setUsername(email);
//                        timeSheetUser.setEmail(email);
//                        sendEmailChange(email, fullName);
//                        authorityService.logoutByUid(timeSheetUser.getUid());
//                    }
//                } else {
//                    timeSheetUser.setActivated(false);
//                    authorityService.logoutByUid(timeSheetUser.getUid());
//                }
//                jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
//            }
//        }
//        if (!contact.isActive()) {
//            contact.setInactived(true);
//            contact.setReceiveEmail(false);
//        }
//    }

    private void updateContact(Long talentId, List<TalentContactDTO> contacts, List<String> linkedinProfile) {
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdAndStatusOrderBySortAsc(talentId, TalentContactStatus.AVAILABLE);
        List<TalentContact> addContactList = contacts.stream().filter(o -> ObjectUtil.isEmpty(o.getId())).map(item -> {
            if (ContactType.EMAIL.equals(item.getType()) || ContactType.PHONE.equals(item.getType())) {
                return new TalentContact(item.getType(), item.getContact(), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 0);
            } else {
                return new TalentContact(item.getType(), item.getContact(), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1);
            }
        }).collect(Collectors.toList());
        Set<Long> updateContactIds = contacts.stream().map(TalentContactDTO::getId).collect(Collectors.toSet());
        List<TalentContact> invalidContactList = talentContactList.stream().filter(o -> !ContactType.LINKEDIN.equals(o.getType()) && !updateContactIds.contains(o.getId())).collect(Collectors.toList());
        talentContactList.removeAll(invalidContactList);

        Map<Long, TalentContactDTO> contactDTOMap = contacts.stream().filter(o -> ObjectUtil.isNotEmpty(o.getId())).collect(Collectors.toMap(TalentContactDTO::getId, o -> o));

        talentContactList.forEach(o -> {
            if (contactDTOMap.containsKey(o.getId())) {
                o.setContact(contactDTOMap.get(o.getId()).getContact());
            }
        });


        if (CollUtil.isNotEmpty(linkedinProfile)) {
            List<TalentContact> contactList = talentContactList.stream().filter(o -> ContactType.LINKEDIN.equals(o.getType())).collect(Collectors.toList());
            linkedinProfile = linkedinProfile.stream().filter(o -> getLinkedinContact(o) != null).collect(Collectors.toList());

            for (int i = 0; i < Math.min(contactList.size(), linkedinProfile.size()); i++) {
                contactList.get(i).setContact(getLinkedinContact(linkedinProfile.get(i)));
                contactList.get(i).setDetails(linkedinProfile.get(i));
            }

            if (contactList.size() < linkedinProfile.size()) {
                for (int i = contactList.size(); i < linkedinProfile.size(); i++) {
                    TalentContact talentContact = new TalentContact(ContactType.LINKEDIN, getLinkedinContact(linkedinProfile.get(i)), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1);
                    talentContact.setDetails(linkedinProfile.get(i));
                    addContactList.add(talentContact);
                }
            } else if (contactList.size() > linkedinProfile.size()) {
                for (int i = linkedinProfile.size(); i < contactList.size(); i++) {
                    invalidContactList.add(contactList.get(i));
                }
            }
        } else {
            invalidContactList.addAll(talentContactList.stream().filter(o -> ContactType.LINKEDIN.equals(o.getType())).collect(Collectors.toList()));
        }

        talentContactList.removeAll(invalidContactList);

        invalidContactList.forEach(o -> o.setStatus(TalentContactStatus.INVALID));

        talentContactCompanyBriefRepository.saveAll(invalidContactList);
        talentContactCompanyBriefRepository.saveAll(talentContactList);
        talentContactCompanyBriefRepository.saveAll(addContactList);
    }

    private void saveContactAdditionalInfo(SalesLeadClientContact salesLeadClientContact, SalesLeadClientContactDTO salesLeadClientContactDTO) {
        SalesLeadClientContactDTO additionalInfo = new SalesLeadClientContactDTO();
        ServiceUtils.myCopyProperties(salesLeadClientContactDTO, additionalInfo, SalesLeadClientContactDTO.additionalInfoSkipProperties);
        if (JSONUtil.parseObj(JsonUtil.toJson(additionalInfo)).isEmpty()) {
            return;
        }
        SalesLeadClientContactAdditionalInfo salesLeadClientContactAdditionalInfo = new SalesLeadClientContactAdditionalInfo();
        salesLeadClientContactAdditionalInfo.setCompanyContactId(salesLeadClientContact.getId());
        salesLeadClientContactAdditionalInfo.setExtendedInfo(JsonUtil.toJson(additionalInfo));
        salesLeadClientContactAddtionalInfoRepository.save(salesLeadClientContactAdditionalInfo);
    }


    @Override
    public TalentDTOV3 verifiedContact(CrmContactDTO crmContactDTO) {
        if (ObjectUtil.isEmpty(crmContactDTO.getTenantId())) {
            throw new CustomParameterizedException("tenantId must not be empty.");
        }
        if (ObjectUtil.isEmpty(crmContactDTO.getCrmUserId())) {
            throw new CustomParameterizedException("crmOperator must not be empty.");
        }
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        List<Long> talentIds = checkTalentContactInformation(crmContactDTO.getTenantId(), crmContactDTO.getContacts());
        List<TalentCompanyBrief> talentCompanyBriefList = talentCompanyBriefRepository.findAllById(talentIds);
        if (CollUtil.isNotEmpty(talentCompanyBriefList)) {
            if (talentCompanyBriefList.size() > 1) {
                throw new CustomParameterizedException("There are multiple similar candidates in the APN, name: " + String.join(",", talentCompanyBriefList.stream().map(TalentCompanyBrief::getFullName).toList()));
            }
            TalentCompanyBrief talentCompanyBrief = talentCompanyBriefList.get(0);
            if (ObjectUtil.isEmpty(crmContactDTO.getApnTalentId()) && (!talentCompanyBrief.getFirstName().equalsIgnoreCase(crmContactDTO.getFirstName()) || !talentCompanyBrief.getLastName().equalsIgnoreCase(crmContactDTO.getLastName()))) {
                throw new CustomParameterizedException("APN has similar candidates, but the names are different, name: " + String.join(",", talentCompanyBriefList.stream().map(TalentCompanyBrief::getFullName).toList()));
            }
            //如果联系人已经关联了候选人再次编辑,查到的联系方式和其他候选人相同
            if (ObjectUtil.isNotEmpty(crmContactDTO.getApnTalentId()) && !crmContactDTO.getApnTalentId().equals(talentCompanyBrief.getId())) {
                throw new CustomParameterizedException("APN has multiple contacts, contact information conflict, name: " + String.join(",", talentCompanyBriefList.stream().map(TalentCompanyBrief::getFullName).toList()));
            }

            //CRM联系人会通过模拟登陆使用查重接口，需要判断候选人是否能被联系人关联，参数接口crm userId并转为 apn userId并用于锁定期判断
            Long operator = crmContactDTO.getCrmUserId();
            List<TalentOwnership> talentOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdAndOwnershipTypeIn(talentCompanyBrief.getId(), checkTalentOwners);
            Set<Long> ownerIds = talentOwnershipList.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet());
            //判断候选人已存在的owner，如果owner没有CRM账号就不允许同步
            if (CollUtil.isNotEmpty(ownerIds)) {
//                Integer crmUserCount = userCrmRelationRepository.countDistinctByUserIdIn(ownerIds.stream().toList());
//                if (ownerIds.size() > crmUserCount) {
//                    throw new CustomParameterizedException("There are similar candidates in APN, and the owner of the candidate does not have a CRM account.");
//                }
            }
            ownerIds.add(talentCompanyBrief.getPermissionUserId());
            //查走了流程的候选人
            List<EntityCountVM> applicationCountList = applicationServiceRepository.findApplicationCountByTalentIds(talentIds);
            Set<Long> existedApplicationTalentIds = applicationCountList.stream().map(EntityCountVM::getId).collect(Collectors.toSet());
            //查已经关联联系人的候选人
//            List<Long> relatedTalentId = salesLeadClientContactRepository.findTalentIdByTenantIdAndTalentIdIn(crmContactDTO.getTenantId(), talentIds);
            //查联系方式
            List<TalentContact> allTalentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndStatus(talentIds, AVAILABLE);
//            List<TalentContact> talentContactList = allTalentContactList.stream().filter(o -> checkContactTypes.contains(o.getType())).toList();

            if ((!ownerIds.contains(operator) && Instant.now().isBefore(talentCompanyBrief.getCreatedDate().plus(3, ChronoUnit.DAYS))) || existedApplicationTalentIds.contains(talentCompanyBrief.getId())) {
                //在流程中或者锁定期内需要判断联系方式
                List<String> existedContacts = allTalentContactList.stream().map(TalentContact::getTypeAndContact).toList();
                List<String> updateContacts = crmContactDTO.getContacts().stream().map(o -> ContactType.fromDbValue(o.getType()) + o.getContact()).toList();
                JSONObject additionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
                List<TalentExperienceDTO> talentExperienceDTOList = additionalInfo.containsKey(KEY_EXPERIENCES) ? JSONUtil.toList(additionalInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class) : new ArrayList<>();
                Set<String> existedExperiences = talentExperienceDTOList.stream().map(o -> o.getCompanyName() + "@*" + o.getTitle()).collect(Collectors.toSet());
                //联系方式 & company & title & name不完全相同，Crm存在仅编辑联系人基本信息的情况，不会涉及companyName等数据更新
                if ((!talentCompanyBrief.getFirstName().equalsIgnoreCase(crmContactDTO.getFirstName()) || !talentCompanyBrief.getLastName().equalsIgnoreCase(crmContactDTO.getLastName())) || !CollUtil.containsAll(existedContacts, updateContacts) || (ObjectUtil.isNotEmpty(crmContactDTO.getAccountCompanyFullBusinessName()) && !existedExperiences.contains(crmContactDTO.getAccountCompanyFullBusinessName() + "@*" + crmContactDTO.getTitle()))) {
                    throw new CustomParameterizedException("There are similar candidates in the APN and the candidate is in the lock-in period or process, name: " + String.join(",", talentCompanyBriefList.stream().map(TalentCompanyBrief::getFullName).toList()));
                }
            }
            ServiceUtils.myCopyProperties(talentCompanyBrief, talentDTOV3);
            return talentDTOV3;
        }
        return talentDTOV3;
    }

    @Override
    public List<TalentDTOV3> verifiedContactWithDetail(CrmContactDTO crmContactDTO) {
        if (ObjectUtil.isEmpty(crmContactDTO.getTenantId())) {
            throw new CustomParameterizedException("TenantId must not be empty.");
        }
        if (ObjectUtil.isEmpty(crmContactDTO.getCrmUserId())) {
            throw new CustomParameterizedException("crmOperator must not be empty.");
        }
        List<TalentDTOV3> result = new ArrayList<>();
        List<Long> talentIds = checkTalentContactInformation(crmContactDTO.getTenantId(), crmContactDTO.getContacts());
        List<TalentCompanyBrief> talentCompanyBriefList = talentCompanyBriefRepository.findAllById(talentIds);
        if (CollUtil.isNotEmpty(talentCompanyBriefList)) {
            //从common pool转换的联系人不能和候选人有重复
            if (!checkCommonPool(crmContactDTO.getEsId(), talentIds, crmContactDTO.getTenantId())) {
                throw new CustomParameterizedException("APN has the same candidates. id: " + talentCompanyBriefList.get(0).getId());
            }
            //如果联系人已经关联了候选人再次编辑,查到的联系方式和其他候选人相同
            if (ObjectUtil.isNotEmpty(crmContactDTO.getApnTalentId()) && (talentCompanyBriefList.size() > 1 || !crmContactDTO.getApnTalentId().equals(talentCompanyBriefList.get(0).getId()))) {
                log.error("contact talentId: {}, verified talent: {}", crmContactDTO.getApnTalentId(), talentCompanyBriefList);
                throw new CustomParameterizedException("APN has multiple contacts, contact information conflict.");
            }
            if (talentCompanyBriefList.size() > 1) {
                List<TalentDTOV3> talentDTOV3List = talentService.findTalentsBasicByIds(talentIds).getBody();
                throw new WithDataException("There are multiple similar candidates in the APN.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
            }
            TalentCompanyBrief talentCompanyBrief = talentCompanyBriefList.get(0);
            if (ObjectUtil.isEmpty(crmContactDTO.getApnTalentId()) && (!talentCompanyBrief.getFirstName().equalsIgnoreCase(crmContactDTO.getFirstName()) || !talentCompanyBrief.getLastName().equalsIgnoreCase(crmContactDTO.getLastName()))) {
                List<TalentDTOV3> talentDTOV3List = talentService.findTalentsBasicByIds(talentIds).getBody();
                throw new WithDataException("APN has similar candidates, but the names are different.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
            }

            //CRM联系人会通过模拟登陆使用查重接口，需要判断候选人是否能被联系人关联，参数接口crm userId并转为 apn userId并用于锁定期判断
            Long operator = crmContactDTO.getCrmUserId();
            List<TalentOwnership> talentOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdAndOwnershipTypeIn(talentCompanyBrief.getId(), checkTalentOwners);
            Set<Long> ownerIds = talentOwnershipList.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet());
            //判断候选人已存在的owner，如果owner没有CRM账号就不允许同步
            if (CollUtil.isNotEmpty(ownerIds)) {
//                Integer crmUserCount = userCrmRelationRepository.countDistinctByUserIdIn(ownerIds.stream().toList());
//                if (ownerIds.size() > crmUserCount) {
//                    throw new CustomParameterizedException("There are similar candidates in APN, and the owner of the candidate does not have a CRM account.");
//                }
            }
            ownerIds.add(talentCompanyBrief.getPermissionUserId());
            //查走了流程的候选人
            List<EntityCountVM> applicationCountList = applicationServiceRepository.findApplicationCountByTalentIds(talentIds);
            Set<Long> existedApplicationTalentIds = applicationCountList.stream().map(EntityCountVM::getId).collect(Collectors.toSet());
            //查已经关联联系人的候选人
//            List<Long> relatedTalentId = salesLeadClientContactRepository.findTalentIdByTenantIdAndTalentIdIn(crmContactDTO.getTenantId(), talentIds);
            //查联系方式
            List<TalentContact> allTalentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndStatus(talentIds, AVAILABLE);
//            List<TalentContact> talentContactList = allTalentContactList.stream().filter(o -> checkContactTypes.contains(o.getType())).toList();
            List<String> existedContacts = allTalentContactList.stream().map(TalentContact::getTypeAndContact).toList();

            //校验已同步apn联系人的已存在联系方式是否完全包含关联的候选人联系方式，不完全包含需要用户补充候选人联系方式
            if (CollUtil.isNotEmpty(crmContactDTO.getVerifiedContacts())) {
                List<String> verifiedContacts = crmContactDTO.getVerifiedContacts().stream().map(o -> ContactType.fromDbValue(o.getType()) + o.getContact()).toList();
                if (!verifiedContacts.containsAll(existedContacts)) {
                    List<TalentDTOV3> talentDTOV3List = talentService.findTalentsBasicByIds(talentIds).getBody();
                    throw new WithDataException("There are similar candidates in APN, but the candidate contact information is different, please add candidates contact information first.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
                }
            }
            List<String> updateContacts = crmContactDTO.getContacts().stream().map(o -> ContactType.fromDbValue(o.getType()) + o.getContact()).toList();
            //未同步apn联系人初次同步apn要求必须包含候选人全部的联系方式
            if (ObjectUtil.isEmpty(crmContactDTO.getApnTalentId()) && !updateContacts.containsAll(existedContacts)) {
                List<TalentDTOV3> talentDTOV3List = talentService.findTalentsBasicByIds(talentIds).getBody();
                throw new WithDataException("There are similar candidates in APN, but the candidate contact information is different.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
            }

            if ((!ownerIds.contains(operator) && Instant.now().isBefore(talentCompanyBrief.getCreatedDate().plus(3, ChronoUnit.DAYS))) || existedApplicationTalentIds.contains(talentCompanyBrief.getId())) {
                //在流程中或者锁定期内需要判断联系方式
                JSONObject additionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
                List<TalentExperienceDTO> talentExperienceDTOList = additionalInfo.containsKey(KEY_EXPERIENCES) ? JSONUtil.toList(additionalInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class) : new ArrayList<>();
                Set<String> existedExperiences = talentExperienceDTOList.stream().map(o -> o.getCompanyName() + "@*" + o.getTitle()).collect(Collectors.toSet());
                //联系方式 & company， title不完全相同，Crm存在仅编辑联系人基本信息的情况，不会涉及companyName等数据更新
                if ((!talentCompanyBrief.getFirstName().equalsIgnoreCase(crmContactDTO.getFirstName()) || !talentCompanyBrief.getLastName().equalsIgnoreCase(crmContactDTO.getLastName())) || !CollUtil.containsAll(existedContacts, updateContacts) || (ObjectUtil.isNotEmpty(crmContactDTO.getAccountCompanyFullBusinessName()) && !existedExperiences.contains(crmContactDTO.getAccountCompanyFullBusinessName() + "@*" + crmContactDTO.getTitle()))) {
                    throw new CustomParameterizedException("There are similar candidates in the APN and the candidate is in the lock-in period or process.");
                }
            }
            TalentDTOV3 talentDTOV3 = new TalentDTOV3();
            ServiceUtils.myCopyProperties(talentCompanyBrief, talentDTOV3);
            result.add(talentDTOV3);
        }
        return result;
    }

    @Resource
    private CreditTransactionRepository creditTransactionRepository;

    private boolean checkCommonPool(String esId, List<Long> talentIds, Long tenantId) {
        if(StringUtils.isEmpty(esId)) {
            return true;
        }
        if(talentIds.isEmpty()) {
            return true;
        }
        CreditTransaction creditTransaction = creditTransactionRepository.findByProfileIdAndTenantIdAndStatus(esId, tenantId, Status.Available);
        if(creditTransaction == null) {
            return true;
        }
        Long talentId = creditTransaction.getTalentId();
        return talentIds.contains(talentId);
    }

    private List<Long> checkTalentContactInformation(Long tenantId, List<ContactInfoDTO> contacts) {
        if (CollUtil.isEmpty(contacts)) {
            return new ArrayList<>();
        }
        List<TalentContactDTO> contactDTOList = contacts.stream().filter(c -> !TalentContactVerificationStatus.WRONG_CONTACT.equals(c.getVerificationStatus())).map(o -> {
            TalentContactDTO talentContactDTO = new TalentContactDTO();
            ServiceUtils.myCopyProperties(o, talentContactDTO);
            talentContactDTO.setType(ContactType.fromDbValue(o.getType()));
            return talentContactDTO;
        }).filter(item -> checkContactTypes.contains(item.getType()) && ObjectUtil.isNotEmpty(item.getContact())).toList();

        if (CollUtil.isEmpty(contactDTOList)) {
            return new ArrayList<>();
        }
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        talentDTOV3.setTenantId(tenantId);
        talentDTOV3.setContacts(contactDTOList);
        Set<Long> allIds = new HashSet<>();
        //需要es查，可以忽略区号查phone
        List<Long> esIds = talentService.searchTalentsIdByContactAndSimilarity(talentDTOV3).getBody();
        List<String> checkContacts = contactDTOList.stream().map(TalentContactDTO::getContact).distinct().toList();

        //先将疑似的联系方式都查出来，后续再区分是否混合查的数据
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTenantIdAndStatusAndTypeInAndContactInAndVerificationStatusIsNot(tenantId, AVAILABLE, checkContactTypes, checkContacts);
        List<Long> mysqlIds = new ArrayList<>();
        //将查重的联系方式分组
        Map<String, List<ContactType>> contactDTOMap = contactDTOList.stream()
                .collect(Collectors.groupingBy(TalentContactDTO::getContact,
                        Collectors.mapping(TalentContactDTO::getType, Collectors.toList())));

        for (TalentContact item : talentContactList) {
            //已经查重到了的候选人不再查重
            if (mysqlIds.contains(item.getTalentId())) {
                continue;
            }
            if (contactDTOMap.containsKey(item.getContact())) {
                List<ContactType> types = contactDTOMap.getOrDefault(item.getContact(), new ArrayList<>());
                //如果查重的联系方式contact & type都相同 或者 是交叉查重到的数据即查重成功
                if (types.contains(item.getType()) || (crossCheckContactTypes.contains(item.getType()) && !CollUtil.intersection(crossCheckContactTypes, types).isEmpty())) {
                    mysqlIds.add(item.getTalentId());
                }
            }
        }

        if (CollUtil.isNotEmpty(esIds)) {
            allIds.addAll(esIds);
        }
        if (CollUtil.isNotEmpty(mysqlIds)) {
            allIds.addAll(mysqlIds);
        }
        return allIds.stream().toList();
    }

    @Override
    public CompanyContactVO createSalesLeadContact(CrmContactDTO crmContactDTO, HttpHeaders headers) {
        if (CollUtil.isEmpty(crmContactDTO.getContacts())) {
            throw new CustomParameterizedException("contacts must be not empty.");
        }
        if (ObjectUtil.isEmpty(crmContactDTO.getAccountCompanyName())) {
            throw new CustomParameterizedException("companyName must be not empty.");
        }
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        talentDTOV3.setContacts(crmContactDTO.getContacts().stream().map(o -> {
            TalentContactDTO talentContactDTO = new TalentContactDTO();
            ServiceUtils.myCopyProperties(o, talentContactDTO);
            talentContactDTO.setType(ContactType.fromDbValue(o.getType()));
            return talentContactDTO;
        }).toList());
        talentDTOV3.setTenantId(SecurityUtils.getTenantId());
        List<TalentDTOV3> talentDTOV3List = talentService.searchTalentsByContact(talentDTOV3).getBody();
        if (CollUtil.isNotEmpty(talentDTOV3List) && talentDTOV3List.size() > 1) {
            throw new WithDataException("There are multiple similar candidates in the APN.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
        }
//        AccountBusiness accountBusiness = accountBusinessRepository.findFirstByAccountCompanyId(crmContactDTO.getAccountCompanyId());
        Set<Long> crmContactIds = new HashSet<>();
        if (CollUtil.isNotEmpty(talentDTOV3List)) {
            TalentDTOV3 existedTalent = talentDTOV3List.get(0);
            if (!crmContactDTO.getFirstName().equalsIgnoreCase(existedTalent.getFirstName()) || !crmContactDTO.getLastName().equalsIgnoreCase(existedTalent.getLastName())) {
                throw new WithDataException("APN has similar candidates, but the names are different.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
            }
            crmContactDTO.setApnTalentId(existedTalent.getId());
//            crmContactDTO.setFirstName(existedTalent.getFirstName());
//            crmContactDTO.setLastName(existedTalent.getLastName());
            TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(existedTalent.getId()).orElseThrow(() -> new CustomParameterizedException("talent does not exist."));
            //查询候选人是否已经是联系人了
            List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllByTalentId(talentCompanyBrief.getId());
            crmContactIds = salesLeadClientContactList.stream().map(SalesLeadClientContact::getCrmContactId).collect(Collectors.toSet());
            //一个候选人映射了多个CRM的联系人抛出异常
            if (crmContactIds.size() > 1) {
                throw new CustomParameterizedException("duplicate apn contacts.");
            }
            List<TalentOwnership> talentOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdAndOwnershipTypeIn(talentCompanyBrief.getId(), checkTalentOwners);
            Set<Long> ownerIds = talentOwnershipList.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet());
            //判断候选人已存在的owner，如果owner没有CRM账号就不允许同步
            if (CollUtil.isNotEmpty(ownerIds)) {
//                Integer crmUserCount = userCrmRelationRepository.countDistinctByUserIdIn(ownerIds.stream().toList());
//                if (ownerIds.size() > crmUserCount) {
//                    throw new CustomParameterizedException("There are similar candidates in APN, and the owner of the candidate does not have a CRM account.");
//                }
            }
            ownerIds.add(talentCompanyBrief.getPermissionUserId());
//            TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationCompanyBriefRepository.findByTalentId(existedTalent.getId());
//            talentCurrentLocationCompanyBrief.setOriginalLoc(JsonUtil.toJson(crmContactDTO.getContactLocation().get(0)));
            List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdAndStatusOrderBySortAsc(existedTalent.getId(), AVAILABLE);
//            Set<String> contacts = talentContactList.stream().map(TalentContact::getTypeAndContact).collect(Collectors.toSet());
//            List<TalentContact> addTalentContactList = crmContactDTO.getContacts().stream().filter(i -> !contacts.contains(ContactType.fromDbValue(i.getType()) + i.getContact())).map(o -> new TalentContact(ContactType.fromDbValue(o.getType()), o.getContact(), existedTalent.getId(), SecurityUtils.getTenantId(), AVAILABLE, EMAIL.equals(ContactType.fromDbValue(o.getType())) || PHONE.equals(ContactType.fromDbValue(o.getType())) ? 0 : 1)).toList();
            List<EntityCountVM> applicationCountList = applicationServiceRepository.findApplicationCountByTalentIds(Arrays.asList(existedTalent.getId()));
            Set<Long> existedApplicationTalentIds = applicationCountList.stream().map(EntityCountVM::getId).collect(Collectors.toSet());
            //联系方式不完全相同
            List<String> existedContacts = talentContactList.stream().map(TalentContact::getTypeAndContact).toList();
            List<String> updateContacts = crmContactDTO.getContacts().stream().map(o -> ContactType.fromDbValue(o.getType()) + o.getContact()).toList();
            if (!updateContacts.containsAll(existedContacts)) {
                throw new WithDataException("There are similar candidates in APN, but the candidate contact information is different.", cn.hutool.http.Status.HTTP_PRECON_FAILED, talentDTOV3List);
            }
            Instant createdDate = existedTalent.getCreatedDate();
            if(createdDate == null) {
                createdDate = Instant.now();
            }
            //3天保护期或者是走了流程
            if ((!ownerIds.contains(SecurityUtils.getUserId()) && Instant.now().isBefore(createdDate.plus(3, ChronoUnit.DAYS))) || CollUtil.isNotEmpty(existedApplicationTalentIds)) {
                //在流程中或者锁定期内需要判断联系方式
                JSONObject additionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
                List<TalentExperienceDTO> talentExperienceDTOList = additionalInfo.containsKey(KEY_EXPERIENCES) ? JSONUtil.toList(additionalInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class) : new ArrayList<>();
                Set<String> existedExperiences = talentExperienceDTOList.stream().map(o -> o.getCompanyName() + "@*" + o.getTitle()).collect(Collectors.toSet());
                //联系方式 & company， title不完全相同
                if (!CollUtil.containsAll(existedContacts, updateContacts) || (ObjectUtil.isNotEmpty(crmContactDTO.getAccountCompanyName()) && !existedExperiences.contains(crmContactDTO.getAccountCompanyName() + "@*" + crmContactDTO.getTitle()))) {
                    throw new CustomParameterizedException("There are similar candidates in the APN and the candidate is in the lock-in period or process.");
                }
            }
        }

        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", headers.getFirst(HttpHeaders.AUTHORIZATION));
            httpHeaders.set("Content-Type", "application/json");
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_CONTACT_URL, convertToOkHttpHeaders(httpHeaders), JsonUtil.toJson(crmContactDTO));
            if (response == null || 201 != response.getCode()) {
                if (ObjectUtils.equals(HttpStatus.BAD_REQUEST.value(), response.getCode())) {
                    JSONObject responseBody = JSONUtil.parseObj(response.getBody());
                    throw new CustomParameterizedException(responseBody.getStr("message"));
                } else if (ObjectUtils.equals(HttpStatus.PRECONDITION_FAILED.value(), response.getCode())) {
                    JSONObject responseBody = JSONUtil.parseObj(response.getBody());
                    throw new WithDataException(responseBody.getStr("message"), responseBody.getInt("status"), responseBody.get("data"));
                } else {
                    throw new CustomParameterizedException(response.getBody());
                }
            }
            JSONObject jsonObject = JSONUtil.parseObj(response.getBody());
            crmContactDTO.setId(jsonObject.getLong("id"));
            //如果CRM新建的联系人和已存在的联系人id不一致，异常
            if (CollUtil.isNotEmpty(crmContactIds) && crmContactIds.contains(crmContactDTO.getId())) {
                throw new CustomParameterizedException("duplicate crm contacts.");
            }

        } catch (CustomParameterizedException | NotFoundException | WithDataException e) {
            // 这些异常已经包含了足够的信息，不需要额外处理
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomParameterizedException(e.getMessage());
        }
        CompanyContactVO companyContactVO = new CompanyContactVO();
        ServiceUtils.myCopyProperties(crmContactDTO, companyContactVO);
        return companyContactVO;
    }

//    private void internationalCountryCodeFormat(CrmContactDTO crmContactDTO) {
//        List<ContactInfoDTO> contacts = crmContactDTO.getContacts();
//        List<ContactInfoDTO> newContacts = contacts.stream().filter(c -> c.getType().equals(PHONE.toDbValue())).filter(c -> Objects.isNull(c.getId())).collect(Collectors.toList());
//        if (CollUtil.isEmpty(newContacts)) {
//            return;
//        }
//        List<ContactInfoDTO> existedContacts = contacts.stream().filter(c -> c.getType().equals(PHONE.toDbValue())).filter(c -> Objects.nonNull(c.getId())).collect(Collectors.toList());
//        for (int i = 0; i < newContacts.size(); i++) {
//            ContactInfoDTO contactInfoDTO = newContacts.get(i);
//            existedContacts.stream().map(existed -> {
//                if (ContactUtil.similarityCheck(existed.getContact(), contactInfoDTO.getContact())) {
//                    String longerContact = contactInfoDTO.getContact().length() > existed.getContact().length() ? contactInfoDTO.getContact() : existed.getContact();
//                    existed.setContact(longerContact);
//                }
//            })
//        }
//
//        for (ContactInfoDTO contactInfoDTO : newContacts) {
//            existedContacts.stream().map(existed -> {
//                if (ContactUtil.similarityCheck(existed.getContact(), contactInfoDTO.getContact())) {
//                    String longerContact = contactInfoDTO.getContact().length() > existed.getContact().length() ? contactInfoDTO.getContact() : existed.getContact();
//
//                }
//            })
//
//        }
//    }

    public static Headers convertToOkHttpHeaders(HttpHeaders httpHeaders) {
        Headers.Builder builder = new Headers.Builder();
        httpHeaders.forEach((key, values) -> {
            for (String value : values) {
                builder.add(key, value);
            }
        });
        return builder.build();
    }

    @Override
    public JSONObject saveClientContact(JSONObject jsonObject) {
        //变商机服务类型时 联系人服务类型也变 特殊处理下 此时其他的数据没变
        List<JSONObject> crmContactList = jsonObject.containsKey(CRM_CONTACT) ? jsonObject.getJSONArray(CRM_CONTACT).toList(JSONObject.class) : new ArrayList<>();
        businessUpdateContactServiceType(jsonObject, crmContactList);
        if (!jsonObject.containsKey(CLIENT_CONTACT) && !jsonObject.containsKey(CRM_CONTACT)) {
            return null;
        }
        //Crm操作人，后续联系人合并需要校验锁定期，需要转为Apn userId
        Long operatorId = jsonObject.getLong("operator");
        List<SalesLeadClientContactVO> backFillList = new ArrayList<>();
        JSONObject backFillObj = new JSONObject();
        List<TalentContactSyncBrief> backFillContactList = new ArrayList<>();
        Set<Long> createTalentIds = new HashSet<>();
        List<JSONObject> contactInfoList = jsonObject.containsKey(CONTACT_INFO) ? jsonObject.getJSONArray(CONTACT_INFO).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactLocationList = jsonObject.containsKey(CONTACT_LOCATION) ? jsonObject.getJSONArray(CONTACT_LOCATION).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> clientContactList = jsonObject.containsKey(CLIENT_CONTACT) ? jsonObject.getJSONArray(CLIENT_CONTACT).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactTagList = jsonObject.containsKey(CONTACT_TAGS) ? jsonObject.getJSONArray(CONTACT_TAGS).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactOwnerList = jsonObject.containsKey(CONTACT_OWNER) ? jsonObject.getJSONArray(CONTACT_OWNER).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactShareUserList = jsonObject.containsKey(CONTACT_SHARE_USER) ? jsonObject.getJSONArray(CONTACT_SHARE_USER).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactShareTenantList = jsonObject.containsKey(CONTACT_SHARE_TENANT) ? jsonObject.getJSONArray(CONTACT_SHARE_TENANT).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactBusinessRelationList = jsonObject.containsKey(CONTACT_BUSINESS_RELATION) ? jsonObject.getJSONArray(CONTACT_BUSINESS_RELATION).toList(JSONObject.class) : new ArrayList<>();
        List<JSONObject> contactServiceTypeRelationList = jsonObject.containsKey(CONTACT_SERVICE_RELATION) ? jsonObject.getJSONArray(CONTACT_SERVICE_RELATION).toList(JSONObject.class) : new ArrayList<>();
        Boolean isSyncClientContact = jsonObject.containsKey(CONTACT_BUSINESS_RELATION);

        Set<Long> crmContactRelationIds = clientContactList.stream().map(o -> o.getLong("id")).collect(Collectors.toSet());
        contactInfoList.forEach(o -> {
            o.put("type", ContactType.fromDbValue(o.getInt("type")));
        });
        //        List<JSONObject> companyIndustryList = jsonObject.containsKey(INDUSTRY) ? jsonObject.getJSONArray(INDUSTRY).toList(JSONObject.class) : new ArrayList<>();
//        Map<Long, List<Long>> companyIndustryMap = companyIndustryList.stream().collect(Collectors.groupingBy(o -> o.getLong("accountCompanyId"), Collectors.mapping(value -> value.getLong("enumId"), Collectors.toList())));
////
        //先查一下CRM contact是否已经在APN了，只是因为回填talentId延迟导致新同步的contact没有talentId，做一个补充
        List<Long> checkContactIds = clientContactList.stream().map(o -> o.getLong("contactId")).collect(Collectors.toList());
        checkContactIds.addAll(crmContactList.stream().map(o -> o.getLong("id")).toList());
        checkContactIds = checkContactIds.stream().distinct().toList();
        List<SalesLeadClientContact> checkClientContacts = salesLeadClientContactRepository.findAllByCrmContactIdIn(checkContactIds);
        Map<Long, List<SalesLeadClientContact>> checkClientContactMap = checkClientContacts.stream().collect(Collectors.groupingBy(SalesLeadClientContact::getCrmContactId));
        crmContactList.forEach(o -> {
            if (!o.containsKey("apnTalentId") && checkClientContactMap.containsKey(o.getLong("id"))) {
                o.put("apnTalentId", checkClientContactMap.get(o.getLong("id")).get(0).getTalentId());
                //不会自动回填联系方式，防止apnTalentId回填延迟导致的联系方式误删
                createTalentIds.add(checkClientContactMap.get(o.getLong("id")).get(0).getTalentId());
            }
        });
        //补充联系人的tenantId & talentId，后续做查重相关的操作
        Map<Long, Long> contactTenantMap = crmContactList.stream().filter(o -> o.containsKey("id") && o.containsKey("tenantId")).collect(Collectors.toMap(item -> item.getLong("id"), item -> item.getLong("tenantId")));
        Map<Long, Long> oldContactTalentMap = crmContactList.stream().filter(o -> o.containsKey("id") && o.containsKey("apnTalentId")).collect(Collectors.toMap(item -> item.getLong("id"), item -> item.getLong("apnTalentId")));
        clientContactList.forEach(o -> {
            //不需要空key，防止后续引起bug
            if (contactTenantMap.containsKey(o.getLong("contactId"))) {
                o.put("tenantId", contactTenantMap.get(o.getLong("contactId"))); // ??
            }
            if (oldContactTalentMap.containsKey(o.getLong("contactId"))) {
                o.put("apnTalentId", oldContactTalentMap.get(o.getLong("contactId")));
            }
            //里面还有isKeyContact
        });
        //初次同步的联系人需要在apn查重
        List<Long> checkedContactIds = CollUtil.isNotEmpty(crmContactList) ? crmContactList.stream().filter(o -> !o.containsKey("apnTalentId")).map(item -> item.getLong("id")).distinct().toList() : new ArrayList<>();
        //重复的联系人，可能是联系方式和多个候选人重复，可能是候选人已经关联了其他联系人
        Set<Long> repeatedContactIds = new HashSet<>();
        checkData(crmContactList, contactInfoList, contactLocationList, clientContactList, checkedContactIds, repeatedContactIds, createTalentIds, operatorId);
        //检查数据之后，首次同步的联系人可能会关联到已有联系人，补充clientContactList的talentId
        Map<Long, Long> checkedContactTalentMap = crmContactList.stream().filter(o -> o.containsKey("id") && o.containsKey("apnTalentId")).collect(Collectors.toMap(item -> item.getLong("id"), item -> item.getLong("apnTalentId")));
        clientContactList.forEach(o -> {
            //不需要空key，防止后续引起bug
            if (checkedContactTalentMap.containsKey(o.getLong("contactId"))) {
                o.put("apnTalentId", checkedContactTalentMap.get(o.getLong("contactId")));
            }
        });
        log.info("crmContactList: {}, clientContactList: {}", crmContactList, clientContactList);
        if (CollUtil.isNotEmpty(crmContactList)) {
            List<Long> crmContactIds = crmContactList.stream().map(o -> o.getLong("id")).distinct().toList();
            //更新已关联候选人的name & experience
            List<TalentCompanySyncBrief> talentCompanyBriefList = updateTalentNameAndExperience(crmContactList, clientContactList, operatorId);
            //新建候选人
            Map<Long, List<JSONObject>> clientContactMap = clientContactList.stream().collect(Collectors.groupingBy(o -> o.getLong("contactId")));
            Map<Long, List<JSONObject>> contactInfoMap = contactInfoList.stream().collect(Collectors.groupingBy(o -> o.getLong("contactId")));
            crmContactList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> {
                TalentCompanySyncBrief talentCompanyBrief = JSONUtil.toBean(item, TalentCompanySyncBrief.class);
                //新建候选人
                talentCompanyBrief.setCreatedBy(talentCompanyBrief.getCreatedBy());
                talentCompanyBrief.setLastModifiedBy(talentCompanyBrief.getLastModifiedBy());
                talentCompanyBrief.setPermissionUserId(talentCompanyBrief.getPermissionUserId());
                talentCompanyBrief.setPermissionTeamId(null);
                //防止更新了原来的候选人，需要将id置空
                talentCompanyBrief.setId(null);
                talentCompanyBrief.setActive(item.containsKey("status") && item.getStr("status").equals("ACTIVE"));
                String commonPoolExtendedInfo = null;
                String contactExperience = null;
                TalentCompanyRelatedVM talentCompanyRelatedVM = null;
                //从es过来的联系人同步
                if (clientContactMap.containsKey(item.getLong("id")) && clientContactMap.get(item.getLong("id")).get(0).containsKey("esId") && clientContactMap.get(item.getLong("id")).get(0).containsKey("tenantId")) {
                    TalentDTOV3 esTalent = talentService.getTalentFromEsForInternal(clientContactMap.get(item.getLong("id")).get(0).getStr("esId"), clientContactMap.get(item.getLong("id")).get(0).getLong("tenantId")).getBody();
                    if (Objects.isNull(esTalent)) {
                        talentCompanyBrief.setOwnedByTenants(talentCompanyBrief.getTenantId());
                    }
                    List<TalentContactDTO> updateTalentContactList = contactInfoMap.containsKey(item.getLong("id")) ? contactInfoMap.get(item.getLong("id")).stream().map(o -> {
                        TalentContactDTO talentContactDTO = JSONUtil.toBean(o, TalentContactDTO.class);
                        return talentContactDTO;
                    }).toList() : new ArrayList<>();
                    boolean ownedByCurrentTenant = compareContacts(updateTalentContactList, esTalent.getContacts());
                    if (ownedByCurrentTenant) {
                        talentCompanyBrief.setOwnedByTenants(talentCompanyBrief.getTenantId());
                    }
                    esTalent.setSource(ResumeSourceType.COMMON_POOL);
                    commonPoolExtendedInfo = TalentV3.generateExtendedInfo(esTalent);
                    talentCompanyRelatedVM = Convert.convert(TalentCompanyRelatedVM.class, esTalent);
                } else {
                    talentCompanyBrief.setOwnedByTenants(talentCompanyBrief.getTenantId());
                }
                if (clientContactMap.containsKey(item.getLong("id"))) {
                    List<JSONObject> value = clientContactMap.get(item.getLong("id"));
                    contactExperience = JsonUtil.toJson(getNewExperience(value));
//                    talentCompanyBrief.setTalentAdditionalInfo(new TalentAdditionalInfo(JsonUtil.toJson(getNewExperience(value))));
                }
                if (ObjectUtil.isNotEmpty(contactExperience) || ObjectUtil.isNotEmpty(commonPoolExtendedInfo)) {
                    TalentAdditionalInfoSyncBrief talentAdditionalInfoSyncBrief = new TalentAdditionalInfoSyncBrief(mergeCommonPoolExperience(contactExperience, commonPoolExtendedInfo));
                    talentAdditionalInfoSyncBrief.setCreatedBy(talentCompanyBrief.getCreatedBy());
                    talentAdditionalInfoSyncBrief.setLastModifiedBy(talentCompanyBrief.getLastModifiedBy());
                    talentAdditionalInfoSyncBrief.setCreatedDate(talentCompanyBrief.getCreatedDate());
                    //更新时间，修复记录日志的bug
                    talentAdditionalInfoSyncBrief.setLastModifiedDate(Instant.now());
                    talentCompanyBrief.setTalentAdditionalInfo(talentAdditionalInfoSyncBrief);
                }
                if (ObjectUtil.isNotEmpty(talentCompanyRelatedVM)) {
                    ServiceUtils.myCopyProperties(talentCompanyRelatedVM, talentCompanyBrief);
                }
                talentCompanyBrief = talentCompanySyncBriefRepository.save(talentCompanyBrief);
                Long newTalentId = talentCompanyBrief.getId();
                item.put("apnTalentId", newTalentId);
                List<JSONObject> itemClientContactList = clientContactMap.getOrDefault(item.getLong("id"), new ArrayList<>());
                itemClientContactList.forEach(o -> o.put("apnTalentId", newTalentId));
                List<JSONObject> itemContactList = contactInfoMap.getOrDefault(item.getLong("id"), new ArrayList<>());
                itemContactList.forEach(o -> o.put("apnTalentId", newTalentId));
                if (CollUtil.isNotEmpty(itemClientContactList)) {
                    clientContactMap.put(item.getLong("id"), itemClientContactList);
                }
                if (CollUtil.isNotEmpty(itemContactList)) {
                    contactInfoMap.put(item.getLong("id"), itemContactList);
                }
                createTalentIds.add(newTalentId);
            });
            Map<Long, Long> contactTalentMap = crmContactList.stream().collect(Collectors.toMap(o -> o.getLong("id"), v -> v.getLong("apnTalentId")));
            Map<Long, String> talentNameMap = crmContactList.stream().collect(Collectors.toMap(o -> o.getLong("id"), v -> v.getStr("fullName")));
            contactInfoList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            contactLocationList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            contactOwnerList = contactOwnerList.stream().filter(o -> contactTalentMap.containsKey(o.getLong("contactId"))).toList();
            contactShareUserList = contactShareUserList.stream().filter(o -> contactTalentMap.containsKey(o.getLong("contactId"))).toList();
            contactShareTenantList = contactShareTenantList.stream().filter(o -> contactTalentMap.containsKey(o.getLong("contactId"))).toList();
            contactServiceTypeRelationList = contactServiceTypeRelationList.stream().filter(o -> contactTalentMap.containsKey(o.getLong("contactId"))).toList();
            contactOwnerList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            contactShareUserList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            contactShareTenantList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            contactServiceTypeRelationList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> item.put("apnTalentId", contactTalentMap.get(item.getLong("contactId"))));
            Map<Long, List<String>> talentEmailMap = saveContactInfo(contactInfoList, talentCompanyBriefList, createTalentIds, backFillContactList);
            saveContactLocation(contactLocationList);
            backFillList = savaClientContact(clientContactList, talentEmailMap, talentNameMap, contactTagList, crmContactIds, createTalentIds, contactBusinessRelationList, isSyncClientContact, contactServiceTypeRelationList);
            Set<Long> syncSuccessContactIds = backFillList.stream().map(SalesLeadClientContactVO::getId).collect(Collectors.toSet());
            List<SalesLeadClientContactVO> contactVOList = new ArrayList<>(backFillList);
            log.info("[CrmAccountRabbitListener] contactOwnerList:{},contactShareUserList:{},createTalentIds:{}",JSONUtil.toJsonStr(contactOwnerList),JSONUtil.toJsonStr(contactShareUserList),JSONUtil.toJsonStr(createTalentIds));
            saveContactOwnership(contactOwnerList, contactShareUserList, createTalentIds);
            //避免联系人第一次同步失败后二次同步失直接忽略而注释
//            for (Long contactId : crmContactRelationIds) {
//                if (!syncSuccessContactIds.contains(contactId)) {
//                    contactVOList.add(new SalesLeadClientContactVO(contactId));
//                }
//            }

            //未查重到候选人而新建的候选人不会回填ownership
            List<Long> backFillTalentIds = contactVOList.stream().filter(o -> o.getTalentId() != null).map(SalesLeadClientContactVO::getTalentId).distinct().toList();
            List<TalentOwnership> backFillOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdInAndOwnershipTypeIn(backFillTalentIds, List.of(TalentOwnershipType.TALENT_OWNER, TalentOwnershipType.SHARE, TalentOwnershipType.TENANT_SHARE));
            Map<Long, List<SalesLeadClientContactVO>> talentContactMap = contactVOList.stream().filter(o -> o.getTalentId() != null).collect(Collectors.groupingBy(SalesLeadClientContactVO::getTalentId));
            log.info("createTalentIds: {}", createTalentIds);
            log.info("backFillOwnershipList: {}", backFillOwnershipList);
            log.info("contactVOList: {}", contactVOList);
            contactVOList = contactVOList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getTalentId()) && createTalentIds.contains(o.getTalentId())).toList();
            backFillOwnershipList = backFillOwnershipList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getTalentId()) && createTalentIds.contains(o.getTalentId())).toList();
            backFillObj.put("clientContacts", contactVOList);
            //不需要回填联系方式到crm
//            backFillObj.put("contactInfos", backFillContactList.stream().filter(item -> talentContactMap.containsKey(item.getTalentId())).map(o -> {
//                JSONObject contact = JSONUtil.parseObj(o);
//                contact.put("contactId", talentContactMap.get(o.getTalentId()).get(0).getContactId());
//                contact.put("type", o.getType().toDbValue());
//                return contact;
//            }).toList());
            backFillObj.put("contactOwners", backFillOwnershipList.stream().filter(item -> talentContactMap.containsKey(item.getTalentId()) && TalentOwnershipType.TALENT_OWNER.equals(item.getOwnershipType())).map(o -> {
                JSONObject contact = JSONUtil.parseObj(o);
                contact.put("contactId", talentContactMap.get(o.getTalentId()).get(0).getContactId());
                contact.put("userId",o.getUserId());
                return contact;
            }).toList());
            backFillObj.put("contactShares", backFillOwnershipList.stream().filter(item -> talentContactMap.containsKey(item.getTalentId()) && (TalentOwnershipType.SHARE.equals(item.getOwnershipType()) || TalentOwnershipType.TENANT_SHARE.equals(item.getOwnershipType()))).map(o -> {
                JSONObject contact = JSONUtil.parseObj(o);
                contact.put("contactId", talentContactMap.get(o.getTalentId()).get(0).getContactId());
                contact.put("userId", o.getUserId());
                return contact;
            }).toList());

        }
        return backFillObj;
    }

    public void businessUpdateContactServiceType(JSONObject jsonObject, List<JSONObject> crmContactList) {
        List<JSONObject> contactServiceTypeRelationList = jsonObject.containsKey(CONTACT_SERVICE_RELATION) ? jsonObject.getJSONArray(CONTACT_SERVICE_RELATION).toList(JSONObject.class) : new ArrayList<>();
        List<Long> crmContactIds = crmContactList.stream().map(o -> o.getLong("id")).distinct().toList();
        log.info("businessUpdateContactServiceType : {}", JSONUtil.toJsonStr(contactServiceTypeRelationList));
//        if(CollUtil.isEmpty(contactServiceTypeRelationList)) {
//            return;
//        }

        List<ContactServiceTypeRelation> contactServiceTypeRelations = CollUtil.isNotEmpty(contactServiceTypeRelationList) ? contactServiceTypeRelationList.stream().map(o -> JSONUtil.toBean(o, ContactServiceTypeRelation.class)).toList() : new ArrayList<>();
        if(contactServiceTypeRelations.stream().anyMatch(p -> p.getContactId() == null)) {
            return;
        }
        Map<Long, List<ContactServiceTypeRelation>> relationMap = contactServiceTypeRelations.stream()
                .collect(Collectors.groupingBy(ContactServiceTypeRelation::getContactId));
        if (CollUtil.isNotEmpty(crmContactIds)) {
            for(Long contactId : crmContactIds) {
                if(!relationMap.containsKey(contactId)) {
                    relationMap.put(contactId, new ArrayList<>());
                }
            }
        }

        List<ContactServiceTypeRelation> existedContactServiceRelationList = contactServiceTypeRelationRepository.findAllByContactIdIn(relationMap.keySet());
        // 1. 将已存在的数据按 contactId 分组
        Map<Long, List<ContactServiceTypeRelation>> existedMap = existedContactServiceRelationList.stream()
                .collect(Collectors.groupingBy(ContactServiceTypeRelation::getContactId));

        List<ContactServiceTypeRelation> addList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();
        // 2. 遍历每个 contactId
        for (Map.Entry<Long, List<ContactServiceTypeRelation>> entry : relationMap.entrySet()) {
            Long contactId = entry.getKey();
            List<ContactServiceTypeRelation> newRelations = entry.getValue();
            List<ContactServiceTypeRelation> existedRelations = existedMap.getOrDefault(contactId, new ArrayList<>());

            // 3. 将现有的和新的关系转换为 serviceTypeId 集合，方便比较
            Set<Integer> newServiceTypeIds = newRelations.stream()
                    .map(ContactServiceTypeRelation::getServiceTypeId)
                    .collect(Collectors.toSet());

            Map<Integer, ContactServiceTypeRelation> existedServiceTypeMap = existedRelations.stream()
                    .collect(Collectors.toMap(
                            ContactServiceTypeRelation::getServiceTypeId,
                            relation -> relation,
                            (existing, replacement) -> existing
                    ));

            // 4. 找出需要删除的记录
            existedRelations.stream()
                    .filter(relation -> !newServiceTypeIds.contains(relation.getServiceTypeId()))
                    .map(ContactServiceTypeRelation::getId)
                    .forEach(deleteList::add);

            // 5. 找出需要新增的记录
            newRelations.stream()
                    .filter(relation -> !existedServiceTypeMap.containsKey(relation.getServiceTypeId()))
                    .forEach(o -> {
                        addList.add(new ContactServiceTypeRelation(o.getContactId(), o.getServiceTypeId()));
                    });
        }
        // 6. 执行删除和新增操作
        if (!deleteList.isEmpty()) {
            contactServiceTypeRelationRepository.deleteAllByIdInBatch(deleteList);
        }

        if (!addList.isEmpty()) {
            contactServiceTypeRelationRepository.saveAll(addList);
        }
    }

    private String mergeCommonPoolExperience(String contactExperience, String commonPoolExtendedInfo) {
        if (ObjectUtil.isEmpty(contactExperience)) {
            return commonPoolExtendedInfo;
        }
        if (ObjectUtil.isEmpty(commonPoolExtendedInfo)) {
            return contactExperience;
        }
        JSONObject extendedInfo = JSONUtil.parseObj(commonPoolExtendedInfo);
        List<TalentExperienceDTO> talentExperienceDTOList = extendedInfo.containsKey(KEY_EXPERIENCES) ? JSONUtil.toList(extendedInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class) : new ArrayList<>();
        JSONObject contactExtendedInfo = JSONUtil.parseObj(contactExperience);
        List<TalentExperienceDTO> contactExperienceDTOList = contactExtendedInfo.containsKey(KEY_EXPERIENCES) ? JSONUtil.toList(contactExtendedInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class) : new ArrayList<>();
        Map<String, List<TalentExperienceDTO>> companyNameExperienceMap = talentExperienceDTOList.stream().filter(experienceDTO -> ObjectUtil.isNotEmpty(experienceDTO.getCompanyName())).collect(Collectors.groupingBy(e -> e.getCompanyName().trim().toLowerCase()));
        List<TalentExperienceDTO> addExperienceDTOList = new ArrayList<>();
        // 获取最大的 Experience ID
        Long maxExperienceId = talentExperienceDTOList.stream()
                .filter(experienceDTO -> ObjectUtil.isNotEmpty(experienceDTO.getId()))
                .map(TalentExperienceDTO::getId)
                .max(Long::compareTo)
                .orElse(0L);

        for (TalentExperienceDTO item : contactExperienceDTOList) {
            if (companyNameExperienceMap.containsKey(item.getCompanyName().trim().toLowerCase())) {
                // 尝试根据 companyName 关联 experience
                TalentExperienceDTO talentExperienceDTO = companyNameExperienceMap.get(item.getCompanyName().trim().toLowerCase()).get(0);
                item.setId(talentExperienceDTO.getId());
                ServiceUtils.myCopyProperties(item, talentExperienceDTO);
            } else {
                // 创建新的 Experience
                maxExperienceId++;
                item.setId(maxExperienceId);
                addExperienceDTOList.add(item);
            }
        }
        talentExperienceDTOList.addAll(addExperienceDTOList);
        extendedInfo.put(KEY_EXPERIENCES, JSONUtil.parseArray(talentExperienceDTOList));
        return JsonUtil.toJson(extendedInfo);
    }

    private void saveContactOwnership(List<JSONObject> contactOwnerList, List<JSONObject> contactShareUserList, Set<Long> createTalentIds) {

        try {
            contactOwnerList = contactOwnerList.stream().filter(o -> ObjectUtil.isNotEmpty(o.get("apnTalentId"))).toList();
            contactShareUserList = contactShareUserList.stream().filter(o -> ObjectUtil.isNotEmpty(o.get("apnTalentId"))).toList();
            log.info("[APN From CRM] owner:{}. contactShareUser:{}", contactOwnerList, contactShareUserList);
            // filter sync out
            Map<Long, List<JSONObject>> contactOwnerMap = contactOwnerList.stream().collect(Collectors.groupingBy(item -> item.getLong("apnTalentId")));
            Map<Long, List<JSONObject>> contactShareUserMap = contactShareUserList.stream().collect(Collectors.groupingBy(item -> item.getLong("apnTalentId")));
//            Map<Long, List<JSONObject>> contactShareTenantMap = contactShareTenant.stream().collect(Collectors.groupingBy(item -> item.getLong("apnTalentId")));

            Set<Long> talentIds = new HashSet<>(contactOwnerMap.keySet());
            talentIds.addAll(contactShareUserMap.keySet());
            //talentIds.addAll(contactShareTenantMap.keySet());
            log.info("[APN From CRM] owner:{}. contactShareUser:{}, new talent: {}", contactOwnerList, contactShareUserList, createTalentIds);

            List<TalentOwnershipSyncBrief> existingTalentOwnershipList = talentContactOwnershipBriefRepository.findAllByTalentIdIn(new ArrayList<>(talentIds));
            Map<Long, Map<Long, TalentOwnershipSyncBrief>> existingTalentOwnershipMap = existingTalentOwnershipList.stream().collect(Collectors.groupingBy(
                    TalentOwnershipSyncBrief::getTalentId,
                    Collectors.toMap(
                            TalentOwnershipSyncBrief::getUserId,
                            Function.identity(),
                            (existing, replacement) -> existing // 保留第一个
                    )
            ));
            //talent to owner list
            Map<Long, TalentOwnershipSyncBrief> ownerTypeMap = existingTalentOwnershipList.stream()
                    .filter(o -> TalentOwnershipType.TALENT_OWNER.equals(o.getOwnershipType()))
                    .collect(Collectors.toMap(
                            TalentOwnershipSyncBrief::getTalentId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));


            List<TalentOwnershipSyncBrief> toUpdateTalentOwnershipList = new ArrayList<>();
            List<TalentOwnershipSyncBrief> talentOwnershipToDelete = new ArrayList<>();

            //todo: get tenantList
            //Instant plus3Day = createDate.plus(userService.getTenantParamValueByTenantId(TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD, ).getBody(), ChronoUnit.MINUTES);
            talentIds.forEach(talentId -> {

                //first sync: merge
                if (createTalentIds.contains(talentId)) {

                    List<JSONObject> ownerList = contactOwnerMap.getOrDefault(talentId, new ArrayList<>());
                    List<JSONObject> shareUserList = contactShareUserMap.getOrDefault(talentId, new ArrayList<>());

                    Map<Long, Set<Long>> currentTalentShareUser = new HashMap<>();
                    Map<Long, Boolean> shareWithAll = new HashMap<>();


                    if (!shareUserList.isEmpty()) {
                        Set<Long> newUserIds = shareUserList.stream().map(item -> item.getLong("userId")).filter(Objects::nonNull).collect(Collectors.toSet());

                        shareUserList.forEach(item -> {
                            Long userId = item.getLong("userId");
                            if(Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID.equals(userId)){
                                TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                talentOwnershipSyncBrief.setId(null);
                                talentOwnershipSyncBrief.setTalentId(talentId);
                                talentOwnershipSyncBrief.setUserId(Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID);
                                talentOwnershipSyncBrief.setOwnershipType( TalentOwnershipType.TENANT_SHARE);
                                talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                                talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                                talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                                toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);
                                shareWithAll.put(talentId, true); // use for owner to share
                                if(existingTalentOwnershipMap.containsKey(talentId)){
                                    List<TalentOwnershipSyncBrief> existedShare =  existingTalentOwnershipMap.get(talentId).values().stream().toList();
                                    talentOwnershipToDelete.addAll(existedShare);
                                }
                            } else if (existingTalentOwnershipMap.containsKey(talentId) && existingTalentOwnershipMap.get(talentId).containsKey(userId)) {
                                //add share user for filter owner
                                if (currentTalentShareUser.containsKey(talentId)) {
                                    currentTalentShareUser.get(talentId).add(userId);
                                } else {
                                    Set<Long> valueSet = new HashSet<>();
                                    valueSet.add(userId);
                                    currentTalentShareUser.put(talentId, valueSet);
                                }
                            } else {
                                TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                //jpa特性，设置id为null会新增数据，否则可能会误更新历史数据
                                talentOwnershipSyncBrief.setId(null);
                                talentOwnershipSyncBrief.setTalentId(talentId);
                                talentOwnershipSyncBrief.setUserId(userId);
                                talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.SHARE);
                                talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                                talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                                talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                                toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);

                                if (currentTalentShareUser.containsKey(talentId)) {
                                    currentTalentShareUser.get(talentId).add(userId);
                                } else {
                                    Set<Long> valueSet = new HashSet<>();
                                    valueSet.add(userId);
                                    currentTalentShareUser.put(talentId, valueSet);
                                }
                            }

                        });
                    }


                    // merge process
                    ownerList.forEach(item -> {
                        Long userId = item.getLong("userId");

                        //get owner from existed list
                        TalentOwnershipSyncBrief currentOwner = null;
                        if (ownerTypeMap.containsKey(talentId)) {
                            currentOwner = ownerTypeMap.get(talentId);
                        }
                        if (currentOwner != null) {
                            if (existingTalentOwnershipMap.containsKey(talentId) && existingTalentOwnershipMap.get(talentId).containsKey(userId)) {
                                TalentOwnershipSyncBrief existedTalentOwnershipSyncBrief = existingTalentOwnershipMap.get(talentId).get(userId);
                                TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                existedTalentOwnershipSyncBrief.setLastModifiedBy(existedTalentOwnershipSyncBrief.getLastModifiedBy());
                                existedTalentOwnershipSyncBrief.setAutoAssigned(talentOwnershipSyncBrief.getAutoAssigned());
                                toUpdateTalentOwnershipList.add(existedTalentOwnershipSyncBrief);

                            }else{

                                //if owner exists and owner is not the same, convert to share: owner from apn will have more privilege than crm
                                if ( !((currentTalentShareUser.containsKey(talentId) && currentTalentShareUser.get(talentId).contains(userId)) || shareWithAll.containsKey(talentId))) {
                                    TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                    talentOwnershipSyncBrief.setId(null);
                                    talentOwnershipSyncBrief.setTalentId(talentId);
                                    talentOwnershipSyncBrief.setUserId(userId);
                                    talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.SHARE);
                                    talentOwnershipSyncBrief.setAutoAssigned(false);
                                    talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                                    talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                                    talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                                    toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);

                                }
                            }
                        } else {
                            TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                            //jpa特性，设置id为null会新增数据，否则可能会误更新历史数据
                            talentOwnershipSyncBrief.setId(null);
                            talentOwnershipSyncBrief.setTalentId(talentId);
                            talentOwnershipSyncBrief.setUserId(userId);
                            talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
                            talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                            talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                            talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                            toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);
                        }
                    });
                }

                // update value
                else{
                    List<JSONObject> ownerList = contactOwnerMap.getOrDefault(talentId, new ArrayList<>());
                    List<JSONObject> shareUserList = contactShareUserMap.getOrDefault(talentId, new ArrayList<>());
                    Map<Long, Boolean> shareWithAll = new HashMap<>();

                    if (!shareUserList.isEmpty()) {
                        Set<Long> inputUserIds = shareUserList.stream().map(item -> item.getLong("userId")).filter(Objects::nonNull).collect(Collectors.toSet());

                        //case: share with all
                        if(inputUserIds.contains(Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID)){
                            if(existingTalentOwnershipMap.containsKey(talentId)){
                                var talentOwnershipList = existingTalentOwnershipMap.get(talentId).values().stream().toList();
                                var currentShareWithAll = talentOwnershipList.stream().filter(talentOwnershipSyncBrief -> TalentOwnershipType.TENANT_SHARE.equals(talentOwnershipSyncBrief.getOwnershipType())).toList();
                                // previous share with individuals, delete all exists and add share with all
                                if(currentShareWithAll.size() == 0){
                                    talentOwnershipToDelete.addAll(talentOwnershipList.stream().filter(talentOwnershipSyncBrief ->  TalentOwnershipType.SHARE.equals(talentOwnershipSyncBrief.getOwnershipType())).toList());
                                    TalentOwnershipSyncBrief talentOwnershipSyncBrief = new TalentOwnershipSyncBrief();
                                    talentOwnershipSyncBrief.setId(null);
                                    talentOwnershipSyncBrief.setTalentId(talentId);
                                    talentOwnershipSyncBrief.setUserId(Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID);
                                    talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.TENANT_SHARE);
                                    talentOwnershipSyncBrief.setAutoAssigned(false);
                                    for (JSONObject jsonObject : shareUserList) { // todo: improve
                                        TalentOwnershipSyncBrief inputTalentOwnershipSyncBrief = JSONUtil.toBean(jsonObject, TalentOwnershipSyncBrief.class);
                                        talentOwnershipSyncBrief.setCreatedBy(inputTalentOwnershipSyncBrief.getCreatedBy());
                                        talentOwnershipSyncBrief.setCreatedDate(inputTalentOwnershipSyncBrief.getCreatedDate());
                                        talentOwnershipSyncBrief.setLastModifiedBy(inputTalentOwnershipSyncBrief.getLastModifiedBy());
                                        talentOwnershipSyncBrief.setLastModifiedDate(inputTalentOwnershipSyncBrief.getLastModifiedDate());
                                    }
                                    talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                                    toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);
                                }else{
                                    // should be only one for share with all
                                    TalentOwnershipSyncBrief existedTalentOwnershipSyncBrief = currentShareWithAll.get(0);
                                    for (JSONObject jsonObject : shareUserList) {
                                        TalentOwnershipSyncBrief inputTalentOwnershipSyncBrief = JSONUtil.toBean(jsonObject, TalentOwnershipSyncBrief.class);
                                        if(inputTalentOwnershipSyncBrief != null && Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID.equals(inputTalentOwnershipSyncBrief.getUserId())){
                                            existedTalentOwnershipSyncBrief.setLastModifiedBy(inputTalentOwnershipSyncBrief.getLastModifiedBy());
                                            existedTalentOwnershipSyncBrief.setLastModifiedDate(inputTalentOwnershipSyncBrief.getLastModifiedDate());
                                        }
                                    }
                                    toUpdateTalentOwnershipList.add(existedTalentOwnershipSyncBrief);
                                }
                            }
                        }
                        else {
                            shareUserList.forEach(item -> {
                                Long userId = item.getLong("userId");
                                if( existingTalentOwnershipMap.containsKey(talentId) && existingTalentOwnershipMap.get(talentId).containsKey(userId)){
                                    TalentOwnershipSyncBrief existedTalentOwnershipSyncBrief = existingTalentOwnershipMap.get(talentId).get(userId);
                                    TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                    existedTalentOwnershipSyncBrief.setLastModifiedBy(existedTalentOwnershipSyncBrief.getLastModifiedBy());
                                    existedTalentOwnershipSyncBrief.setLastModifiedDate(talentOwnershipSyncBrief.getLastModifiedDate());
                                    existedTalentOwnershipSyncBrief.setAutoAssigned(talentOwnershipSyncBrief.getAutoAssigned());
                                    toUpdateTalentOwnershipList.add(existedTalentOwnershipSyncBrief);
                                }else {
                                    TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                                    talentOwnershipSyncBrief.setId(null);
                                    talentOwnershipSyncBrief.setTalentId(talentId);
                                    talentOwnershipSyncBrief.setUserId(userId);
                                    talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.SHARE);
                                    talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                                    talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                                    talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                                    toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);
                                }

                            });

                            if(existingTalentOwnershipMap.containsKey(talentId)){
                                var existedTalentOwnershipList = existingTalentOwnershipMap.get(talentId).values().stream().toList();
                                var toDelete = existedTalentOwnershipList.stream().filter(ownership -> (TalentOwnershipType.SHARE.equals(ownership.getOwnershipType()) || TalentOwnershipType.TENANT_SHARE.equals(ownership.getOwnershipType())) && !inputUserIds.contains(ownership.getUserId())).toList();
                                talentOwnershipToDelete.addAll(toDelete);
                            }

                        }
                    }
                    else {
                        if(existingTalentOwnershipMap.containsKey(talentId)){
                            var existedTalentOwnershipList = existingTalentOwnershipMap.get(talentId).values().stream()
                                    .filter(ownership -> TalentOwnershipType.SHARE.equals(ownership.getOwnershipType())
                                            || TalentOwnershipType.TENANT_SHARE.equals(ownership.getOwnershipType()))
                                    .toList();
                            talentOwnershipToDelete.addAll(existedTalentOwnershipList);
                        }
                    }

                    TalentOwnershipSyncBrief currentOwner;
                    if (ownerTypeMap.containsKey(talentId)) {
                        currentOwner = ownerTypeMap.get(talentId);
                    } else {
                        currentOwner = null;
                    }
                    ownerList.forEach(item -> {
                        Long userId = item.getLong("userId");

                        if(currentOwner == null){ // edge case
                            TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                            //jpa特性，设置id为null会新增数据，否则可能会误更新历史数据
                            talentOwnershipSyncBrief.setId(null);
                            talentOwnershipSyncBrief.setTalentId(talentId);
                            talentOwnershipSyncBrief.setUserId(userId);
                            talentOwnershipSyncBrief.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
                            talentOwnershipSyncBrief.setCreatedBy(talentOwnershipSyncBrief.getCreatedBy());
                            talentOwnershipSyncBrief.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                            talentOwnershipSyncBrief.setExpireTime(Instant.now().plus(3, ChronoUnit.DAYS)); // temp
                            toUpdateTalentOwnershipList.add(talentOwnershipSyncBrief);
                        }else{
                            TalentOwnershipSyncBrief talentOwnershipSyncBrief = JSONUtil.toBean(item, TalentOwnershipSyncBrief.class);
                            currentOwner.setUserId(talentOwnershipSyncBrief.getUserId());
                            currentOwner.setLastModifiedBy(talentOwnershipSyncBrief.getLastModifiedBy());
                            currentOwner.setLastModifiedDate(talentOwnershipSyncBrief.getLastModifiedDate());
                        }
                    });
                }
            });

            log.info("talentOwnership: delete info:{}",JSONUtil.toJsonStr(talentOwnershipToDelete.stream().map(TalentOwnershipSyncBrief::getId).toList()));
            log.info("talentOwnership: save info:{}",JSONUtil.toJsonStr(toUpdateTalentOwnershipList));

            talentContactOwnershipBriefRepository.deleteAllByIdInBatch(talentOwnershipToDelete.stream().map(TalentOwnershipSyncBrief::getId).toList());
            talentContactOwnershipBriefRepository.saveAll(toUpdateTalentOwnershipList);
        } catch (Exception ex) {
            log.error("[Sync CRM to APN ownership]: {}", ex);
        }

    }



    private List<TalentCompanySyncBrief> updateTalentNameAndExperience(List<JSONObject> crmContactList, List<JSONObject> clientContactList, Long operatorId) {
        List<Long> talentIds = crmContactList.stream().filter(o -> o.containsKey("apnTalentId")).map(item -> item.getLong("apnTalentId")).toList();
        if (CollUtil.isEmpty(talentIds)) {
            return new ArrayList<>();
        }
        Map<Long, List<JSONObject>> clientContactMap = clientContactList.stream().filter(o -> o.containsKey("apnTalentId")).collect(Collectors.groupingBy(o -> o.getLong("apnTalentId")));
        Map<Long, JSONObject> contactMap = crmContactList.stream()
                .filter(o -> o.containsKey("apnTalentId"))
                .collect(Collectors.toMap(
                        o -> o.getLong("apnTalentId"),
                        o -> o,
                        (existing, replacement) -> existing
                ));
        List<TalentCompanySyncBrief> talentCompanyBriefList = talentCompanySyncBriefRepository.findAllByIdIn(talentIds);
        talentCompanyBriefList.forEach(o -> {
            if (contactMap.containsKey(o.getId())) {
                JSONObject item = contactMap.get(o.getId());
                o.setFirstName(item.getStr("firstName"));
                o.setLastName(item.getStr("lastName"));
                o.setFullName(item.getStr("fullName"));
                o.setLastModifiedDate(Instant.now());
                o.setLastModifiedBy(item.getStr("lastModifiedBy"));
            }
            List<JSONObject> itemContactList = clientContactMap.getOrDefault(o.getId(), new ArrayList<>());
            Map<Long, List<JSONObject>> itemContactMap = itemContactList.stream().collect(Collectors.groupingBy(i -> i.getLong("accountCompanyId")));
            if (ObjectUtil.isEmpty(o.getTalentAdditionalInfo())) {
                TalentAdditionalInfoSyncBrief talentAdditionalInfoSyncBrief = new TalentAdditionalInfoSyncBrief(JsonUtil.toJson(getNewExperience(itemContactList)));
                talentAdditionalInfoSyncBrief.setCreatedBy(o.getLastModifiedBy());
                talentAdditionalInfoSyncBrief.setLastModifiedBy(operatorId + "," + o.getTenantId());
                talentAdditionalInfoSyncBrief.setCreatedDate(o.getCreatedDate());
                talentAdditionalInfoSyncBrief.setLastModifiedDate(Instant.now());
                o.setTalentAdditionalInfo(talentAdditionalInfoSyncBrief);
            } else {
                JSONObject exitedExtendInfo = JSONUtil.parseObj(o.getTalentAdditionalInfo().getExtendedInfo());
                if (!exitedExtendInfo.containsKey(KEY_EXPERIENCES)) {
                    exitedExtendInfo.put(KEY_EXPERIENCES, getNewExperience(itemContactList).getJSONArray(KEY_EXPERIENCES));
                } else {
                    List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(exitedExtendInfo.getJSONArray(KEY_EXPERIENCES), TalentExperienceDTO.class);
                    //将已关联联系人的experience都设置为companyId,后续会统一设置，目的方便分组更新
                    talentExperienceDTOList.forEach(experienceDTO -> {
                        if (ObjectUtil.isNotEmpty(experienceDTO.getActiveCompanyId())) {
                            experienceDTO.setCompanyId(experienceDTO.getActiveCompanyId());
                        } else if (ObjectUtil.isNotEmpty(experienceDTO.getActiveCRMAccountId())) {
                            experienceDTO.setCompanyId(experienceDTO.getActiveCRMAccountId());
                        } else if (ObjectUtil.isNotEmpty(experienceDTO.getCrmAccountId())) {
                            experienceDTO.setCompanyId(experienceDTO.getCrmAccountId());
                        }
                    });
                    //将临时设置的companyId还原，防止误更新候选人的其他experience
                    Map<Long, List<TalentExperienceDTO>> companyIdExperienceMap = talentExperienceDTOList.stream().filter(experienceDTO -> ObjectUtil.isNotEmpty(experienceDTO.getCompanyId())).collect(Collectors.groupingBy(TalentExperienceDTO::getCompanyId));
                    talentExperienceDTOList.forEach(experienceDTO -> {
                        if (ObjectUtil.isNotEmpty(experienceDTO.getActiveCompanyId())) {
                            experienceDTO.setCompanyId(null);
                        } else if (ObjectUtil.isNotEmpty(experienceDTO.getActiveCRMAccountId())) {
                            experienceDTO.setCompanyId(null);
                        } else if (ObjectUtil.isNotEmpty(experienceDTO.getCrmAccountId())) {
                            experienceDTO.setCompanyId(null);
                        }
                    });
                    //兼容experience的companyName可能和company表数据的name存在大小写不一致 & 前后空格的情况
                    Map<String, List<TalentExperienceDTO>> companyNameExperienceMap = talentExperienceDTOList.stream().filter(experienceDTO -> ObjectUtil.isNotEmpty(experienceDTO.getCompanyName())).collect(Collectors.groupingBy(e -> e.getCompanyName().trim().toLowerCase()));
                    List<TalentExperienceDTO> addExperienceDTOList = new ArrayList<>();
                    // 获取最大的 Experience ID
                    Long maxExperienceId = talentExperienceDTOList.stream()
                            .filter(experienceDTO -> ObjectUtil.isNotEmpty(experienceDTO.getId()))
                            .map(TalentExperienceDTO::getId)
                            .max(Long::compareTo)
                            .orElse(0L);

                    for (JSONObject contactInfo : itemContactList) {
                        TalentExperienceDTO talentExperienceDTO = null;
                        Long accountCompanyId = contactInfo.getLong("accountCompanyId");
                        String companyName = contactInfo.getStr("companyName");
                        String title = contactInfo.getStr("title");
                        Long originalCompanyId = contactInfo.getLong("originalCompanyId");

                        // 已关联联系人的 experience 根据 companyId 更新
                        if (companyIdExperienceMap.containsKey(accountCompanyId)) {
                            //这里需要注意：
                            //如果有公司id匹配上的工作经验
                            //当有且只有一条匹配，且无论有没有TalentRecruitmentProcessId，则更新这条经验
                            //当有多条匹配，且有TalentRecruitmentProcessId 以外的经验，则更新没有TalentRecruitmentProcessId的经验的第一条
                           List<TalentExperienceDTO> talentExperiences = companyIdExperienceMap.get(accountCompanyId);
                           if(talentExperiences.size() > 1) {
                               Optional<TalentExperienceDTO> currentExperience = talentExperiences.stream()
                                       .filter(experienceDTO -> ObjectUtil.isNull(experienceDTO.getTalentRecruitmentProcessId()))
                                       .findFirst();
                               if(currentExperience.isPresent()) {
                                   talentExperienceDTO = currentExperience.get();
                               }
                           }else {
                               //只有一条经验匹配上，就跟新这条经验
                               talentExperienceDTO = talentExperiences.get(0);
                           }
                        } else if (companyNameExperienceMap.containsKey(companyName.trim().toLowerCase())) {
                            // 尝试根据 companyName 关联 experience
                            talentExperienceDTO = companyNameExperienceMap.get(companyName.trim().toLowerCase()).get(0);
                        } else if (null != originalCompanyId) {
                            if (companyIdExperienceMap.containsKey(originalCompanyId)) {
                                //这里需要注意：
                                //如果有公司id匹配上的工作经验
                                //当有且只有一条匹配，且无论有没有TalentRecruitmentProcessId，则更新这条经验
                                //当有多条匹配，且有TalentRecruitmentProcessId 以外的经验，则更新没有TalentRecruitmentProcessId的经验的第一条
                                List<TalentExperienceDTO> talentExperiences = companyIdExperienceMap.get(originalCompanyId);
                                if (talentExperiences.size() > 1) {
                                    Optional<TalentExperienceDTO> currentExperience = talentExperiences.stream()
                                            .filter(experienceDTO -> ObjectUtil.isNull(experienceDTO.getTalentRecruitmentProcessId()))
                                            .filter(experienceDTO -> experienceDTO.getEndDate() == null)
                                            .findFirst();
                                    if (currentExperience.isPresent()) {
                                        talentExperienceDTO = currentExperience.get();
                                    }
                                } else {
                                    //只有一条经验匹配上，就跟新这条经验
                                    talentExperienceDTO = talentExperiences.get(0);
                                }
                            }
                        } else {
                            //联系人关联公司被替换  原始公司信息的工作经历 直接更新为新公司的id
                            if (null == originalCompanyId) {
                                // 创建新的 Experience
                                maxExperienceId++;
                                TalentExperienceDTO addTalentExperienceDTO = new TalentExperienceDTO(maxExperienceId, companyName, title);
                                setExperienceDetails(contactInfo, addTalentExperienceDTO, accountCompanyId);
                                addExperienceDTOList.add(addTalentExperienceDTO);
                            }
                        }
                        //更新原本的Experience
                        if (talentExperienceDTO != null) {
                            updateTalentExperienceDTO(contactInfo, talentExperienceDTO, companyName, title, accountCompanyId);
                        }
                    }
                    talentExperienceDTOList.addAll(addExperienceDTOList);
                    exitedExtendInfo.put(KEY_EXPERIENCES, JSONUtil.parseArray(talentExperienceDTOList));
                }
                //additionalInfo有更新
                if (!JsonUtil.toJson(exitedExtendInfo).equals(o.getTalentAdditionalInfo().getExtendedInfo())) {
                    o.getTalentAdditionalInfo().setLastModifiedBy(operatorId + "," + o.getTenantId());
                }
                o.getTalentAdditionalInfo().setExtendedInfo(JsonUtil.toJson(exitedExtendInfo));
            }
        });
        talentCompanySyncBriefRepository.saveAll(talentCompanyBriefList);
        return talentCompanyBriefList;
    }

    private static void setExperienceDetails(JSONObject contactInfo, TalentExperienceDTO talentExperienceDTO, Long accountCompanyId) {
        if (!contactInfo.containsKey("isClient")) {
            talentExperienceDTO.setCurrent(contactInfo.containsKey("active") && Boolean.TRUE.equals(contactInfo.getBool("active")));
            if (talentExperienceDTO.getCurrent()) {
                talentExperienceDTO.setActiveCRMAccountId(accountCompanyId);
            } else {
                talentExperienceDTO.setCrmAccountId(accountCompanyId);
            }
        } else if (contactInfo.containsKey("active") && Boolean.TRUE.equals(contactInfo.getBool("active"))) {
            talentExperienceDTO.setCurrent(true);
            talentExperienceDTO.setActiveCompanyId(accountCompanyId);
        } else {
            talentExperienceDTO.setCurrent(false);
            talentExperienceDTO.setCompanyId(accountCompanyId);
        }
        talentExperienceDTO.setDepartment(contactInfo.getStr("department"));
    }

    private void updateTalentExperienceDTO(JSONObject contactInfo, TalentExperienceDTO talentExperienceDTO, String companyName, String title, Long accountCompanyId) {
        talentExperienceDTO.setDepartment(contactInfo.getStr("department"));
        talentExperienceDTO.setCompanyName(companyName);
        talentExperienceDTO.setTitle(title);
        talentExperienceDTO.setActiveCompanyId(null);
        talentExperienceDTO.setActiveCRMAccountId(null);
        talentExperienceDTO.setCrmAccountId(null);
        talentExperienceDTO.setCompanyId(null);

        if (!contactInfo.containsKey("isClient")) {
            talentExperienceDTO.setCurrent(contactInfo.containsKey("active") && Boolean.TRUE.equals(contactInfo.getBool("active")));
            if (talentExperienceDTO.getCurrent()) {
                talentExperienceDTO.setActiveCRMAccountId(accountCompanyId);
            } else {
                talentExperienceDTO.setCrmAccountId(accountCompanyId);
            }
        } else if (contactInfo.containsKey("active") && Boolean.TRUE.equals(contactInfo.getBool("active"))) {
            talentExperienceDTO.setCurrent(true);
            talentExperienceDTO.setActiveCompanyId(accountCompanyId);
        } else {
            talentExperienceDTO.setCurrent(false);
            talentExperienceDTO.setCompanyId(accountCompanyId);
        }
    }


    private JSONObject getNewExperience(List<JSONObject> value) {
        JSONArray experienceArray = new JSONArray();
        for (int i = 0; i < value.size(); i++) {
            TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO(Long.valueOf(String.valueOf(i)), value.get(i).getStr("companyName"), value.get(i).getStr("title"));
            if (!value.get(i).containsKey("isClient")) {
                talentExperienceDTO.setCurrent(value.get(i).containsKey("active") && Boolean.TRUE.equals(value.get(i).getBool("active")));
                if (talentExperienceDTO.getCurrent()) {
                    talentExperienceDTO.setActiveCRMAccountId(value.get(i).getLong("accountCompanyId"));
                } else {
                    talentExperienceDTO.setCrmAccountId(value.get(i).getLong("accountCompanyId"));
                }
            } else if (value.get(i).containsKey("active") && Boolean.TRUE.equals(value.get(i).getBool("active"))) {
                talentExperienceDTO.setCurrent(true);
                talentExperienceDTO.setActiveCompanyId(value.get(i).getLong("accountCompanyId"));
            } else {
                talentExperienceDTO.setCurrent(false);
                talentExperienceDTO.setCompanyId(value.get(i).getLong("accountCompanyId"));
            }
            talentExperienceDTO.setDepartment(value.get(i).getStr("department"));
            experienceArray.add(JSONUtil.parseObj(talentExperienceDTO));
        }
        JSONObject talentAdditionalInfo = new JSONObject();
        talentAdditionalInfo.put(KEY_EXPERIENCES, experienceArray);
        return talentAdditionalInfo;
    }

    private void checkData(List<JSONObject> crmContactList, List<JSONObject> contactInfoList, List<JSONObject> contactLocationList, List<JSONObject> clientContactList, List<Long> checkedContactIds, Set<Long> repeatedContactIds, Set<Long> createTalentIds, Long operatorId) {
        if (CollUtil.isNotEmpty(crmContactList)) {
            Long tenantId = crmContactList.get(0).getLong("tenantId");
            //根据联系方式查疑似候选人id
            List<Long> talentIds = checkTalentContactInformation(contactInfoList.stream().filter(o -> checkedContactIds.contains(o.getLong("contactId"))).toList(), tenantId);
            if (CollUtil.isNotEmpty(talentIds)) {
                List<TalentCompanyBrief> existedTalentList = talentCompanyBriefRepository.findAllByIdIn(talentIds);
                Map<Long, TalentCompanyBrief> existedTalentMap = existedTalentList.stream().collect(Collectors.toMap(TalentCompanyBrief::getId, o -> o));
                //查走了流程的候选人
                List<EntityCountVM> applicationCountList = applicationServiceRepository.findApplicationCountByTalentIds(talentIds);
                Set<Long> existedApplicationTalentIds = applicationCountList.stream().map(EntityCountVM::getId).collect(Collectors.toSet());
                //查已经关联联系人的候选人
                List<Long> relatedTalentId = salesLeadClientContactRepository.findTalentIdByTenantIdAndTalentIdIn(tenantId, talentIds);
                //查疑似的联系方式
                List<TalentContact> allTalentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndStatus(talentIds, AVAILABLE);
                List<TalentContact> talentContactList = allTalentContactList.stream().filter(o -> checkContactTypes.contains(o.getType())).toList();
                Map<Long, List<String>> allTalentContactMap = allTalentContactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId, Collectors.mapping(TalentContact::getContact, Collectors.toList())));
                //需要查重的联系方式map
                Map<Long, List<String>> checkContactMap = contactInfoList.stream().filter(o -> checkedContactIds.contains(o.getLong("contactId"))).collect(Collectors.groupingBy(item -> item.getLong("contactId"), Collectors.mapping(value -> value.getStr("contact"), Collectors.toList())));
                //疑似候选人的联系方式map，电话统一转为不带区号的联系方式
                Map<String, List<Long>> existedContactMap = talentContactList.stream()
                        .collect(Collectors.groupingBy(
                                o -> PhoneNumberUtils.parsePhoneNumber(o.getContact()).getPhone() != null ? PhoneNumberUtils.parsePhoneNumber(o.getContact()).getPhone().toString() : o.getContact(),
                                Collectors.mapping(TalentContact::getTalentId, Collectors.toList())
                        ));

                //查候选人的owner
                List<TalentOwnership> talentOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdInAndOwnershipTypeIn(talentIds, checkTalentOwners);
                Map<Long, Set<Long>> talentOwnershipMap = talentOwnershipList.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId, Collectors.mapping(TalentOwnership::getUserId, Collectors.toSet())));
                Set<Long> owners = talentOwnershipList.stream().map(TalentOwnership::getUserId).collect(Collectors.toSet());
                log.info("checkData, existedContactMap:{}, checkContactMap:{}, relatedTalentId: {}", existedContactMap, checkContactMap, relatedTalentId);
                crmContactList.stream().filter(o -> !o.containsKey("apnTalentId")).forEach(item -> {
                    if (checkContactMap.containsKey(item.getLong("id"))) {
                        List<String> itemContacts = checkContactMap.get(item.getLong("id"));
                        //疑似的候选人集合
                        List<Long> itemSimilarTalentIds = new ArrayList<>();
                        //判断前需要去重，可能联系人有多个联系方式
                        Set<Long> suspectedTalentIds = new HashSet<>();
                        for (int i = 0; i < itemContacts.size(); i++) {
                            //尝试解析
                            String contactKey = PhoneNumberUtils.parsePhoneNumber(itemContacts.get(i)).getPhone() != null ? PhoneNumberUtils.parsePhoneNumber(itemContacts.get(i)).getPhone().toString() : itemContacts.get(i);
                            log.info("checkData, contactKey:{}", contactKey);
                            if (existedContactMap.containsKey(contactKey)) {
                                suspectedTalentIds.addAll(existedContactMap.get(contactKey));
                                itemSimilarTalentIds = suspectedTalentIds.stream().toList();
                                //如果疑似候选人大于1或者候选人已经关联了联系人
                                if (itemSimilarTalentIds.size() > 1 || (itemSimilarTalentIds.size() == 1 && relatedTalentId.contains(itemSimilarTalentIds.get(0)))) {
                                    repeatedContactIds.add(item.getLong("id"));
                                    break;
                                }
                            }
                        }
                        //查重了唯一的候选人和联系人关联
                        if (CollUtil.isNotEmpty(itemSimilarTalentIds) && itemSimilarTalentIds.size() == 1) {
                            Long talentId = itemSimilarTalentIds.get(0);
                            TalentCompanyBrief talentCompanyBrief = existedTalentMap.get(talentId);
                            //姓名不一样不能同步
                            if (!talentCompanyBrief.getFirstName().equalsIgnoreCase(item.getStr("firstName")) || !talentCompanyBrief.getLastName().equalsIgnoreCase(item.getStr("lastName"))) {
                                repeatedContactIds.add(item.getLong("id"));
                            } else if (talentOwnershipMap.containsKey(talentCompanyBrief.getId()) && !owners.containsAll(talentOwnershipMap.get(talentCompanyBrief.getId()))) {
                                //已存在的候选人owner没有crm账号不允许同步
                                repeatedContactIds.add(item.getLong("id"));
                            } else if ((!talentCompanyBrief.getPermissionUserId().equals(operatorId) && (!talentOwnershipMap.containsKey(talentCompanyBrief.getId())
                                    || !talentOwnershipMap.get(talentCompanyBrief.getId()).contains(operatorId)) && Instant.now().isBefore(talentCompanyBrief.getCreatedDate().plus(3, ChronoUnit.DAYS))) || existedApplicationTalentIds.contains(talentId)) {
                                //在流程中或者锁定期内需要判断，用户是候选人创建人或者owner可以跳过锁定期
                                List<String> existedTalentContactList = allTalentContactMap.getOrDefault(talentId, new ArrayList<>());
                                List<String> updateContactInfoList = checkContactMap.getOrDefault(item.getLong("id"), new ArrayList<>());
                                if (CollUtil.containsAll(existedTalentContactList, updateContactInfoList)) {
                                    item.put("apnTalentId", itemSimilarTalentIds.get(0));
                                    relatedTalentId.add(itemSimilarTalentIds.get(0));
                                    //查到唯一的候选人并且可以关联联系人，防止后面把候选人的联系方式删除
                                    createTalentIds.add(itemSimilarTalentIds.get(0));
                                } else {
                                    repeatedContactIds.add(item.getLong("id"));
                                }
                            } else {
                                item.put("apnTalentId", itemSimilarTalentIds.get(0));
                                relatedTalentId.add(itemSimilarTalentIds.get(0));
                                //查到唯一的候选人并且可以关联联系人，防止后面把候选人的联系方式删除
                                createTalentIds.add(itemSimilarTalentIds.get(0));
                            }
                        }
                    }
                });
            }
        }
        if (CollUtil.isNotEmpty(repeatedContactIds)) {
            crmContactList.removeIf(o -> repeatedContactIds.contains(o.getLong("id")));
            contactInfoList.removeIf(o -> repeatedContactIds.contains(o.getLong("contactId")));
            contactLocationList.removeIf(o -> repeatedContactIds.contains(o.getLong("contactId")));
            clientContactList.removeIf(o -> repeatedContactIds.contains(o.getLong("contactId")));
        }
    }

    private boolean checkCompanyAndTitle(TalentAdditionalInfo talentAdditionalInfo, List<JSONObject> clientContactList) {
        if (talentAdditionalInfo == null || talentAdditionalInfo.getExtendedInfo() == null || CollUtil.isEmpty(clientContactList)) {
            return false;
        }
        JSONObject additionalInfo = JSONUtil.parseObj(talentAdditionalInfo.getExtendedInfo());
        if (!additionalInfo.containsKey(KEY_EXPERIENCES)) {
            return false;
        }
        //查候选人已有的experience
        List<TalentExperienceDTO> experienceDTOList = JSONUtil.toList(JSONUtil.parseArray(additionalInfo.getJSONArray(KEY_EXPERIENCES)), TalentExperienceDTO.class);
        Set<String> experiences = experienceDTOList.stream().map(o -> o.getCompanyName() + "@" + o.getTitle()).collect(Collectors.toSet());
        //锁定期或者流程中联系人工作经历候选人experience完全吻合才能同步
        return clientContactList.stream().allMatch(o -> experiences.contains((o.getStr("companyName") + "@" + o.getStr("title"))));
    }

    private Map<Long, List<String>> saveContactInfo(List<JSONObject> contactInfoList, List<TalentCompanySyncBrief> talentCompanyBriefList, Set<Long> createTalentIds, List<TalentContactSyncBrief> buckFillContactList) {
        //业务上不允许联系人联系方式为空，此处仅是为了兼容CRM联系人同步。当编辑公司 & 商机时可能会触发联系人同步。此时是不会对联系人的联系方式做更新，如果说联系人不是首次同步需要关联候选人，那么CRM就不需要传contactInfo，避免误删关联候选人的联系方式（候选人联系方式不会回填，联系人 & 候选人的联系方式可能不一致）。
        if (CollUtil.isEmpty(contactInfoList)) {
            return new HashMap<>();
        }
        //编辑联系人仅通过crm接口
        List<Long> talentIds = contactInfoList.stream().filter(o -> o.containsKey("apnTalentId")).map(item -> item.getLong("apnTalentId")).distinct().toList();
        List<TalentContactSyncBrief> talentContactList = talentContactSyncBriefRepository.findAllByTalentIdInAndStatusOrderBySortAsc(talentIds, AVAILABLE);
        Set<String> existedContacts = talentContactList.stream().map(TalentContactSyncBrief::getTypeAndContact).collect(Collectors.toSet());
        Map<Long, Set<String>> saveContactMap = contactInfoList.stream().collect(Collectors.groupingBy(o -> o.getLong("apnTalentId"), Collectors.mapping(o -> o.getStr("type") + o.getStr("contact"), Collectors.toSet())));
        talentContactList.stream().filter(o -> !createTalentIds.contains(o.getTalentId()) && saveContactMap.containsKey(o.getTalentId()) && !saveContactMap.get(o.getTalentId()).contains(o.getTypeAndContact())).forEach(item -> item.setStatus(TalentContactStatus.INVALID));
        //联系人关联到已存在的候选人，需要回填联系人没有的联系方式（已废弃，直接在CRM维护，不需要回填）
        List<TalentContactSyncBrief> addList = talentContactList.stream().filter(o -> createTalentIds.contains(o.getTalentId()) && saveContactMap.containsKey(o.getTalentId()) &&
                !saveContactMap.get(o.getTalentId()).contains(o.getContact())).toList();
        buckFillContactList.addAll(addList);
        Map<Long, Long> addNewContactMap = new HashMap<>();
        contactInfoList.stream().filter(o -> !existedContacts.contains(o.getStr("type") + o.getStr("contact"))).forEach(item -> {
            TalentContactSyncBrief talentContact = JSONUtil.toBean(item, TalentContactSyncBrief.class);
            talentContact.setId(null);
            talentContact.setTalentId(item.getLong("apnTalentId"));
            talentContact.setStatus(TalentContactStatus.AVAILABLE);
            talentContact.setSort(EMAIL.equals(talentContact.getType()) || PHONE.equals(talentContact.getType()) ? 0 : 1);
            talentContact.setCreatedBy(talentContact.getCreatedBy());
            talentContact.setLastModifiedBy(talentContact.getLastModifiedBy());
            talentContact.setPermissionUserId(talentContact.getPermissionUserId());
            talentContact.setPermissionTeamId(null);
            talentContactList.add(talentContact);
            addNewContactMap.put(talentContact.getTalentId(), talentContact.getTenantId());
        });
        talentCompanyBriefList.forEach(i -> {
            if (ObjectUtil.isEmpty(i.getOwnedByTenants()) && addNewContactMap.containsKey(i.getId())) {
                i.setOwnedByTenants(addNewContactMap.get(i.getId()));
            }
        });
        talentContactSyncBriefRepository.saveAll(talentContactList);
        return talentContactList.stream().filter(item -> EMAIL.equals(item.getType())).collect(Collectors.groupingBy(TalentContactSyncBrief::getTalentId, Collectors.mapping(TalentContactSyncBrief::getContact, Collectors.toList())));
    }

    private void saveContactLocation(List<JSONObject> contactLocationList) {
        List<Long> talentIds = contactLocationList.stream().filter(o -> o.containsKey("apnTalentId")).map(item -> item.getLong("apnTalentId")).distinct().toList();
        List<TalentCurrentLocationSyncBrief> talentCurrentLocationCompanyBriefList = talentCurrentLocationSyncBriefRepository.findAllByTalentIdIn(talentIds);
        Set<Long> existedTalentIds = talentCurrentLocationCompanyBriefList.stream().map(TalentCurrentLocationSyncBrief::getTalentId).collect(Collectors.toSet());
        Map<Long, String> locationMap = contactLocationList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(o -> o.getLong("apnTalentId"),
                                Collectors.mapping(v -> v.getStr("originalLoc"),
                                        Collectors.toList())),
                        map -> map.entrySet().stream()
                                .collect(Collectors.toMap(Map.Entry::getKey,
                                        entry -> entry.getValue().get(0)))
                ));
        contactLocationList.stream().filter(o -> !existedTalentIds.contains(o.getLong("apnTalentId"))).forEach(item -> {
            TalentCurrentLocationSyncBrief talentCurrentLocationSyncBrief = JSONUtil.toBean(item, TalentCurrentLocationSyncBrief.class);
            talentCurrentLocationSyncBrief.setId(null);
            talentCurrentLocationSyncBrief.setTalentId(item.getLong("apnTalentId"));
            talentCurrentLocationSyncBrief.setCreatedBy(talentCurrentLocationSyncBrief.getCreatedBy());
            talentCurrentLocationSyncBrief.setLastModifiedBy(talentCurrentLocationSyncBrief.getLastModifiedBy());
            talentCurrentLocationCompanyBriefList.add(talentCurrentLocationSyncBrief);
        });
        talentCurrentLocationCompanyBriefList.forEach(item -> item.setOriginalLoc(JsonUtil.toJson(JSONUtil.parseObj(locationMap.get(item.getTalentId())))));
        talentCurrentLocationSyncBriefRepository.saveAll(talentCurrentLocationCompanyBriefList);
    }

    private List<SalesLeadClientContactVO> savaClientContact(List<JSONObject> clientContactList, Map<Long, List<String>> clientEmailMap, Map<Long, String> clientNameMap, List<JSONObject> contactTagList, List<Long> crmContactIds, Set<Long> createTalentIds, List<JSONObject> contactBusinessRelationList, Boolean isSyncClientContact, List<JSONObject> contactServiceTypeRelationList) {
        List<SalesLeadClientContactMigrateDTO> salesLeadClientContactDTOList = clientContactList.stream().map(o -> {
            SalesLeadClientContactMigrateDTO salesLeadClientContactDTO = JSONUtil.toBean(o, SalesLeadClientContactMigrateDTO.class);
            salesLeadClientContactDTO.setCompanyId(o.getLong("accountCompanyId"));
            salesLeadClientContactDTO.setCrmContactId(o.getLong("contactId"));
            salesLeadClientContactDTO.setTalentId(o.getLong("apnTalentId"));
            JSONObject itemAdditionalInfo = JSONUtil.parseObj(o.getStr("extendedInfo"));
            if (itemAdditionalInfo.containsKey("creditTransactionId")) {
                salesLeadClientContactDTO.setCreditTransactionId(itemAdditionalInfo.getLong("creditTransactionId"));
            }
            return salesLeadClientContactDTO;
        }).toList();
        Map<Long, JSONObject> clientContactMap = clientContactList.stream().collect(Collectors.toMap(o -> o.getLong("id"), o -> o));
        List<Long> contactIds = salesLeadClientContactDTOList.stream().map(SalesLeadClientContactMigrateDTO::getId).filter(Objects::nonNull).toList();
        List<SalesLeadClientContactAdditionalInfo> additionalInfoList = salesLeadClientContactAddtionalInfoRepository.findByCompanyContactIdIn(contactIds);
        Set<Long> existedAdditionalInfoContactIds = additionalInfoList.stream().map(SalesLeadClientContactAdditionalInfo::getCompanyContactId).collect(Collectors.toSet());
        additionalInfoList.forEach(o -> {
            if (clientContactMap.containsKey(o.getCompanyContactId())) {
                JSONObject item = clientContactMap.get(o.getCompanyContactId());
                JSONObject itemAdditionalInfo = JSONUtil.parseObj(item.getStr("extendedInfo"));
                if (item.containsKey("businessGroup")) {
                    itemAdditionalInfo.put("businessGroup", item.getStr("businessGroup"));
                }
                if (item.containsKey("businessUnit")) {
                    itemAdditionalInfo.put("businessUnit", item.getStr("businessUnit"));
                }
                if (item.containsKey("department")) {
                    itemAdditionalInfo.put("department", item.getStr("department"));
                }
                if (item.containsKey("note")) {
                    itemAdditionalInfo.put("note", item.getStr("note"));
                }
                o.setExtendedInfo(JsonUtil.toJson(itemAdditionalInfo));
            }
        });
        List<SalesLeadClientContactAdditionalInfo> createAdditionalInfoList = clientContactList.stream()
                .filter(o -> !existedAdditionalInfoContactIds.contains(o.getLong("id"))).map(item -> {
                    JSONObject itemAdditionalInfo = JSONUtil.parseObj(item.getStr("extendedInfo"));
                    if (item.containsKey("businessGroup")) {
                        itemAdditionalInfo.put("businessGroup", item.getStr("businessGroup"));
                    }
                    if (item.containsKey("businessUnit")) {
                        itemAdditionalInfo.put("businessUnit", item.getStr("businessUnit"));
                    }
                    if (item.containsKey("department")) {
                        itemAdditionalInfo.put("department", item.getStr("department"));
                    }
                    if (item.containsKey("note")) {
                        itemAdditionalInfo.put("note", item.getStr("note"));
                    }
                    return new SalesLeadClientContactAdditionalInfo(item.getLong("id"), JsonUtil.toJson(itemAdditionalInfo));
                }).toList();

        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllById(contactIds);
        Set<Long> existedClientContactIds = salesLeadClientContactList.stream().map(SalesLeadClientContact::getId).collect(Collectors.toSet());
        Map<Long, SalesLeadClientContactMigrateDTO> salesLeadClientContactDTOMap = salesLeadClientContactDTOList.stream().filter(i -> ObjectUtil.isNotEmpty(i.getId())).collect(Collectors.toMap(SalesLeadClientContactMigrateDTO::getId, o -> o));
        //处理需要更新的联系人
        salesLeadClientContactList.forEach(o -> ServiceUtils.myCopyProperties(salesLeadClientContactDTOMap.get(o.getId()), o));
        //处理需要新增的联系人
        salesLeadClientContactList.addAll(salesLeadClientContactDTOList.stream().filter(o -> !existedClientContactIds.contains(o.getId())).map(SalesLeadClientContactMigrateDTO::toSalesLeadClientContact).toList());

        salesLeadClientContactRepository.saveAll(salesLeadClientContactList);
        salesLeadClientContactAddtionalInfoRepository.saveAll(additionalInfoList);
        salesLeadClientContactAddtionalInfoRepository.saveAll(createAdditionalInfoList);

        log.info("save crm client isSyncClientContact : {}", isSyncClientContact);
        if (Boolean.TRUE.equals(isSyncClientContact)) {
            List<AccountBusinessContactRelation> contactRelationList = CollUtil.isNotEmpty(contactBusinessRelationList) ? contactBusinessRelationList.stream().map(o -> JSONUtil.toBean(o, AccountBusinessContactRelation.class)).toList() : new ArrayList<>();
            Map<Long, AccountBusinessContactRelation> contactRelationMap = contactRelationList.stream().collect(Collectors.toMap(AccountBusinessContactRelation::getId, o -> o));
            List<AccountBusinessContactRelation> existedBusinessContactRelationList = accountBusinessContactRelationRepository.findAllRelationByClientContactIdIn(contactIds);
            List<Long> deleteContactRelationIds = existedBusinessContactRelationList.stream().map(AccountBusinessContactRelation::getId).filter(o -> !contactRelationMap.containsKey(o)).toList();
            existedBusinessContactRelationList.forEach(item -> {
                if (contactRelationMap.containsKey(item.getId())) {
                    ServiceUtils.myCopyProperties(contactRelationMap.get(item.getId()), item);
                }
            });
            log.info("save crm client contactIds: {}, contactRelationList : {}, existedBusinessContactRelationList: {}, deleteContactRelationIds: {}", contactIds, contactRelationList, existedBusinessContactRelationList, deleteContactRelationIds);
            accountBusinessContactRelationRepository.deleteAllByIdInBatch(deleteContactRelationIds);
            accountBusinessContactRelationRepository.saveAll(contactRelationList);
        }
        //保存联系人tags
        saveTags(crmContactIds, contactTagList);
        if (ObjectUtil.isNotEmpty(clientNameMap) && ObjectUtil.isNotEmpty(clientEmailMap)) {
            for (SalesLeadClientContact salesLeadClientContact : salesLeadClientContactList) {
//                clientContactDetection(salesLeadClientContact, clientEmailMap.get(salesLeadClientContact.getTalentId()), clientNameMap.get(salesLeadClientContact.getTalentId()));
            }
        }
        //保存关联服务类型  提前到一开始处理
//        saveServiceTypeRelation(crmContactIds, contactServiceTypeRelationList);

        for (SalesLeadClientContactMigrateDTO salesLeadClientContactMigrateDTO : salesLeadClientContactDTOList) {
            if (ObjectUtil.isNotEmpty(salesLeadClientContactMigrateDTO.getTalentId()) && ObjectUtil.isNotEmpty(salesLeadClientContactMigrateDTO.getCreditTransactionId()) && createTalentIds.contains(salesLeadClientContactMigrateDTO.getTalentId())) {
                creditTransactionCompanyBriefRepository.updateTalentId(salesLeadClientContactMigrateDTO.getTalentId(), salesLeadClientContactMigrateDTO.getCreditTransactionId());
            }
        }
        return salesLeadClientContactList.stream().map(SalesLeadClientContactVO::fromSalesLeadClientContact).toList();
    }

    private void saveServiceTypeRelation(List<Long> crmContactIds, List<JSONObject> contactServiceTypeRelationList) {
        List<ContactServiceTypeRelation> contactServiceTypeRelations = CollUtil.isNotEmpty(contactServiceTypeRelationList) ? contactServiceTypeRelationList.stream().map(o -> JSONUtil.toBean(o, ContactServiceTypeRelation.class)).toList() : new ArrayList<>();

        Map<Long, ContactServiceTypeRelation> contactServiceTypeRelationMap = contactServiceTypeRelations.stream().collect(Collectors.toMap(ContactServiceTypeRelation::getId, o -> o));

        List<ContactServiceTypeRelation> existedContactServiceRelationList = contactServiceTypeRelationRepository.findAllByContactIdIn(crmContactIds);

        List<Long> deleteLocationIds = existedContactServiceRelationList.stream().map(ContactServiceTypeRelation::getId).filter(o -> !contactServiceTypeRelationMap.containsKey(o)).toList();

        existedContactServiceRelationList.forEach(item -> {
            if (contactServiceTypeRelationMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contactServiceTypeRelationMap.get(item.getId()), item);
            }
        });

        contactServiceTypeRelationRepository.deleteAllByIdInBatch(deleteLocationIds);
        contactServiceTypeRelationRepository.saveAll(contactServiceTypeRelations);
    }

    private void saveTags(List<Long> crmContactIds, List<JSONObject> contactTagList) {
        List<ContactTagRelation> contactTagRelationList = CollUtil.isNotEmpty(contactTagList) ? contactTagList.stream().map(o -> JSONUtil.toBean(o, ContactTagRelation.class)).toList() : new ArrayList<>();

        Map<Long, ContactTagRelation> contactTagRelationMap = contactTagRelationList.stream().collect(Collectors.toMap(ContactTagRelation::getId, o -> o));

        List<ContactTagRelation> existedContactTagRelationList = contactTagRelationRepository.findAllByContactIdIn(crmContactIds);

        List<Long> deleteLocationIds = existedContactTagRelationList.stream().map(ContactTagRelation::getId).filter(o -> !contactTagRelationMap.containsKey(o)).toList();

        existedContactTagRelationList.forEach(item -> {
            if (contactTagRelationMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contactTagRelationMap.get(item.getId()), item);
            }
        });

        contactTagRelationRepository.deleteAllByIdInBatch(deleteLocationIds);
        contactTagRelationRepository.saveAll(contactTagRelationList);
    }


    private List<Long> checkTalentContactInformation(List<JSONObject> contacts, Long tenantId) {
        if (CollUtil.isEmpty(contacts)) {
            return new ArrayList<>();
        }
        List<TalentContactDTO> contactDTOList = contacts.stream().map(o -> {
            TalentContactDTO talentContactDTO = JSONUtil.toBean(o, TalentContactDTO.class);
            return talentContactDTO;
        }).filter(item -> checkContactTypes.contains(item.getType()) && ObjectUtil.isNotEmpty(item.getContact())).toList();

        if (CollUtil.isEmpty(contactDTOList)) {
            return new ArrayList<>();
        }
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        talentDTOV3.setTenantId(tenantId);
        talentDTOV3.setContacts(contactDTOList);
        Set<Long> allIds = new HashSet<>();
        //需要es查，可以忽略区号查phone
        List<Long> esIds = talentService.searchTalentsIdByContactAndSimilarity(talentDTOV3).getBody();
        List<String> checkContacts = contactDTOList.stream().map(TalentContactDTO::getContact).distinct().toList();
        //先将疑似的联系方式都查出来，后续再区分是否混合查的数据
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTenantIdAndStatusAndTypeInAndContactInAndVerificationStatusIsNot(tenantId, AVAILABLE, checkContactTypes, checkContacts);
        List<Long> mysqlIds = new ArrayList<>();
        //将查重的联系方式分组
        Map<String, List<ContactType>> contactDTOMap = contactDTOList.stream()
                .collect(Collectors.groupingBy(TalentContactDTO::getContact,
                        Collectors.mapping(TalentContactDTO::getType, Collectors.toList())));

        for (TalentContact item : talentContactList) {
            //已经查重到了的候选人不再查重
            if (mysqlIds.contains(item.getTalentId())) {
                continue;
            }
            if (contactDTOMap.containsKey(item.getContact())) {
                List<ContactType> types = contactDTOMap.getOrDefault(item.getContact(), new ArrayList<>());
                //如果查重的联系方式contact & type都相同 或者 是交叉查重到的数据即查重成功
                if (types.contains(item.getType()) || (crossCheckContactTypes.contains(item.getType()) && !CollUtil.intersection(crossCheckContactTypes, types).isEmpty())) {
                    mysqlIds.add(item.getTalentId());
                }
            }
        }

        if (CollUtil.isNotEmpty(esIds)) {
            allIds.addAll(esIds);
        }
        if (CollUtil.isNotEmpty(mysqlIds)) {
            allIds.addAll(mysqlIds);
        }
        log.info("checkTalentContactInformation, talentDTOV3:{}, esIds:{}, mysqlIds:{}, allIds:{}", talentDTOV3, esIds, mysqlIds, allIds);
        return allIds.stream().toList();
    }

//    private void updateApproverActive(SalesLeadClientContact contact) {
//        // 当approver关联的联系人都为inactive，那么将该approver的状态置为inactive
//        final Integer countActiveClient = salesLeadClientContactRepository.countActiveClient(contact.getApproverId());
//        if (countActiveClient <= 1) {
//            TimeSheetUser timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.getTimeSheetUserById(contact.getApproverId()).getBody());
//            if (null != timeSheetUser) {
//                contact.setInactived(Boolean.TRUE);
//                timeSheetUser.setActivated(false);
//                jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
//                salesLeadClientContactRepository.updateInactivedByApproverIdAndTenantId(Boolean.TRUE, timeSheetUser.getId(), SecurityUtils.getTenantId());
//            }
//        }
//    }

    @Override
    public List<SalesLeadClientContact> searchSalesLeadClientContact(CompanyContactSearchDTO companyContactSearchDTO, HttpHeaders headers) {
        List<SalesLeadClientContact> salesLeadClientContacts = salesLeadClientContactRepository.findAllByTenantIdAndCompanyId(SecurityUtils.getTenantId(), companyContactSearchDTO.getCompanyId());
        Set<Long> validTalentIds = talentService.getValidTalents(salesLeadClientContacts.stream().map(SalesLeadClientContact::getTalentId).toList()).getBody();
        log.info("validTalentIds size=" + validTalentIds.size());
        log.info("validTalentIds=" + validTalentIds);
        headers.set("Pagination-Count", String.valueOf(salesLeadClientContacts.size()));
        headers.set("Pagination-View-Count", String.valueOf(validTalentIds.size()));
        return salesLeadClientContacts.stream().filter(c -> validTalentIds.contains(c.getTalentId())).toList();
    }

    @Override
    public List<SalesLeadClientContactVO> toVo(List<SalesLeadClientContact> salesLeadClientContactList, HttpHeaders headers) {
        if (CollUtil.isEmpty(salesLeadClientContactList)) {
            return Collections.emptyList();
        }
        // 初始化结果列表
        List<SalesLeadClientContactVO> result = salesLeadClientContactList.stream().map(SalesLeadClientContactVO::fromSalesLeadClientContact).collect(Collectors.toList());
        // 提取所需 ID 列表
        List<Long> talentIdList = salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).distinct().collect(Collectors.toList());
        List<Long> clientContactIdsList = salesLeadClientContactList.stream().map(SalesLeadClientContact::getId).distinct().collect(Collectors.toList());
        List<Long> contactIdsList = salesLeadClientContactList.stream().map(SalesLeadClientContact::getCrmContactId).distinct().collect(Collectors.toList());

        // 异步任务初始化
        var talentNameMapFuture = CompletableFuture.supplyAsync(() ->
                talentServiceRepository.findTalentNameByIds(talentIdList).stream().collect(Collectors.toMap(EntityNameVM::getId, o -> o)), executor);

        var talentContactMapFuture = CompletableFuture.supplyAsync(() -> {
            List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndTypeInAndStatusAndVerificationStatusOrderBySortAsc(talentIdList,
                    Arrays.asList(ContactType.EMAIL, ContactType.PHONE), TalentContactStatus.AVAILABLE);
            return talentContactList.stream().collect(Collectors.groupingBy(TalentContact::getTalentId));
        }, executor);

        var companyNameFuture = CompletableFuture.supplyAsync(() -> companyRepository.getCompanyNameById(salesLeadClientContactList.get(0).getCompanyId()), executor);

        var contactTagRelationFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> ids = salesLeadClientContactList.stream().map(SalesLeadClientContact::getCrmContactId).collect(Collectors.toList());
            Long companyId = salesLeadClientContactList.get(0).getCompanyId();
            List<ContactTagRelation> contactTagRelationList = contactTagRelationRepository.findAllByAccountCompanyIdAndContactIdIn(companyId, ids);
            return contactTagRelationList.stream().collect(Collectors.groupingBy(ContactTagRelation::getContactId));
        }, executor);

        var talentContactOwnershipFuture = CompletableFuture.supplyAsync(() -> {
            var talentOwnershipList = talentOwnershipCompanyBriefRepository.findAllByTalentIdIn(talentIdList);
            return talentOwnershipList;
        }, executor);

        var contactServiceTypeFuture = CompletableFuture.supplyAsync(() -> {
            return contactServiceTypeRelationRepository.findAllByContactIdIn(contactIdsList);
        }, executor);

        var businessFuture = CompletableFuture.supplyAsync(() -> {
            var accountBusinessNameList = accountBusinessContactRelationRepository.findAllByClientContactIdIn(clientContactIdsList);
            Map<Long, List<AccountBusinessNameDTO>> clientContactToBusinessMap = accountBusinessNameList.stream().collect(Collectors.groupingBy(AccountBusinessNameDTO::getClientContactId));
            return clientContactToBusinessMap;
        }, executor);


        // 等待所有异步任务完成
        CompletableFuture.allOf(talentNameMapFuture, talentContactMapFuture, companyNameFuture, contactTagRelationFuture, talentContactOwnershipFuture, contactServiceTypeFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching SalesLeadClientContact data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching SalesLeadClientContact data");
                }).join();

        // 获取异步任务结果
        Map<Long, EntityNameVM> talentNameMap = talentNameMapFuture.join();
        Map<Long, List<TalentContact>> talentContactMap = talentContactMapFuture.join();
        String companyName = companyNameFuture.join();
        Map<Long, List<ContactTagRelation>> contactTagRelationMap = contactTagRelationFuture.join();
        List<TalentOwnership> talentOwnershipList = talentContactOwnershipFuture.join();
        Map<Long, List<AccountBusinessNameDTO>> clientContactToBusinessMap = businessFuture.join();
        Map<Long, List<ContactServiceTypeRelation>> serviceTypeMap = contactServiceTypeFuture.join().stream()
                .collect(Collectors.groupingBy(ContactServiceTypeRelation::getContactId));

        // 构建 TalentOwnershipMap 和 UserMap
        //todo: combine this into competeableFuture
        Map<Long, List<TalentOwnership>> talentOwnershipMap = talentOwnershipList.stream()
                .collect(Collectors.groupingBy(TalentOwnership::getTalentId));
        List<Long> userIds = talentOwnershipList.stream()
                .filter(talentOwnership -> TalentOwnershipType.TALENT_OWNER.equals(talentOwnership.getOwnershipType()) || TalentOwnershipType.SHARE.equals(talentOwnership.getOwnershipType()))
                .map(TalentOwnership::getUserId).toList();
        Map<Long, UserBriefDTO> userMap = userService.getAllBriefUsersByIds(userIds).getBody().stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));


        // 填充结果数据
        result.forEach(vo -> populateVo(vo, talentNameMap, talentContactMap, companyName, contactTagRelationMap,
                talentOwnershipMap, userMap, clientContactToBusinessMap, serviceTypeMap));
        return result;
    }

    private void populateVo(SalesLeadClientContactVO vo, Map<Long, EntityNameVM> talentNameMap,
                            Map<Long, List<TalentContact>> talentContactMap, String companyName,
                            Map<Long, List<ContactTagRelation>> contactTagRelationMap,
                            Map<Long, List<TalentOwnership>> talentOwnershipMap,
                            Map<Long, UserBriefDTO> userMap,
                            Map<Long, List<AccountBusinessNameDTO>> clientContactToBusinessMap,
                            Map<Long, List<ContactServiceTypeRelation>> serviceTypeMap) {

        // 设置 Talent 相关信息
        if (vo.getTalentId() != null && talentNameMap.containsKey(vo.getTalentId())) {
            EntityNameVM talentNameEntity = talentNameMap.get(vo.getTalentId());
            vo.setFirstName(talentNameEntity.getFirstName());
            vo.setLastName(talentNameEntity.getLastName());
            vo.setName(CommonUtils.formatFullName(talentNameEntity.getFirstName(), talentNameEntity.getLastName()));
        }

        // 设置联系方式标志
        if (vo.getTalentId() != null && talentContactMap.containsKey(vo.getTalentId())) {
            Set<ContactType> contactTypes = talentContactMap.get(vo.getTalentId()).stream()
                    .map(TalentContact::getType)
                    .collect(Collectors.toSet());
            vo.setHasEmail(contactTypes.contains(ContactType.EMAIL));
            vo.setHasPhone(contactTypes.contains(ContactType.PHONE));
            vo.setHasWechat(contactTypes.contains(ContactType.WECHAT));
            vo.setHasLinkedIn(contactTypes.contains(ContactType.LINKEDIN));
            vo.setHasWhatsApp(contactTypes.contains(ContactType.WHATSAPP));
        }

        // 设置公司名称
        vo.setCompany(companyName);

        // 设置标签信息
        if (vo.getContactId() != null && contactTagRelationMap.containsKey(vo.getContactId())) {
            vo.setTags(contactTagRelationMap.get(vo.getContactId()).stream()
                    .map(t -> new TagVO(t.getTag(), t.getPermissionUserId(), false))
                    .distinct()
                    .toList());
        }

        // 设置可见性和共享状态
        vo.setIsViewable(Boolean.FALSE);
        vo.setShareToAll(Boolean.FALSE);
        if (vo.getTalentId() != null && talentOwnershipMap.containsKey(vo.getTalentId())) {
            List<TalentOwnership> currentUserOwnershipList = talentOwnershipMap.get(vo.getTalentId());

            boolean isShareToAll = currentUserOwnershipList.stream()
                    .anyMatch(t -> TalentOwnershipType.TENANT_SHARE.equals(t.getOwnershipType()));

            vo.setShareToAll(isShareToAll);

            vo.setContactOwners(currentUserOwnershipList.stream()
                    .filter(t -> TalentOwnershipType.TALENT_OWNER.equals(t.getOwnershipType()))
                    .map(t -> new ContactOwnershipVO(t.getUserId(), Optional.ofNullable(userMap.get(t.getUserId())).map(UserBriefDTO::getFullName).orElse(null)))
                    .toList());

            vo.setContactShareUsers(currentUserOwnershipList.stream()
                    .filter(t -> TalentOwnershipType.SHARE.equals(t.getOwnershipType()))
                    .map(t -> new ContactOwnershipVO(t.getUserId(), Optional.ofNullable(userMap.get(t.getUserId())).map(UserBriefDTO::getFullName).orElse(null)))
                    .toList());
        }

        // 设置业务信息
        if (vo.getId() != null && clientContactToBusinessMap.containsKey(vo.getId())) {
            vo.setAccountBusinesses(clientContactToBusinessMap.get(vo.getId()).stream()
                    .map(dto -> new AccountBusinessNameBriefVO(dto.getBusinessId(), dto.getName()))
                    .toList());
        }

        // 设置服务类型信息
        if (vo.getContactId() != null && serviceTypeMap.containsKey(vo.getContactId())) {
            vo.setAssociatedServiceTypes(serviceTypeMap.get(vo.getContactId()).stream()
                    .map(ContactServiceTypeRelation::getServiceTypeId)
                    .distinct()
                    .toList());
        }
    }

    @Override
    public List<SalesLeadClientContactVO> searchTenantSalesLeadClientContact(CompanyContactTenantSearchDTO companyContactTenantSearchDTO) {
        Pageable pageable = PageRequest.of(0, companyContactTenantSearchDTO.getLimit());
        List<CompanyContactVM> contactVMList;
        if (companyContactTenantSearchDTO.getActive() != null && companyContactTenantSearchDTO.getName() != null) {
            contactVMList = salesLeadClientContactRepository.findByTenantIdAndActiveAndNameLikeWithLimit(SecurityUtils.getTenantId(), companyContactTenantSearchDTO.getActive(), companyContactTenantSearchDTO.getName(), pageable);
        } else if (companyContactTenantSearchDTO.getActive() != null) {
            contactVMList = salesLeadClientContactRepository.findByTenantIdAndActiveWithLimit(SecurityUtils.getTenantId(), companyContactTenantSearchDTO.getActive(), pageable);
        } else if (companyContactTenantSearchDTO.getName() != null) {
            contactVMList = salesLeadClientContactRepository.findByTenantIdAndNameLikeWithLimit(SecurityUtils.getTenantId(), companyContactTenantSearchDTO.getName(), pageable);
        } else {
            contactVMList = salesLeadClientContactRepository.findByTenantIdWithLimit(SecurityUtils.getTenantId(), pageable);
        }
        return contactVMList.stream().map(SalesLeadClientContactVO::fromCompanyContactVM).collect(Collectors.toList());
    }

    @Override
    public List<SalesLeadClientContactVO> queryTenantSalesLeadClientContactList(Boolean active) {
        if (active == null) {
            return salesLeadClientContactRepository.findAllByTenantId(SecurityUtils.getTenantId(), SecurityUtils.getUserId(), SecurityUtils.getUserUid()).stream().map(SalesLeadClientContactVO::fromCompanyContactVM).collect(Collectors.toList());
        } else {
            return salesLeadClientContactRepository.findAllByTenantIdAndActive(SecurityUtils.getTenantId(), active, SecurityUtils.getUserId(), SecurityUtils.getUserUid()).stream().map(SalesLeadClientContactVO::fromCompanyContactVM).collect(Collectors.toList());
        }
    }

    @Override
    public List<CompanyContactVO> checkContactDuplicatedTalent(CompanyContactCheckDuplicatedTalentDTO companyContactCheckDuplicatedTalentDTO) throws IOException {
        List<CompanyContactVO> result = new ArrayList<>();
        List<Long> talentIds = checkTalentContactInformation(companyContactCheckDuplicatedTalentDTO.getContacts(), companyContactCheckDuplicatedTalentDTO.getLinkedinProfile(), SecurityUtils.getTenantId());
        if (CollUtil.isNotEmpty(talentIds)) {
            List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllByTalentIdInAndActive(talentIds, true);
            Map<Long, List<SalesLeadClientContact>> clientContactMap = clientContactList.stream().collect(Collectors.groupingBy(SalesLeadClientContact::getTalentId));
            talentIds.forEach(o -> {
                CompanyContactVO companyContactVO = new CompanyContactVO();
                companyContactVO.setTalentId(o);
                if (clientContactMap.containsKey(o)) {
                    companyContactVO.setCompanyIds(clientContactMap.get(o).stream().map(SalesLeadClientContact::getCompanyId).distinct().collect(Collectors.toList()));
                }
                result.add(companyContactVO);
            });
        }
        return result;
    }

    @Override
    public SalesLeadClientContactVO findById(Long id, HttpHeaders headers) {
        log.info("Request to get salesLeadClientContactVM by Id: {}", id);
        SalesLeadClientContact salesLeadClientContact = salesLeadClientContactRepository.findById(id).orElseThrow(() -> new NotFoundException("company contact does not exists."));
        checkCompanyPermission(salesLeadClientContact.getCompanyId());
        SalesLeadClientContactVO salesLeadClientContactVO = SalesLeadClientContactVO.fromSalesLeadClientContact(salesLeadClientContact);
        toVo(salesLeadClientContactVO, headers);
        return salesLeadClientContactVO;
    }

    private void toVo(SalesLeadClientContactVO salesLeadClientContactVO, HttpHeaders headers) {
        if (salesLeadClientContactVO.getTalentId() == null || salesLeadClientContactVO.getTalentId() == 0L) {
            return;
        }
        //改为异步查询
        CompletableFuture<TalentCompanyBrief> talentCompanyBriefFuture = CompletableFuture.supplyAsync(() ->
                talentCompanyBriefRepository.findById(salesLeadClientContactVO.getTalentId())
                        .orElseThrow(() -> new NotFoundException("talent contact does not exists."))
        );

        CompletableFuture<List<TalentContact>> talentContactListFuture = CompletableFuture.supplyAsync(() ->
                talentContactCompanyBriefRepository.findAllByTalentIdAndStatusOrderBySortAsc(
                        salesLeadClientContactVO.getTalentId(), TalentContactStatus.AVAILABLE)
        );

        CompletableFuture<TalentCurrentLocationCompanyBrief> talentCurrentLocationFuture = CompletableFuture.supplyAsync(() ->
                talentCurrentLocationCompanyBriefRepository.findByTalentId(salesLeadClientContactVO.getTalentId())
        );

        CompletableFuture<List<TalentOwnership>> talentOwnershipListFuture = CompletableFuture.supplyAsync(() ->
                talentOwnershipCompanyBriefRepository.findAllByTalentId(salesLeadClientContactVO.getTalentId())
        );

        //合并查询结果
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(
                talentCompanyBriefFuture,
                talentContactListFuture,
                talentCurrentLocationFuture,
                talentOwnershipListFuture
        );

        // 异常处理
        try {
            combinedFuture.join();
        } catch (CompletionException e) {
            if (e.getCause() instanceof NotFoundException) {
                throw (NotFoundException) e.getCause();
            }
            throw new RuntimeException("Error fetching data", e);
        }

        // 获取查询结果
        TalentCompanyBrief talentCompanyBrief = talentCompanyBriefFuture.join();
        List<TalentContact> talentContactList = talentContactListFuture.join();
        TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationFuture.join();
        List<TalentOwnership> talentOwnershipList = talentOwnershipListFuture.join();

        //次级查询
        CompletableFuture<List<AccountBusinessNameDTO>> accountBusinessFuture = CompletableFuture.supplyAsync(() ->
                accountBusinessContactRelationRepository.findAllByClientContactIdIn(List.of(salesLeadClientContactVO.getId()))
        );

        // 处理同步逻辑
        Map<Integer, List<TalentContact>> talentContactMap = talentContactList.stream()
                .collect(Collectors.groupingBy(o -> o.getType().toDbValue()));

        if (!isTalentContactViewable(talentCompanyBrief, talentOwnershipList)) {
            throw new ForbiddenException("You are not allow to view current talent contact");
        }

        // 等待次級查詢完成
        List<AccountBusinessNameDTO> accountBusinessNameList = accountBusinessFuture.join();

        // 後續處理邏輯
        Map<Long, List<AccountBusinessNameDTO>> clientContactToBusinessMap = accountBusinessNameList.stream()
                .collect(Collectors.groupingBy(AccountBusinessNameDTO::getClientContactId));

        setContactAdditionalInfo(salesLeadClientContactVO);
        setContactTitle(salesLeadClientContactVO, talentCompanyBrief);
        setContactDepartment(salesLeadClientContactVO, talentCompanyBrief);
        salesLeadClientContactVO.setFirstName(talentCompanyBrief.getFirstName());
        salesLeadClientContactVO.setLastName(talentCompanyBrief.getLastName());
        salesLeadClientContactVO.setName(talentCompanyBrief.getFullName());
        if (talentCurrentLocationCompanyBrief != null) {
            LocationDTO locationDTO = JSONUtil.toBean(talentCurrentLocationCompanyBrief.getOriginalLoc(), LocationDTO.class);
            if (ObjectUtil.isEmpty(locationDTO.getZipcode())) {
                locationDTO.setZipcode(talentCurrentLocationCompanyBrief.getZipCode());
            }
            salesLeadClientContactVO.setCompanyLocation(locationDTO);
        }
        List<TalentContactDTO> contactList = talentContactList.stream().map(TalentContactDTO::fromTalentContact).collect(Collectors.toList());
        addApproverTagToContact(contactList);
        salesLeadClientContactVO.setContacts(contactList);
        salesLeadClientContactVO.setTags(getContactTags(salesLeadClientContactVO.getCompanyId(), salesLeadClientContactVO.getContactId(), headers));

        //ownership
        Map<Long, TalentOwnership> ownershipMap = talentOwnershipList.stream()
                .filter(talentOwnership -> TALENT_OWNER.equals(talentOwnership.getOwnershipType()) || SHARE.equals(talentOwnership.getOwnershipType()))
                .collect(Collectors.toMap(TalentOwnership::getUserId, Function.identity(), (a, b) -> a));
        List<UserBriefDTO> userBriefs = userService.getAllBriefUsersByIds(ownershipMap.keySet().stream().toList()).getBody();
        Map<Long, UserBriefDTO> userMap = userBriefs == null ? new HashMap<>() : userBriefs.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
        boolean shareToAll = talentOwnershipList.stream().anyMatch(ownership -> TalentOwnershipType.TENANT_SHARE.equals(ownership.getOwnershipType()));
        salesLeadClientContactVO.setShareToAll(shareToAll);
        List<ContactOwnershipVO> ownerList = new ArrayList<>();
        List<ContactOwnershipVO> shareList = new ArrayList<>();

        ownershipMap.forEach((ownerId, talentOwnership) -> {
            if (!userMap.containsKey(ownerId)) {
                return;
            }
            if (TalentOwnershipType.TALENT_OWNER.equals(talentOwnership.getOwnershipType())) {
                ownerList.add(new ContactOwnershipVO(talentOwnership.getUserId(), userMap.get(talentOwnership.getUserId()).getFullName()));
            } else if (TalentOwnershipType.SHARE.equals(talentOwnership.getOwnershipType())) {
                shareList.add(new ContactOwnershipVO(talentOwnership.getUserId(), userMap.get(talentOwnership.getUserId()).getFullName(), talentOwnership.getAutoAssigned() == null ? Boolean.FALSE : talentOwnership.getAutoAssigned()));
            }
        });
        salesLeadClientContactVO.setContactOwners(ownerList);
        if (!shareList.isEmpty()) {
            salesLeadClientContactVO.setContactShareUsers(shareList);
        }
        if (!accountBusinessNameList.isEmpty()) {
            salesLeadClientContactVO.setAccountBusinesses(accountBusinessNameList.stream().map(dto -> new AccountBusinessNameBriefVO(dto.getBusinessId(), dto.getName())).toList());
            salesLeadClientContactVO.setAccountBusiness(accountBusinessNameList.stream().map(AccountBusinessNameDTO::getBusinessId).toList());
        }


        if (talentContactMap.containsKey(ContactType.LINKEDIN.toDbValue())) {
            salesLeadClientContactVO.setLinkedinProfile(talentContactMap.get(ContactType.LINKEDIN.toDbValue()).stream().map(TalentContact::getDetails).collect(Collectors.toList()));
        } else {
            salesLeadClientContactVO.setLinkedinProfile(null);
        }

        // 异步处理服务类型
        Long contactId = salesLeadClientContactVO.getContactId();
        CompletableFuture<List<ContactServiceTypeRelation>> serviceTypeFuture = CompletableFuture.supplyAsync(() -> {
            if (contactId != null) {
                return contactServiceTypeRelationRepository.findAllByContactId(contactId);
            }
            return Collections.emptyList();
        });

        // 获取服务类型结果
        List<ContactServiceTypeRelation> serviceTypeRelationList = serviceTypeFuture.join();
        if (CollUtil.isNotEmpty(serviceTypeRelationList)) {
            salesLeadClientContactVO.setAssociatedServiceTypes(
                    serviceTypeRelationList.stream()
                            .map(ContactServiceTypeRelation::getServiceTypeId)
                            .collect(Collectors.toList())
            );
        }
    }

    private Boolean isTalentContactViewable(TalentCompanyBrief talentCompanyBrief, List<TalentOwnership> talentOwnershipList) {

        //admin
        if (SecurityUtils.isAdmin()) {
            return true;
        }
        //是创建人
        if(SecurityUtils.getUserId().equals(talentCompanyBrief.getPermissionUserId())) {
            return true;
        }

        Set<Long> viewableUserIdSet = viewableUserSetForClientContact();
        if (viewableUserIdSet.contains(-1L)) {
            return true;
        }

        log.info("viewableUserIdSet=" + viewableUserIdSet);
        //share with all user
        boolean shareToAll = talentOwnershipList.stream()
                .anyMatch(owner -> TalentOwnershipType.TENANT_SHARE.equals(owner.getOwnershipType()));
        if (shareToAll) {
            return true;
        }

        // current user is team permission and owner is under current user's team
        boolean isCurrentUserUnderTeamViewable = talentOwnershipList.stream()
                .filter(talentOwnership -> TalentOwnershipType.TALENT_OWNER.equals(talentOwnership.getOwnershipType()))
                .map(TalentOwnership::getUserId)
                .anyMatch(viewableUserIdSet::contains);
        if (isCurrentUserUnderTeamViewable) {
            return true;
        }

        //和创建人同团队
        if (viewableUserIdSet.contains(talentCompanyBrief.getPermissionUserId())) {
            return true;
        }

        // share with current user
        return talentOwnershipList.stream()
                .anyMatch(talentOwnership -> TalentOwnershipType.SHARE.equals(talentOwnership.getOwnershipType()) && talentOwnership.getUserId().equals(SecurityUtils.getUserId()));

    }

    @Deprecated
    private Set<Long> viewableUserSet() {

        //Map<Long, Set<Long>> viewableOwnerUserMap = new HashMap<>();
        Set<Long> viewableOwnerUserSet = new HashSet<>();
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }
        if (teamDataPermission.getAll()) {
            return Set.of(-1L);
        }

        if (CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            return userService.findUserIdsInSameTeam(new ArrayList<>(teamDataPermission.getReadableTeamIds())).getBody();
        }

        return viewableOwnerUserSet;

    }

    private Set<Long> viewableUserSetForClientContact() {

        //Map<Long, Set<Long>> viewableOwnerUserMap = new HashMap<>();
        Set<Long> viewableOwnerUserSet = new HashSet<>();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        log.info("DataPermission (user: {}) = {}", SecurityUtils.getUserId(), teamDataPermission);
        if (teamDataPermission.getAll()) {
            return Set.of(-1L);
        }
        if (teamDataPermission.getSelf()){
            return Set.of(SecurityUtils.getUserId());
        }

        if (CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            return userService.findUserIdsInSameTeam(new ArrayList<>(teamDataPermission.getReadableTeamIds())).getBody();
        }

        return viewableOwnerUserSet;
    }

    private void setContactAdditionalInfo(SalesLeadClientContactVO salesLeadClientContactVO) {
        SalesLeadClientContactAdditionalInfo salesLeadClientContactAdditionalInfo = salesLeadClientContactAddtionalInfoRepository.findByCompanyContactId(salesLeadClientContactVO.getId());
        if (salesLeadClientContactAdditionalInfo != null) {
            SalesLeadClientContactVO additionalInfoVo = JSONUtil.toBean(salesLeadClientContactAdditionalInfo.getExtendedInfo(), SalesLeadClientContactVO.class);
            ServiceUtils.myCopyProperties(additionalInfoVo, salesLeadClientContactVO);
        }
    }

    private void setContactTitle(SalesLeadClientContactVO salesLeadClientContactVO, TalentCompanyBrief talentCompanyBrief) {
        if (talentCompanyBrief.getTalentAdditionalInfo() != null && talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo() != null) {
            JSONObject talentAdditionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
            if (talentAdditionalInfo.containsKey(KEY_EXPERIENCES)) {
                List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(JSONUtil.parseArray(talentAdditionalInfo.get(KEY_EXPERIENCES)), TalentExperienceDTO.class);
                for (TalentExperienceDTO talentExperienceDTO : talentExperienceDTOList) {
                    if (talentExperienceDTO.getTitle() == null) {
                        continue;
                    }
                    if (salesLeadClientContactVO.getActive().equals(Boolean.TRUE) && talentExperienceDTO.getActiveCompanyId() != null && talentExperienceDTO.getActiveCompanyId().equals(salesLeadClientContactVO.getCompanyId())) {
                        salesLeadClientContactVO.setTitle(talentExperienceDTO.getTitle());
                        break;
                    }
                    if (salesLeadClientContactVO.getActive().equals(Boolean.FALSE) && talentExperienceDTO.getCompanyId() != null && talentExperienceDTO.getCompanyId().equals(salesLeadClientContactVO.getCompanyId())) {
                        salesLeadClientContactVO.setTitle(talentExperienceDTO.getTitle());
                        break;
                    }
                }
            }
        }
    }

    private void setContactDepartment(SalesLeadClientContactVO salesLeadClientContactVO, TalentCompanyBrief talentCompanyBrief) {
        if (talentCompanyBrief.getTalentAdditionalInfo() != null && talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo() != null) {
            JSONObject talentAdditionalInfo = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
            if (talentAdditionalInfo.containsKey(KEY_EXPERIENCES)) {
                List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(JSONUtil.parseArray(talentAdditionalInfo.get(KEY_EXPERIENCES)), TalentExperienceDTO.class);
                for (TalentExperienceDTO talentExperienceDTO : talentExperienceDTOList) {
                    if (talentExperienceDTO.getDepartment() == null) {
                        continue;
                    }
                    if (salesLeadClientContactVO.getActive().equals(Boolean.TRUE) && talentExperienceDTO.getActiveCompanyId() != null && talentExperienceDTO.getActiveCompanyId().equals(salesLeadClientContactVO.getCompanyId())) {
                        salesLeadClientContactVO.setDepartment(talentExperienceDTO.getDepartment());
                        break;
                    }
                    if (salesLeadClientContactVO.getActive().equals(Boolean.FALSE) && talentExperienceDTO.getCompanyId() != null && talentExperienceDTO.getCompanyId().equals(salesLeadClientContactVO.getCompanyId())) {
                        salesLeadClientContactVO.setDepartment(talentExperienceDTO.getDepartment());
                        break;
                    }
                }
            }
        }
    }


    private void updateTalentName(TalentCompanyBrief talentCompanyBrief, String firstName, String lastName) {
        talentCompanyBrief.setFirstName(firstName);
        talentCompanyBrief.setLastName(lastName);
        talentCompanyBrief.setFullName(CommonUtils.formatFullName(firstName, lastName));
    }

    private Long getNewExperienceId(List<TalentExperienceDTO> talentExperienceDTOList) {
        Optional<Long> maxIdOptional = talentExperienceDTOList
                .stream()
                .map(TalentExperienceDTO::getId)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder());

        return maxIdOptional.map(maxId -> maxId + 1).orElse(0L);
    }

    private void updateTalentContact(TalentCompanyBrief talent, List<TalentContactDTO> contacts, List<String> linkedinProfile, Long companyId) {
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdAndStatusOrderBySortAsc(talent.getId(), TalentContactStatus.AVAILABLE);
        Set<String> existContactMap = talentContactList.stream().map(o -> {
            if (!ContactType.LINKEDIN.equals(o.getType())) {
                return o.getContact();
            } else {
                return o.getDetails();
            }
        }).collect(Collectors.toSet());
        List<TalentContact> addTalentContactList = new ArrayList<>();
        contacts.stream().filter(item -> !existContactMap.contains(item.getContact())).forEach(o -> {
            if (ContactType.EMAIL.equals(o.getType()) || ContactType.PHONE.equals(o.getType())) {
                addTalentContactList.add(new TalentContact(o.getType(), o.getContact(), talent.getId(), SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 0));
            } else {
                addTalentContactList.add(new TalentContact(o.getType(), o.getContact(), talent.getId(), SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1));
            }
        });
        if (CollUtil.isNotEmpty(linkedinProfile)) {
            linkedinProfile.forEach(item -> {
                if (!existContactMap.contains(item) && getLinkedinContact(item) != null) {
                    TalentContact talentContact = new TalentContact(ContactType.LINKEDIN, getLinkedinContact(item), talent.getId(), SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1);
                    talentContact.setDetails(item);
                    addTalentContactList.add(talentContact);
                }
            });
        }
        try {
            if (CollUtil.isNotEmpty(addTalentContactList)) {
                talentContactCompanyBriefRepository.saveAll(addTalentContactList);
                talentCompanyBriefRepository.updateTalentCompanyBriefOwnedByTenantsByTalentId(talent.getId(), SecurityUtils.getTenantId());
            }
        } catch (Exception e) {
            log.error("talent contact update error :{}, contact: {}. companyId:{}", e.getMessage(), addTalentContactList, companyId);
        }
    }

    private void updateTalentLocation(Long talentId, Long locationId) {
        if (talentId == null) {
            return;
        }
        if (locationId != null) {
            CompanyLocation companyLocation = companyLocationRepository.findById(locationId).orElse(null);
            if (companyLocation == null) {
                return;
            }
            TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationCompanyBriefRepository.findByTalentId(talentId);
            if (talentCurrentLocationCompanyBrief == null) {
                talentCurrentLocationCompanyBriefRepository.save(TalentCurrentLocationCompanyBrief.fromCompanyLocation(companyLocation).setTalentId(talentId));
            } else {
                ServiceUtils.myCopyProperties(companyLocation, talentCurrentLocationCompanyBrief, TalentCurrentLocationCompanyBrief.UpdateSkipProperties);
                talentCurrentLocationCompanyBriefRepository.save(talentCurrentLocationCompanyBrief);
            }
        } else {
            TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationCompanyBriefRepository.findByTalentId(talentId);
            if (talentCurrentLocationCompanyBrief != null) {
                talentCurrentLocationCompanyBrief.setOriginalLoc("{}");
                talentCurrentLocationCompanyBriefRepository.save(talentCurrentLocationCompanyBrief);
            }
        }

    }

    private List<TalentContact> getTalentContactList(List<TalentContactDTO> contacts, List<String> linkedin, Long talentId) {
        List<TalentContact> contactList = new ArrayList<>();
        contacts.forEach(o -> {
            if (ContactType.EMAIL.equals(o.getType()) || ContactType.PHONE.equals(o.getType())) {
                contactList.add(new TalentContact(o.getType(), o.getContact(), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 0));
            } else {
                contactList.add(new TalentContact(o.getType(), o.getContact(), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1));
            }
        });
        if (CollUtil.isNotEmpty(linkedin)) {
            linkedin.forEach(o -> {
                if (getLinkedinContact(o) != null) {
                    TalentContact talentContact = new TalentContact(ContactType.LINKEDIN, getLinkedinContact(o), talentId, SecurityUtils.getTenantId(), TalentContactStatus.AVAILABLE, 1);
                    talentContact.setDetails(o);
                    contactList.add(talentContact);
                }
            });
        }
        return contactList;
    }

    private String getLinkedinContact(String linkedinProfile) {
        String linkedInIDRegex = "(?:https?:\\/\\/)?(?:(?:www|[a-z]{2})\\.)?linkedin\\.com\\/(?:in|talent\\/profile|public-profile\\/in|chatin\\/wnc\\/in|mwlite\\/in)\\/([^^^\\/ :?？=—*&!！`$)(）（<>©|}{@#]{3,900})";
        Pattern linkedInIDPattern = Pattern.compile(linkedInIDRegex);
        Matcher linkedInMatcher = linkedInIDPattern.matcher(linkedinProfile);

        if (linkedInMatcher.find()) {
            return linkedInMatcher.group(1);
        } else {
            return null;
        }
    }


    private void formatName(TalentCompanyBrief talentCompanyBrief) {
        String firstName = firstLetterName(talentCompanyBrief.getFirstName().trim());
        String lastName = firstLetterName(talentCompanyBrief.getLastName().trim());
        if (firstName.equals(lastName)) {
            talentCompanyBrief.setFullName(firstName);
        } else {
            talentCompanyBrief.setFullName(CommonUtils.formatFullName(firstName, lastName));
        }
    }

    public static boolean isChinese(String str) {
        String regEx = "[\\u4e00-\\u9fa5]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static String firstLetterName(String name) {
        name = name.substring(0, 1).toUpperCase() + name.substring(1);
        return name;
    }

    private void sendEmailAdd(String email, String password, String contactName) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            String loginUrl = applicationProperties.getJobDivaUrl() + "/login";
            String subject = "HiTalent New Account Activated";
            StringBuilder sb = new StringBuilder();
            sb.append("<body>");
            HtmlUtil.appendParagraphCell(sb, "Hi " + contactName + "：");
            HtmlUtil.appendParagraphCell(sb, "Welcome to HiTalent! Your HiTalent account has been activated:");
            HtmlUtil.appendParagraphCell(sb, "<br/>");
            HtmlUtil.appendParagraphCell(sb, "You can Click <a href='" + loginUrl + "'>here</a> to login, or copy/paste the following URL in your browser.");
            HtmlUtil.appendParagraphCell(sb, applicationProperties.getJobDivaUrl());
            HtmlUtil.appendParagraphCell(sb, "<br/>");
            HtmlUtil.appendParagraphCell(sb, "Account:" + email);
            HtmlUtil.appendParagraphCell(sb, "Temporary Password:" + password);
            HtmlUtil.appendParagraphCell(sb, "<br/><br/>");
            HtmlUtil.appendParagraphCell(sb, "Thank you,");
            HtmlUtil.appendParagraphCell(sb, "HiTalent Customer Support");
            sb.append("</body>");
            //add email content
            MailVM mailVM = new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(email), null, null
                    , subject, sb.toString(), null, true);
            mailService.sendHtmlMail(mailVM);
        });
    }

    private void sendEmailChange(String email, String contactName) {
        String loginUrl = applicationProperties.getJobDivaUrl() + "/login";
        String subject = "HiTalent Account Changed";
        StringBuilder sb = new StringBuilder();
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Hi " + contactName + "：");
        HtmlUtil.appendParagraphCell(sb, "Welcome to HiTalent! Your HiTalent account has been changed:");
        HtmlUtil.appendParagraphCell(sb, "Account:" + email);
        HtmlUtil.appendParagraphCell(sb, "<br/>");
        HtmlUtil.appendParagraphCell(sb, "You can Click <a href='" + loginUrl + "'>here</a> to login, or copy/paste the following URL in your browser.");
        HtmlUtil.appendParagraphCell(sb, applicationProperties.getJobDivaUrl());
        HtmlUtil.appendParagraphCell(sb, "<br/><br/>");
        HtmlUtil.appendParagraphCell(sb, "Thank you,");
        HtmlUtil.appendParagraphCell(sb, "HiTalent Customer Support");
        sb.append("</body>");
        MailVM mailVM = new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(email), null, null
                , subject, sb.toString(), null, true);
        mailService.sendHtmlMail(mailVM);
    }

    @Override
    public ApproverVO approver(ApproverDTO approverDTO) {
        ApproverVO approverVO = new ApproverVO();
        SalesLeadClientContact contact = salesLeadClientContactRepository.getById(approverDTO.getContactId());
        if (ObjectUtil.isEmpty(contact) || contact.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_CLIENTCONTACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        if (!SecurityUtils.getTenantId().equals(contact.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        List<Long> companyIds = accountBusinessAdministratorRepository.findCompanyIdsByUserIdWithAm(SecurityUtils.getUserId());
        if (!SecurityUtils.isAdmin() && (CollUtil.isEmpty(companyIds) || !companyIds.contains(approverDTO.getCompanyId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_NOPERMISSION_CONTACT_AM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(contact.getTalentId()).orElseThrow(() ->
                new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_UPDATESALESLEADCLIENTCONTACT_TALENEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()))
        );
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findByTalentIdAndTypeAndStatusOrderBySortAsc(contact.getTalentId(), ContactType.EMAIL.toDbValue(), AVAILABLE.toDbValue());
        TalentContact talentContact = talentContactList.get(0);
        if (talentContact == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_EMAILNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        Long contactId = approverDTO.getContactId();
        Long approverId = contact.getApproverId();
        String email = talentContact.getContact();
        Long tenantId = contact.getTenantId();
        String password = null;
        if (StrUtil.isNotBlank(approverDTO.getPassword())) {
            password = LoginUtil.validatePassword(approverDTO.getPassword(), applicationProperties.getSecret());
        }
        TimeSheetUser timeSheetUser = null;
        approverVO.setContactId(contactId);
        approverVO.setInactived(approverDTO.getInactived());
        approverVO.setReceived(approverDTO.getReceived());

        boolean add = true;
        if (approverDTO.getInactived()) {
            contact.setReceiveEmail(false);
            contact.setInactived(true);
            salesLeadClientContactRepository.save(contact);
            if (approverId != null) {
                timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.getTimeSheetUserById(contact.getApproverId()).getBody());
                if (timeSheetUser != null && timeSheetUser.getId() != null) {
                    timeSheetUser.setActivated(false);
                    timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser)).getBody());
                    salesLeadClientContactRepository.updateInactivedByApproverIdAndTenantId(true, timeSheetUser.getId(), SecurityUtils.getTenantId());
                    authorityService.logoutByUid(timeSheetUser.getUid());
                }
            }
            return approverVO;
        } else {
            String uid = contactId + StrUtil.UNDERLINE + tenantId + StrUtil.UNDERLINE + TimeSheetUserType.CLIENT.toDbValue();
            if (approverId == null) {
                timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.findByUsernameOrEmail(email).getBody());
                if (StringUtils.isEmpty(password)) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_PASSWORDISEMPTY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
                if (timeSheetUser == null || timeSheetUser.getId() == null) {
                    timeSheetUser = new TimeSheetUser();
                    timeSheetUser.setPassword(passwordEncoder.encode(password));
                    timeSheetUser.setUsername(email);
                    timeSheetUser.setEmail(email);
                    timeSheetUser.setTenantId(tenantId);
                    timeSheetUser.setUserType(TimeSheetUserType.CLIENT);
                    timeSheetUser.setPassChanged(false);
                    timeSheetUser.setUid(uid);

                    timeSheetUser.setRoles(roleRepository.findByName(AuthoritiesConstants.ROLE_APPROVER));
                    timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser)).getBody());
                } else {
                    timeSheetUser.setPassword(passwordEncoder.encode(password));
                    timeSheetUser.setActivated(true);
                    authorityService.logoutByUid(timeSheetUser.getUid());
                    timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser)).getBody());
                    salesLeadClientContactRepository.updateInactivedByApproverIdAndTenantId(false, timeSheetUser.getId(), SecurityUtils.getTenantId());
                    add = false;
                }
            } else {
                timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.getTimeSheetUserById(approverId).getBody());
                Set<String> emailSet = talentContactList.stream().map(TalentContact::getContact).collect(Collectors.toSet());
                if (!emailSet.contains(timeSheetUser.getEmail())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_EMAILINCONSISTENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
                if (StringUtils.isNotEmpty(password)) {
                    timeSheetUser.setPassword(passwordEncoder.encode(password));
                    authorityService.logoutByUid(timeSheetUser.getUid());
                }
                timeSheetUser.setActivated(true);
                timeSheetUser.setUid(uid);
                timeSheetUser = Convert.convert(TimeSheetUser.class, jobdivaClient.saveTimeSheetUser(Convert.convert(TimeSheetUserDTO.class, timeSheetUser)).getBody());
                salesLeadClientContactRepository.updateInactivedByApproverIdAndTenantId(false, timeSheetUser.getId(), SecurityUtils.getTenantId());
                add = false;
            }
            contact.setApproverId(timeSheetUser.getId());
            contact.setReceiveEmail(approverDTO.getReceived());
            contact.setInactived(approverDTO.getInactived());
            salesLeadClientContactRepository.save(contact);
        }

        if (add) {
            sendEmailAdd(email, password, CommonUtils.formatFullName(talentCompanyBrief.getFirstName(), talentCompanyBrief.getLastName()));
        }
        if (!approverDTO.getInactived()) {
            //标记同步hr
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                talentService.updateTalentNeedSyncToHr(contact.getTalentId());
            });
        }
        approverVO.setTimesheetUserId(timeSheetUser.getId());
        return approverVO;
    }

    @Override
    public List<ClientContactDTO> findBriefContactByIdAndReceiveEmail(List<Long> contactIds) {
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllById(contactIds).stream().filter(o -> o.getReceiveEmail() == true).collect(Collectors.toList());
        if (CollUtil.isEmpty(salesLeadClientContactList)) {
            return new ArrayList<>();
        }

        if (!SecurityUtils.isAdmin() && !SecurityUtils.isSystemAdmin() && salesLeadClientContactList.stream().anyMatch(o -> !o.getTenantId().equals(SecurityUtils.getTenantId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_FINDBRIEFCONTACTBYIDANDRECEIVEEMAIL_CONTACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        List<EntityNameVM> talentNameList = talentServiceRepository.findTalentNameByIds(salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).collect(Collectors.toList()));
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndTypeInAndStatusAndVerificationStatusOrderBySortAsc(salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).collect(Collectors.toList()), Arrays.asList(ContactType.EMAIL), TalentContactStatus.AVAILABLE);
        Map<Long, EntityNameVM> entityNameMap = talentNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> o));
        Map<Long, List<TalentContact>> talentContactMap = talentContactList.stream()
                .sorted(Comparator.comparing(TalentContact::getTalentId))
                .collect(Collectors.toMap(
                        TalentContact::getTalentId,
                        Collections::singletonList,
                        (a, b) -> Stream.concat(a.stream(), b.stream()).sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList()),
                        LinkedHashMap::new
                ));
        return salesLeadClientContactList.stream().map(o -> {
            ClientContactDTO clientContactDTO = new ClientContactDTO();
            clientContactDTO.setId(o.getId());
            if (entityNameMap.containsKey(o.getTalentId())) {
                clientContactDTO.setFirstName(entityNameMap.get(o.getTalentId()).getFirstName());
                clientContactDTO.setLastName(entityNameMap.get(o.getTalentId()).getLastName());
                clientContactDTO.setName(CommonUtils.translateName(entityNameMap.get(o.getTalentId()).getFirstName(), entityNameMap.get(o.getTalentId()).getLastName()));
            }
            if (talentContactMap.containsKey(o.getTalentId())) {
                clientContactDTO.setEmail(talentContactMap.get(o.getTalentId()).get(0).getContact());
            }
            return clientContactDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean hasApproverPermission(Long companyId) {
        return isAm(companyId, SecurityUtils.getUserId());
    }

    private boolean isAm(Long companyId, Long amId) {
        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByCompanyIdAndSalesLeadRoleTypeAndUserId(companyId, Arrays.asList(SalesLeadRoleType.ACCOUNT_MANAGER.toDbValue(),SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER.toDbValue()), amId);
        if (CollUtil.isNotEmpty(businessFlowAdministratorList)) {
            return true;
        }
        return false;
    }


    @Override
    public SalesLeadClientContactProfile findSalesLeadClientContactProfile(Long id) {
        log.debug("Request to get findSalesLeadClientContactProfile by Id: {}", id);
        SalesLeadClientContact clientContact = salesLeadClientContactRepository.findById(id).orElseThrow(() -> new NotFoundException("client contact doesn't exist."));
        SalesLeadClientContactProfile profile = Convert.convert(SalesLeadClientContactProfile.class, clientContact);

        TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(clientContact.getTalentId()).orElse(null);
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdAndStatusOrderBySortAsc(clientContact.getTalentId(), TalentContactStatus.AVAILABLE);
        TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationCompanyBriefRepository.findByTalentId(clientContact.getTalentId());
        if (talentCompanyBrief != null) {
            profile.setFirstName(talentCompanyBrief.getFirstName());
            profile.setLastName(talentCompanyBrief.getLastName());
            profile.setName(CommonUtils.translateName(talentCompanyBrief.getFirstName(), talentCompanyBrief.getLastName()));
        }
        if (CollUtil.isNotEmpty(talentContactList)) {
            Optional<TalentContact> email = talentContactList.stream().filter(o -> ContactType.EMAIL.equals(o.getType())).findFirst();
            email.ifPresent(c -> {
                SalesLeadClientContactProfileContactInfoDTO emailDTO = new SalesLeadClientContactProfileContactInfoDTO(c.getContact(), c.getVerificationStatus());
                profile.setEmail(emailDTO);
            });
            Optional<TalentContact> phone = talentContactList.stream().filter(o -> ContactType.PHONE.equals(o.getType())).findFirst();
            phone.ifPresent(c -> {
                SalesLeadClientContactProfileContactInfoDTO phoneDTO = new SalesLeadClientContactProfileContactInfoDTO(c.getContact(), c.getVerificationStatus());
                profile.setPhone(phoneDTO);
            });
            Optional<TalentContact> wechat = talentContactList.stream().filter(o -> ContactType.WECHAT.equals(o.getType())).findFirst();
            wechat.ifPresent(c -> {
                SalesLeadClientContactProfileContactInfoDTO wechatDTO = new SalesLeadClientContactProfileContactInfoDTO(c.getContact(), c.getVerificationStatus());
                profile.setWechat(wechatDTO);
            });
            Optional<TalentContact> linkedinProfile = talentContactList.stream().filter(o -> ContactType.LINKEDIN.equals(o.getType())).findFirst();
            linkedinProfile.ifPresent(c -> {
                SalesLeadClientContactProfileContactInfoDTO linkedinProfileDTO = new SalesLeadClientContactProfileContactInfoDTO(c.getContact(), c.getVerificationStatus());
                profile.setLinkedinProfile(linkedinProfileDTO);
            });
        }

        if (talentCurrentLocationCompanyBrief != null) {
            LocationDTO locationDTO = JSONUtil.toBean(talentCurrentLocationCompanyBrief.getOriginalLoc(), LocationDTO.class);
            profile.setAddress(locationDTO.getAddressLine());
            profile.setCity(locationDTO.getCity());
            profile.setProvince(locationDTO.getProvince());
            profile.setCountry(locationDTO.getCountry());
            profile.setLocation(locationDTO.getLocation());
            profile.setZipcode(locationDTO.getZipcode());
//            profile.setCityId(locationDTO.getCityId());
        }
        return profile;
    }


    @Override
    public List<SalesLeadClientContactVO> findByIdIn(List<Long> ids) {
        log.info("Request to get brief salesLeadClientContactVM by Ids: {}", ids);
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllById(ids);
        if (salesLeadClientContactList.stream().anyMatch(o -> !o.getTenantId().equals(SecurityUtils.getTenantId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_FINDBRIEFCONTACTBYIDANDRECEIVEEMAIL_CONTACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        List<EntityNameVM> talentNameList = talentServiceRepository.findTalentNameByIds(salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).collect(Collectors.toList()));
        List<TalentContact> talentContactList = talentContactCompanyBriefRepository.findAllByTalentIdInAndTypeInAndStatusAndVerificationStatusOrderBySortAsc(salesLeadClientContactList.stream().map(SalesLeadClientContact::getTalentId).collect(Collectors.toList()), Arrays.asList(ContactType.EMAIL), TalentContactStatus.AVAILABLE);
        Map<Long, EntityNameVM> entityNameMap = talentNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> o));
        Map<Long, List<TalentContact>> talentContactMap = talentContactList.stream()
                .sorted(Comparator.comparing(TalentContact::getTalentId))
                .collect(Collectors.toMap(
                        TalentContact::getTalentId,
                        Collections::singletonList,
                        (a, b) -> Stream.concat(a.stream(), b.stream()).sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList()),
                        LinkedHashMap::new
                ));
        return salesLeadClientContactList.stream().map(o -> {
            SalesLeadClientContactVO salesLeadClientContactVO = new SalesLeadClientContactVO();
            salesLeadClientContactVO.setId(o.getId());
            if (entityNameMap.containsKey(o.getTalentId())) {
                salesLeadClientContactVO.setFirstName(entityNameMap.get(o.getTalentId()).getFirstName());
                salesLeadClientContactVO.setLastName(entityNameMap.get(o.getTalentId()).getLastName());
                salesLeadClientContactVO.setName(CommonUtils.translateName(entityNameMap.get(o.getTalentId()).getFirstName(), entityNameMap.get(o.getTalentId()).getLastName()));
            }
            salesLeadClientContactVO.setContacts(talentContactMap.containsKey(o.getTalentId()) ? talentContactMap.get(o.getTalentId()).stream().map(TalentContactDTO::fromTalentContact).collect(Collectors.toList()) : null);
            return salesLeadClientContactVO;
        }).collect(Collectors.toList());
    }


    @Override
    public SalesLeadClientContact findInfoById(Long id) {
        log.debug("Request to get salesLeadClientContact by Id: {}", id);
        return salesLeadClientContactRepository.findById(id).orElse(null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public SalesLeadClientContactProfile updateContactInfo(SalesLeadClientContactProfile profile) {
        SalesLeadClientContact contact = salesLeadClientContactRepository.getById(profile.getId());
        if (ObjectUtil.isEmpty(contact)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_UPDATECONTACTINFO_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        if (!SecurityUtils.getTenantId().equals(contact.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_APPROVER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
//        CompanyLocation companyLocation = null;
//        if (profile.getCompanyLocationId() != null) {
//            companyLocation = companyLocationRepository.findById(profile.getCompanyLocationId()).orElseThrow(() -> new NotFoundException("location does not exits"));
//            if (!companyLocation.getCompanyId().equals(contact.getCompanyId())) {
//                throw new ForbiddenException("location does not exits");
//            }
//        }
        TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(contact.getTalentId()).orElseThrow(() -> new NotFoundException("talent does not exist"));
        TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief = talentCurrentLocationCompanyBriefRepository.findByTalentId(contact.getTalentId());
        if (ObjectUtil.isNotNull(profile.getFirstName())) {
            talentCompanyBrief.setFirstName(profile.getFirstName());
        }
        if (ObjectUtil.isNotNull(profile.getLastName())) {
            talentCompanyBrief.setLastName(profile.getLastName());
        }
        formatName(talentCompanyBrief);
        if (Objects.nonNull(profile.getPhone())) {
            updateTalentContactOnlyPhone(List.of(new TalentContactDTO(ContactType.PHONE, profile.getPhone().getContact(), profile.getPhone().getVerificationStatus(), contact.getTalentId(), contact.getTenantId(), TalentContactStatus.AVAILABLE, 0)), contact.getTalentId());
        }
        if (ObjectUtil.isNotNull(profile.getReceiveEmail())) {
            contact.setReceiveEmail(profile.getReceiveEmail());
        }
        if (ObjectUtil.isNotNull(profile.getZipcode())) {
            contact.setZipcode(profile.getZipcode());
        }

        LocationDTO locationDTO = talentCurrentLocationCompanyBrief != null ? JSONUtil.toBean(talentCurrentLocationCompanyBrief.getOriginalLoc(), LocationDTO.class) : new LocationDTO();
        if (talentCurrentLocationCompanyBrief == null) {
            talentCurrentLocationCompanyBrief = new TalentCurrentLocationCompanyBrief();
            talentCurrentLocationCompanyBrief.setTalentId(contact.getTalentId());
        }
        if (!Objects.equals(locationDTO.getCountry(), profile.getCountry()) || !Objects.equals(locationDTO.getProvince(), profile.getProvince()) || !Objects.equals(locationDTO.getCity(), profile.getCity())
                || !Objects.equals(locationDTO.getAddressLine(), profile.getAddress()) || !Objects.equals(locationDTO.getLocation(), profile.getLocation())) {
            CompanyLocation companyLocation = checkNewCompanyLocation(contact.getCompanyId(), profile.getCountry(), profile.getProvince(), profile.getCity(), profile.getAddress(), profile.getLocation());
            if (companyLocation == null) {
                LocationDTO originalLocDto = new LocationDTO(profile.getCity(), profile.getProvince(), profile.getCountry());
                originalLocDto.setAddressLine(profile.getAddress());
                originalLocDto.setLocation(profile.getLocation());
                companyLocation = new CompanyLocation(contact.getCompanyId(), JsonUtil.toJson(originalLocDto));
                CompanyLocation createCompanyLocation = companyLocationRepository.save(companyLocation);
                talentCurrentLocationCompanyBrief.setOriginalLoc(createCompanyLocation.getOriginalLoc());
            } else {
                talentCurrentLocationCompanyBrief.setOriginalLoc(companyLocation.getOriginalLoc());
            }
        }
        salesLeadClientContactRepository.save(contact);
        talentCompanyBriefRepository.save(talentCompanyBrief);
        if (talentCurrentLocationCompanyBrief.getOriginalLoc() != null) {
            talentCurrentLocationCompanyBriefRepository.save(talentCurrentLocationCompanyBrief);
        }
        return profile;
    }

    private CompanyLocation checkNewCompanyLocation(Long companyId, String country, String province, String city, String address, String location) {
        if (companyId == null) {
            return null;
        }

        List<CompanyLocation> companyLocationList = companyLocationRepository.findAllByAccountCompanyId(companyId);
        Optional<CompanyLocation> companyLocation = companyLocationList.stream().filter(o -> {
            if (o.getOriginalLoc() != null) {
                LocationDTO locationDTO = JSONUtil.toBean(o.getOriginalLoc(), LocationDTO.class);
                return Objects.equals(locationDTO.getCountry(), country) && Objects.equals(locationDTO.getProvince(), province) && Objects.equals(locationDTO.getCity(), city) && Objects.equals(locationDTO.getAddressLine(), address) && Objects.equals(locationDTO.getLocation(), location);
            }
            return false;
        }).findFirst();

        return companyLocation.orElse(null);
    }

    @Override
    public ClientContactBriefInfoDTO findClientContactBriefById(Long companyId, Long clientContactId) {
        return salesLeadClientContactRepository.findBrieClientContactById(companyId, clientContactId).orElse(null);
    }

    @Override
    public Long queryContactLocationIdByTalentId(Long talentId) {
        return 1L;
//        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllByTalentIdAndActive(talentId, Boolean.TRUE);
//        // Compatible with historical data, which may contain contacts who work in multiple companies.
//        if (clientContactList.size() > 1 && clientContactList.stream().map(SalesLeadClientContact::getCompanyId).distinct().count() > 1) {
//            TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(talentId).orElse(null);
//            if (talentCompanyBrief != null && talentCompanyBrief.getTalentAdditionalInfo() != null) {
//                JSONObject additionalInfoJson = JSONUtil.parseObj(talentCompanyBrief.getTalentAdditionalInfo().getExtendedInfo());
//                if (additionalInfoJson.containsKey(KEY_EXPERIENCES)) {
//                    List<TalentExperienceDTO> talentExperienceDTOList = JSONUtil.toList(JSONUtil.parseArray(additionalInfoJson.get(KEY_EXPERIENCES)), TalentExperienceDTO.class);
//                    Optional<Long> companyId = talentExperienceDTOList.stream().map(TalentExperienceDTO::getActiveCompanyId).filter(Objects::nonNull).findFirst();
//                    if (companyId.isPresent()) {
//                        Optional<SalesLeadClientContact> salesLeadClientContact = clientContactList.stream().filter(o -> o.getCompanyId().equals(companyId.get())).findFirst();
//                        if (salesLeadClientContact.isPresent()) {
//                            return salesLeadClientContact.get().getCompanyLocationId();
//                        }
//                    }
//                }
//            }
//        }
//
//        return clientContactList.stream().findFirst().map(SalesLeadClientContact::getCompanyLocationId).orElse(null);
    }

    @Override
    public List<SalesLeadClientContactVO> queryAllContactByTalentId(Long talentId) {
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllByTenantIdAndTalentId(SecurityUtils.getTenantId(), talentId);
        return salesLeadClientContactList.stream().map(SalesLeadClientContactVO::fromSalesLeadClientContact).toList();
    }

    @Override
    public List<SalesLeadClientContactVO> findAllContactByCompany(List<Long> companyIds) {
        if (CollUtil.isEmpty(companyIds)) {
            return new ArrayList<>();
        }
        List<CompanyContactVM> salesLeadClientContactList = salesLeadClientContactRepository.findAllByTenantIdAndCompanyIdIn(SecurityUtils.getTenantId(), companyIds);
        return salesLeadClientContactList.stream().map(SalesLeadClientContactVO::fromCompanyContactVM).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContactLocationIdByTalentId(Long talentId, Long locationId) {
//        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllByTalentIdAndActive(talentId, Boolean.TRUE);
//        CompanyLocation companyLocation = companyLocationRepository.findById(locationId).orElse(null);
//        if (companyLocation != null) {
//            clientContactList.forEach(o -> {
//                if (o.getCompanyId().equals(companyLocation.getCompanyId())) {
////                    o.setCompanyLocationId(companyLocation.getId());
//                    salesLeadClientContactRepository.updateContactLocationIdById(companyLocation.getId(), o.getId());
//                }
//            });
////            salesLeadClientContactRepository.saveAll(clientContactList);
//        }

    }

    private boolean compareCompanyLocationWithTalentLocation(CompanyLocation companyLocation, TalentCurrentLocationCompanyBrief talentCurrentLocationCompanyBrief) {
        return Objects.equals(companyLocation.getOfficialCountry(), talentCurrentLocationCompanyBrief.getOfficialCountry()) &&
                Objects.equals(companyLocation.getOfficialCounty(), talentCurrentLocationCompanyBrief.getOfficialCounty()) &&
                Objects.equals(companyLocation.getOfficialProvince(), talentCurrentLocationCompanyBrief.getOfficialProvince()) &&
                Objects.equals(companyLocation.getOfficialCity(), talentCurrentLocationCompanyBrief.getOfficialCity()) &&
                Objects.equals(companyLocation.getOriginalLoc(), talentCurrentLocationCompanyBrief.getOriginalLoc());
    }

    private Company checkCompanyPermission(Long id) {
        Company company = companyRepository.findById(id).orElseThrow(() -> new NotFoundException("company does not exists."));
        if (!company.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_CHECKCOMPANYPERMISSION_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        return company;
    }

    private SalesLeadClientContact checkClientContactPermission(Long id) {
        SalesLeadClientContact salesLeadClientContact = salesLeadClientContactRepository.findById(id).orElseThrow(() -> new NotFoundException("company contact does not exists."));
        if (!SecurityUtils.getTenantId().equals(salesLeadClientContact.getTenantId())) {
            throw new ForbiddenException("company contact does not exists.");
        }
        if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()) {
            return salesLeadClientContact;
        }
        if (!SecurityUtils.getUserId().equals(salesLeadClientContact.getPermissionUserId()) && !isAm(salesLeadClientContact.getCompanyId(), SecurityUtils.getUserId())) {
            throw new ForbiddenException("company contact does not exists.");
        }
        return salesLeadClientContact;
    }

    private void checkCompanyLocation(Long locationId, Long companyId) {
        CompanyLocation companyLocation = companyLocationRepository.findById(locationId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_CHECKCOMPANYLOCATION_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService())));
        if (!companyLocation.getAccountCompanyId().equals(companyId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_CHECKCOMPANYLOCATION_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
    }

    private List<Long> checkTalentContactInformation(List<TalentContactDTO> contacts, List<String> linkedin, Long tenantId) {
        if (CollUtil.isEmpty(contacts)) {
            return new ArrayList<>();
        }
        List<TalentContactDTO> contactDTOList = new ArrayList<>(contacts);
        if (CollUtil.isNotEmpty(linkedin)) {
            linkedin.forEach(item -> {
                if (getLinkedinContact(item) != null) {
                    contactDTOList.add(new TalentContactDTO(ContactType.LINKEDIN, getLinkedinContact(item)));
                }
            });
        }

        if (CollUtil.isEmpty(contactDTOList)) {
            return new ArrayList<>();
        }
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        talentDTOV3.setTenantId(tenantId);
        talentDTOV3.setContacts(contactDTOList);
        return talentService.searchTalentsIdByContactAndSimilarity(talentDTOV3).getBody();
    }

    private List<TalentContactDTO> mergeLinkedinContact(List<TalentContactDTO> contacts, List<String> linkedin) {
        List<TalentContactDTO> contactDTOList = contacts == null ? new ArrayList<>() : new ArrayList<>(contacts);
        if (CollUtil.isNotEmpty(linkedin)) {
            linkedin.forEach(item -> {
                if (getLinkedinContact(item) != null) {
                    contactDTOList.add(new TalentContactDTO(ContactType.LINKEDIN, getLinkedinContact(item), item));
                }
            });
        }
        return contactDTOList;
    }

    private void checkDuplicationContactByCompanyIdAndTalentId(Long companyId, Long talentId, Long contactId, Boolean contactStatus) {
        if (!Boolean.TRUE.equals(contactStatus)) {
            return;
        }
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findAllByCompanyIdAndTalentIdAndActive(companyId, talentId, true);
        if (CollUtil.isNotEmpty(salesLeadClientContactList) && contactId == null ? salesLeadClientContactList.stream().map(SalesLeadClientContact::getId).findAny().isPresent() : salesLeadClientContactList.stream().map(SalesLeadClientContact::getId).anyMatch(o -> !o.equals(contactId))) {
            TalentCompanyBrief talentCompanyBrief = talentCompanyBriefRepository.findById(talentId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_CHECKDUPLICATIONCONTACTBYCOMPANYIDANDTALENTID_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService())));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SALESLEAD_CHECKDUPLICATIONCONTACTBYCOMPANYIDANDTALENTID_CONTACTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(CommonUtils.formatFullName(talentCompanyBrief.getFirstName(), talentCompanyBrief.getLastName())), companyApiPromptProperties.getCompanyService()));
        }
    }

    private void addApproverTagToContact(List<TalentContactDTO> contactList) {
        Set<String> emailSet = contactList.stream().filter(t -> t.getType().equals(ContactType.EMAIL)).map(TalentContactDTO::getContact).collect(Collectors.toSet());
        List<TimeSheetUserDTO> timeSheetUserList = jobdivaClient.findByUsernameOrEmailList(emailSet).getBody();
        if (CollUtil.isNotEmpty(timeSheetUserList)) {
            Set<String> existsEmails = new HashSet<>();
            timeSheetUserList.forEach(t -> {
                existsEmails.add(t.getUsername());
                existsEmails.add(t.getEmail());
            });
            contactList.stream().filter(t -> t.getType().equals(ContactType.EMAIL) && existsEmails.contains(t.getContact())).forEach(t -> t.setApproverEmail(true));
        }
    }


    @Override
    public List<TalentClientContactStatusDTO> getTalentClientContactStatus(List<Long> talentIds) {
        List<TalentClientContactStatusDTO> talentStatus = salesLeadClientContactRepository.findClientContactStatusByTalentIdInAndActive(talentIds, true);
        Set<Long> contactSet = talentStatus.stream().map(TalentClientContactStatusDTO::getTalentId).collect(Collectors.toSet());
        talentIds.forEach(talentId -> {
            if (!contactSet.contains(talentId)) {
                talentStatus.add(new TalentClientContactStatusDTO(talentId, false));
            }
        });
        return talentStatus;
    }

    @Override
    public List<TalentClientContactRelationDTO> getTalentClientContactStatusByContactIds(Set<Long> contactIds) {
        List<TalentClientContactRelationDTO> relation = salesLeadClientContactRepository.findTalentIdsByClientContactIds(contactIds, true);
        return relation;
    }

    @Override
    public List<TalentClientContactRelationDTO> getTalentClientContactStatusByTalentIds(Set<Long> talentIds) {
        List<TalentClientContactRelationDTO> relation = salesLeadClientContactRepository.findContactIdsByTalentIds(talentIds, true);
        return relation;
    }

    /**
     * 查询很少被调用，所以暂未改成异步
     * @param crmContactDTO
     * @return
     */
    @Override
    public CompanyContactCheckDataExistVO contactCheckDataExist(CrmContactCheckDataExistDTO crmContactDTO) {
        CompanyContactCheckDataExistVO result = new CompanyContactCheckDataExistVO();
        List<SalesLeadClientContact> salesLeadClientContacts = salesLeadClientContactRepository.findByCrmContactIdAndTenantId(crmContactDTO.getContactId(), crmContactDTO.getTenantId());
        if (null == salesLeadClientContacts || salesLeadClientContacts.isEmpty()) {
            return result;
        }
        Long id = salesLeadClientContacts.get(0).getId();

        //查询打卡审批人
        List<Long> tmContact = salesLeadClientContactRepository.findTimesheetManagerByContactId(crmContactDTO.getContactId(), crmContactDTO.getTenantId(),crmContactDTO.getCompanyId());
        if (!tmContact.isEmpty() && !tmContact.stream().allMatch(Objects::isNull)) {
            result.setTimesheetContact(true);
        }

        //查询job联系人
        List<Long> jobContact = salesLeadClientContactRepository.findJobContactByContactId(id, crmContactDTO.getTenantId());
        if (!jobContact.isEmpty()) {
            result.setJobContact(true);
        }

        //查询流程联系人
        List<Long> processContact = salesLeadClientContactRepository.findProcessContactByContactId(id, crmContactDTO.getTenantId());
        if (!processContact.isEmpty()) {
            result.setProcessContact(true);
        }

        //查询发票联系人
        List<Long> invoiceContact = salesLeadClientContactRepository.findInvoiceContactByContactId(id, crmContactDTO.getTenantId());
        if (!invoiceContact.isEmpty()) {
            result.setInvoiceContact(true);
        }

        //查询assignment联系人
        List<Long> assignmentContact = salesLeadClientContactRepository.findAssignmentContactByContactId(id);
        if (!assignmentContact.isEmpty()) {
            result.setAssignmentContact(true);
        }

        return result;
    }
}